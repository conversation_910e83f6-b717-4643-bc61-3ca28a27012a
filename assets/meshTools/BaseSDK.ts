

export class BaseSDK
{
    public AddAPPEvent(): void {}

    public HideLoading(): void {}

    public CloseWebView(): void {}

    public ShowAppShop(type?: number): void {}

    public GetAppVersion(callback: Action1<string>, errCallback?: Action0): void 
    {
        callback && callback("0.0.0");
    }

    public GetConfig(callback: Action1<any>): void 
    {
        callback && callback(null);
    }

   

}

export interface Action0
{
    (): void;
}

export interface Action1<T>
{
    (p: T): void;
}