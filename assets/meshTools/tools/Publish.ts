/**
 * mesh游戏发布配置管理类
 */

import { Singleton } from "../Singleton";


export class Publish extends Singleton
{
    // 0 开发 1 测试 2 正式
    private miniGameEnv: number = 1;
    //版本号
    private miniGameVersion: string = "1.1.8";
    //游戏id
    private gameId: number = 2059;  

    // private gameId: number = 2040;  


    // ※※※※※ 下面这些个参数是由 app 端传进来的 接收到之后进行赋值※※※※※※
    public appChannel:string //商户渠道
    public appId:number //商户 id
    public userId:string //用户 userId
    public code:string //商户生成的认证令牌
    public roomId:string //房间 ID（不存在，可以传空）
    public gameMode:string //游戏场景   2:半屏（秀场）    3: 全屏（游戏场）
    public language:string //语言类型，默认为英文（详情见 表 3。多语言对照表）
    public gsp:number=8001 //游戏正式服务器节点 101:新加坡（阿里云）  201:迪拜（AWS）301:硅谷 (阿里云)  401:法兰克福(阿里云） 8001:soofun（阿里云）
    // 下面这俩参数归属于 gameConfig对象下面 
    public sceneMode:number  //场馆级别，默认为 0  0:场馆列表 1:初级场 2:中级场 3:高级场  这个游戏此参数用不到
    public currencyIcon:string //货币图标


     //区分上面的参数是不是 url 传进来的，正常是用 js 传进来，个别渠道是通过 url 传参的
     public isDataByURL = false;


 
    public getVersion (): string
    {
        return this.miniGameVersion;
    }

    public getEnv (): number
    {
        return this.miniGameEnv;
    }

    public getGameId(): number
    {
        return this.gameId;
    }
}