import { MeshTools } from "../MeshTools";
import { BaseSDK } from "../BaseSDK";
import { AutoMessageBean, AutoMessageId } from "../../scripts/net/MessageBaseBean";
import { GameMgr } from "../../scripts/common/GameMgr";
import { EventType } from "../../scripts/common/EventCenter";

var MeshSdk = require("MeshSdk");
// var ZegoGameClient = ZG.ZegoGameClient;

export default class MeshSdkApi extends BaseSDK
{
    private _meshSdk: any = MeshSdk.meshSDK; // new ZegoGameClient();
    private _isChangeVoluming: boolean = false;
    public IsNotifyGameLoaded: boolean = false;
    public IsNotifyGameLoading: boolean = false;
    
    // 监听 APP 事件  APP -> H5
    public AddAPPEvent (): void
    {
        // regReceiveMessage  注册 APP 回调到 H5 的函数
        this._meshSdk.regReceiveMessage({
            // 余额变更
            walletUpdate: function () 
            {
                console.log("walletUpdate");
                let autoMessageBean: AutoMessageBean = {
                    'msgId': AutoMessageId.WalletUpdateMsg,//更新金豆余额的通知
                    'data': {}
                }
                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
            },
            
            // code 变更回调
            serverCodeUpdate: function (data: any)
            {
                data.code = encodeURIComponent(data.code);
            
                let autoMessageBean: AutoMessageBean = {
                    'msgId': AutoMessageId.ServerCodeUpdateMsg,//更新 code 的通知
                    'data': {code: data.code}
                }
                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
            }
        });
    }

    // H5 调用APP 获取APP 版本号
    public GetAppVersion(callback: Function, errCallback: Function): void 
    {
        callback && callback("1.0.0");
    }
  
    // H5 调用 APP  获取用户信息数据
    public GetConfig (callback: (data: any) => void): void
    {
        if (MeshTools.Publish.isDataByURL) {
            callback({
                "gameConfig": {
                    "currencyIcon": "",
                    "sceneMode": 0
                },
                "code": MeshTools.Publish.code,
                "appId":  MeshTools.Publish.appId,
                "language":  MeshTools.Publish.language,
                "gameMode":  MeshTools.Publish.gameMode,
                "userId":  MeshTools.Publish.userId,
                "roomId":  MeshTools.Publish.roomId,
                "appChannel":  MeshTools.Publish.appChannel,
                "gsp":  MeshTools.Publish.gsp
            });
            return;
        }

        if (CC_DEBUG) {
            callback({
                "gameConfig": {
                    "currencyIcon": "https://game-center-test.jieyou.shop/static/images/index/game_bean.png", // 货币图标地址
                    "sceneMode": 0 
                },
                "code": "qFwaAVKyEYTmPey1vWA4Huq7bvto4xexT0UJRnh03vlwTghRwFyVsbO4JRLV", 
                "appId": 66666666,
                "language": "2",
                "gameMode": 3,
                "userId": new Date(),
                "roomId": "room01",
                "appChannel": "debug",
                "gsp":8001
            });
            return;
        }

        this._meshSdk.getConfig().then(callback).catch((err: any) =>
        {
            cc.error(err);
        });
    }

    // 通知APP 游戏资源加载完了
    public HideLoading (): void
    {
        if (this.IsNotifyGameLoaded == true)
        {
            return;
        }
        this.IsNotifyGameLoaded = true;
        this._meshSdk.gameLoaded();
    }

    // 销毁 WebView
    public CloseWebView (): void
    {   
        this._meshSdk.destroy();
    }

    // 余额不足  H5 调用 APP
    public ShowAppShop (type?: number): void
    {
        cc.log("余额不足回调");
        this._meshSdk.gameRecharge();
    }
}

declare var require: any;