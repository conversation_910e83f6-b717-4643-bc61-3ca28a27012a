export const language = {
    //這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: ' 退出將無法返回遊戲。',

    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',

    upSeat: '上座',
    downSeat: '下座',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join: '進入', //加入
    create: '創建', //创建
    auto: '匹配',
    Room: '房間',
    room_number: '房號', //房间号
    copy: '複製', //复制
    game_amount: '遊戲費用', //游戏费用
    player_numbers: '玩家數量:', //玩家数量
    room_exist: '房間不存在',//房间不存在
    enter_room_number: '輸入房間號',//输入房间号
    free: '免費',
    players: '玩家', //玩家
    Player: '玩家',
    Tickets: '門票',


    // 雙方都未部署
    twonotready: "拖拽放置戰艦。\n點擊旋轉戰艦。",

    // 我部署了，對方沒部署
    uarenotready: "您已提交戰艦部署。\n等待對手中...",

    // 對方部署了，我沒部署
    imnotready: "對手已提交戰艦部署。\n等待您的部署...",

    // 雙方都部署了
    weareallready: "雙方都已準備就緒！\n遊戲即將開始...",

    mystartturn: "所有船隻準備就緒，輪到你了。",

    youstartturn: "所有船隻準備就緒，輪到對手了。",

    youturn: "你未命中。輪到對手了。",



    netdisconnected: "網路中斷連線。正在重新連線...",

    netreconnectd: "網路已重新連線。正在恢復遊戲狀態...",

    ituturn: "輪到你了！",

    itoturn: "輪到對手了！",

    usunkship: "你擊沉了一艘敵艦！",

    uhitship: "你擊中了一艘船！",

    umiss: "你未命中！",

    osunkship: "對手擊沉了你的一艘船！",

    ohitship: "對手擊中了你的一艘船！",

    omiss: "對手未命中！",

    ready: "準備",

    random: "隨機",

    fbuy: "是否要花費",

    lbuy: "進行購買？",

    morenbuy: "是否要花費    300 購買？",

    Buy: "購買",

    cancelbot:"託管",

    // 遊戲規則
    GameRules_GameIntroduction: "遊戲介紹",
    GameRules_ChessboardPattern: "棋盤模式",
    GameRules_BattleshipLayout: "戰艦佈局",
    GameRules_GameStart: "遊戲開始",
    GameRules_BattleshipArea: "戰艦區域",
    GameRules_HitTarget: "命中目標",
    GameRules_WinGame: "獲勝條件",

    // 遊戲介紹
    GameRules_IntroText: "海戰棋是一款雙人棋盤遊戲。每位玩家都有一個10X10的棋盤。玩家通過點擊對手棋盤上的格子來開火。第一個摧毀對手所有戰艦的玩家獲勝。",
    GameRules_ImageHengxian: "hengxian", // 圖預製體標記

    // 棋盤模式
    GameRules_ChessboardSize: "棋盤大小為10X10",
    GameRules_WarshipSpecs: "每方有6艘不同大小的戰艦。規格為：1X3; 2X5; 1X5; 1X2; 1X2; 1X4;",
    GameRules_ImageBg1: "bg1", // 圖預製體標記
    GameRules_ImageHengxian2: "hengxian", // 圖預製體標記

    // 戰艦佈局
    GameRules_LayoutTime: "開始前，雙方有30秒時間佈置戰艦。",
    GameRules_RotateShip: "點擊戰艦可以調整戰艦方向（順時針旋轉），拖拽可以調整戰艦位置，但戰艦不能重疊。",
    GameRules_ImageBg2: "bg2", // 圖預製體標記
    GameRules_ImageHengxian3: "hengxian", // 圖預製體標記

    // 遊戲開始
    GameRules_RandomFirstPlayer: "遊戲正式開始後，系統隨機選擇先手玩家。",
    GameRules_ImageHengxian4: "hengxian", // 圖預製體標記

    // 戰艦區域
    GameRules_BattleshipAreaText: "遊戲過程中，雙方可以看到兩個網格，一大一小。大網格是對手戰艦所在的區域。遊戲過程中，玩家可以點擊大網格中的格子來開火。",
    GameRules_ImageHengxian5: "hengxian", // 圖預製體標記

    // 命中目標
    GameRules_HitTargetText: "如果成功命中目標，將出現紅色目標圖標，可以進行連續攻擊。如果未命中目標，將出現灰色\"X\"，輪到對手。",
    GameRules_ImageBg3: "bg3", // 圖預製體標記
    GameRules_SmallGridText: "小網格是您自己的戰艦佈局。您可以看到您的戰艦受損情況。",
    GameRules_ImageBg4: "bg4", // 圖預製體標記
    GameRules_ImageHengxian6: "hengxian", // 圖預製體標記

    // 獲勝條件
    GameRules_WinCondition: "你的目標是擊沉所有敵方戰艦。",
    GameRules_FirstToWin: "第一個擊沉所有敵方戰艦的玩家獲勝！"
};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = language;