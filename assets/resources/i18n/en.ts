export const language = {
//这部分是通用的
    kickout1 : 'You have been asked to leave the room',//您被请出房间
    LeaveRoom : 'The room is dissolved',//房间已解散
    InsufficientBalance : 'The current balance is insufficient, please go to purchase',//余额不足
    GameRouteNotFound : 'Game route not found',//游戏线路异常
    NetworkError : 'network error',//网络异常
    RoomIsFull : 'Room is full',//房间已满
    EnterRoomNumber : 'Enter room number',//输入房间号
    GetUserInfoFailed : 'get user info failed',//获取用户信息失败
    RoomDoesNotExist : 'Room does not exist',//房间不存在
    FailedToDeductGoldCoins : 'Failed to deduct gold coins',//扣除金币失败
    ExitApplication : 'Are you sure to leave?',//完全退出游戏
    QuitTheGame: 'Quitting will prevent you from returning to the game.',

    NotEnoughPlayers : 'Not enough players',//玩家数量不足
    TheGameIsFullOfPlayers : 'The game is full of players',//玩家数量已满
    kickout2: 'Whether to kick {0} out of the room?',//踢出玩家文案

    upSeat:'Join', //上座
    downSeat:'Leave', //下座
    startGame:'Start', //开始游戏
    readyGame:'Ready', //准备
    cancelGame:'Cancel', //取消准备
    cancel:'Cancel', 
    confirm:'Confirm', 
    kickout3:'Kick Out', 
    back:'Back',//返回
    leave:'Leave', //退出
    music:'Music',  //音乐
    sound:'Sound', //音效
    join:'Join', //加入
    create:'Create', //创建
    auto:'Auto',
    Room:'Room',
    room_number:'Room Number', //房间号
    copy:'Copy', //复制
    game_amount:'Game Amount', //游戏费用
    player_numbers:'Player Numbers:', //玩家数量
    room_exist:'Room doesn’t exist',//房间不存在
    enter_room_number:'Enter room number',//输入房间号
    free:'Free',
    players:'Players', //玩家
    Player:'Player',
    Tickets:'tickets',

    // 双方都未部署
   twonotready: "Drag & drop to place ships.\nTap to rotate a ship.",

   // 我部署了，对方没部署
  uarenotready:"You submitted your ships.\nWaiting for opponent...",

   // 对方部署了，我没部署
  imnotready:"Opponent submitted their ships.\nWaiting for you...",

   // 双方都部署了
   weareallready:"Both players are ready!\nGame will start soon...",

   mystartturn:"All ship prepared,it’s your turn.",

   youstartturn:"All ship prepared,it’s opponent‘s turn.",

   youturn:"You missed.It's opponent‘s turn.",



   netdisconnected:"Network disconnected. Reconnecting...",

   netreconnectd:"Network reconnected. Restoring game state...",

   ituturn:"It's your turn!",

   itoturn:"It's opponent's turn!",

   usunkship:"You sunk an enemy ship!",

   uhitship:"You hit a ship!",
   
    umiss:"You missed!",

    osunkship:"Opponent sunk your ship!",

    ohitship:"Opponent hit your ship!",

    omiss:"Opponent missed!",

    ready:"Ready",

    random:"Random",

    fbuy:"Whether spend",
    
    lbuy:" to buy?",
    
    morenbuy:"Whether spend    300 to buy?",

    Buy: "Buy",

    cancelbot:"Cancel Hosting",

    // Game Rules
    GameRules_GameIntroduction: "Game Introduction",
    GameRules_ChessboardPattern: "Chessboard Pattern",
    GameRules_BattleshipLayout: "Battleship Layout",
    GameRules_GameStart: "Game Start",
    GameRules_BattleshipArea: "Battleship Area",
    GameRules_HitTarget: "Hit A Target",
    GameRules_WinGame: "Win the Game",

    // Game Introduction
    GameRules_IntroText: "Battleship Chess is a two-player board game. Each player has a 10X10 board. Players fire by clicking on the grids on the opponent's board. The first player to destroy all the opponent's warships wins.",
    GameRules_ImageHengxian: "hengxian", // Image prefab marker

    // Chessboard Pattern
    GameRules_ChessboardSize: "The chessboard size is 10X10",
    GameRules_WarshipSpecs: "Each side has 6 warships of different sizes. The specifications are: 1X3; 2X5; 1X5; 1X2; 1X2; 1X4;",
    GameRules_ImageBg1: "bg1", // Image prefab marker
    GameRules_ImageHengxian2: "hengxian", // Image prefab marker

    // Battleship Layout
    GameRules_LayoutTime: "Before the start, both sides have 30 seconds to layout their warships.",
    GameRules_RotateShip: "By clicking on the battleship, you can adjust the orientation of the battleship (rotate clockwise), and by dragging, you can adjust the position of the battleship, but the battleships cannot overlap.",
    GameRules_ImageBg2: "bg2", // Image prefab marker
    GameRules_ImageHengxian3: "hengxian", // Image prefab marker

    // Game Start
    GameRules_RandomFirstPlayer: "After the game officially starts, the system randomly selects a first player.",
    GameRules_ImageHengxian4: "hengxian", // Image prefab marker

    // Battleship Area
    GameRules_BattleshipAreaText: "During the game, both parties can see two grids, one large and one small. The large grid is the area where the opponent's warships are located. During the game, players can click on the grid in the large grid to fire.",
    GameRules_ImageHengxian5: "hengxian", // Image prefab marker

    // Hit A Target
    GameRules_HitTargetText: "If the target is successfully hit, a red target icon will appear and continuous attacks can be carried out. If the target is not hit, a gray \"X\" will appear and it is the opponent's turn.",
    GameRules_ImageBg3: "bg3", // Image prefab marker
    GameRules_SmallGridText: "The small grid is your own battleship layout. You can see the damage of your battleships.",
    GameRules_ImageBg4: "bg4", // Image prefab marker
    GameRules_ImageHengxian6: "hengxian", // Image prefab marker

    // Win the Game
    GameRules_WinCondition: "Your goal is to sink all enemy warships.",
    GameRules_FirstToWin: "The first player to sink all enemy warships wins!"

};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.en = language;