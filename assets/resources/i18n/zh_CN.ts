export const language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出将无法返回到游戏。',

    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案

    upSeat: '上座',
    downSeat: '下座',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',//返回
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入', //加入
    create: '创建', //创建
    auto: '匹配',
    Room: '房间',
    room_number: '房间号', //房间号
    copy: '复制', //复制
    game_amount: '游戏费用', //游戏费用
    player_numbers: '玩家数量:', //玩家数量
    room_exist: '房间不存在',//房间不存在
    enter_room_number: '输入房间号',//输入房间号
    free: '免费',
    players: '玩家', //玩家
    Player: '玩家',
    Tickets: '门票',


    // 双方都未部署
    twonotready: "拖拽放置战舰。\n点击旋转战舰。",

    // 我部署了，对方没部署
    uarenotready: "您已提交战舰部署。\n等待对手中...",

    // 对方部署了，我没部署
    imnotready: "对手已提交战舰部署。\n等待您的部署...",

    // 双方都部署了
    weareallready: "双方都已准备就绪！\n游戏即将开始...",

    mystartturn: "所有船只准备就绪，轮到你了。",

    youstartturn: "所有船只准备就绪，轮到对手了。",

    youturn: "你未命中。轮到对手了。",



    netdisconnected: "网络断开连接。正在重新连接...",

    netreconnectd: "网络已重新连接。正在恢复游戏状态...",

    ituturn: "轮到你了！",

    itoturn: "轮到对手了！",

    usunkship: "你击沉了一艘敌舰！",

    uhitship: "你击中了一艘船！",

    umiss: "你未命中！",

    osunkship: "对手击沉了你的一艘船！",

    ohitship: "对手击中了你的一艘船！",

    omiss: "对手未命中！",

    ready: "准备",

    random: "随机",

    fbuy: "是否要花费",

    lbuy: "进行购买？",

    morenbuy: "是否要花费    300 购买？",

    Buy: "购买",

    cancelbot:"取消托管",

    // 游戏规则
    GameRules_GameIntroduction: "游戏介绍",
    GameRules_ChessboardPattern: "棋盘模式",
    GameRules_BattleshipLayout: "战舰布局",
    GameRules_GameStart: "游戏开始",
    GameRules_BattleshipArea: "战舰区域",
    GameRules_HitTarget: "命中目标",
    GameRules_WinGame: "获胜条件",

    // 游戏介绍
    GameRules_IntroText: "海战棋是一款双人棋盘游戏。每位玩家都有一个10X10的棋盘。玩家通过点击对手棋盘上的格子来开火。第一个摧毁对手所有战舰的玩家获胜。",
    GameRules_ImageHengxian: "hengxian", // 图预制体标记

    // 棋盘模式
    GameRules_ChessboardSize: "棋盘大小为10X10",
    GameRules_WarshipSpecs: "每方有6艘不同大小的战舰。规格为：1X3; 2X5; 1X5; 1X2; 1X2; 1X4;",
    GameRules_ImageBg1: "bg1", // 图预制体标记
    GameRules_ImageHengxian2: "hengxian", // 图预制体标记

    // 战舰布局
    GameRules_LayoutTime: "开始前，双方有30秒时间布置战舰。",
    GameRules_RotateShip: "点击战舰可以调整战舰方向（顺时针旋转），拖拽可以调整战舰位置，但战舰不能重叠。",
    GameRules_ImageBg2: "bg2", // 图预制体标记
    GameRules_ImageHengxian3: "hengxian", // 图预制体标记

    // 游戏开始
    GameRules_RandomFirstPlayer: "游戏正式开始后，系统随机选择先手玩家。",
    GameRules_ImageHengxian4: "hengxian", // 图预制体标记

    // 战舰区域
    GameRules_BattleshipAreaText: "游戏过程中，双方可以看到两个网格，一大一小。大网格是对手战舰所在的区域。游戏过程中，玩家可以点击大网格中的格子来开火。",
    GameRules_ImageHengxian5: "hengxian", // 图预制体标记

    // 命中目标
    GameRules_HitTargetText: "如果成功命中目标，将出现红色目标图标，可以进行连续攻击。如果未命中目标，将出现灰色\"X\"，轮到对手。",
    GameRules_ImageBg3: "bg3", // 图预制体标记
    GameRules_SmallGridText: "小网格是您自己的战舰布局。您可以看到您的战舰受损情况。",
    GameRules_ImageBg4: "bg4", // 图预制体标记
    GameRules_ImageHengxian6: "hengxian", // 图预制体标记

    // 获胜条件
    GameRules_WinCondition: "你的目标是击沉所有敌方战舰。",
    GameRules_FirstToWin: "第一个击沉所有敌方战舰的玩家获胜！"

};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = language;