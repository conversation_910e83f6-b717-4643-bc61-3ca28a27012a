// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { InviteUser, RoomUser } from "../bean/GameBean";
import NickNameLabel from "../util/NickNameLabel";
import { Tools } from "../util/Tools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SeatItemController extends cc.Component {

    @property(cc.Node)
    avatarFrame: cc.Node = null;
    @property(cc.Node)
    avatar: cc.Node = null;
    @property(cc.Node)
    seatEmpty: cc.Node = null;
    @property(NickNameLabel)
    nameLabel: NickNameLabel = null;
    @property(cc.Node)
    ready: cc.Node = null;

    users: InviteUser

    // onLoad () {}

    start () {

    }

    setData(users: InviteUser) {
        this.users = users
        if (users == null) {
            this.nameLabel.string = ''
            this.seatEmpty.active = true;
            this.avatarFrame.active = false;
            this.avatar.active = false;
            this.ready.active = false;
        } else {
            this.nameLabel.string = users.nickname
            this.seatEmpty.active = false;
            this.avatarFrame.active = true;
            this.avatar.active = true;
            this.ready.active = users.ready
            
            Tools.setNodeSpriteFrameUrl(this.avatar, users.avatar)
        }
    }
    getUsers(){
        return this.users;
    }

    // update (dt) {}
}
