// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser } from "../bean/GameBean";
import NickNameLabel from "../util/NickNameLabel";
import { Tools } from "../util/Tools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class MatchItemController extends cc.Component {

    @property(cc.Node)
    avatar: cc.Node = null;   //头像
    @property(cc.Node)
    gundongtouxiang: cc.Node = null;   //滚动头像
    @property(cc.Label)
    nameLabel: cc.Label = null;  //用户昵称



    // onLoad () {}

    start () {

    }

    setData(user: RoomUser){
        this.scheduleOnce(() => {
            if (user == null) {
                this.gundongtouxiang.active = true
                this.avatar.active = false

            } else {
               

                Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);//添加头像
                let nicknameLabel = this.nameLabel.getComponent(NickNameLabel)
                nicknameLabel.string = user.nickName;//添加昵称
                this.gundongtouxiang.active = false
                this.avatar.active = true
            }
        }, 0.1);
    }

    // update (dt) {}
}
