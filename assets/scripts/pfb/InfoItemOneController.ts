// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property} = cc._decorator;

@ccclass
export default class InfoItemOneController extends cc.Component {

     
    @property(cc.Label)
    content: cc.Label = null;

    // onLoad () {}

    start () {

    }

    setData(content:string){
        this.content.string = content
    }

    setimgNode(imgNode:cc.Node){
        // 在第 0 位插入新节点
        this.node.insertChild(imgNode, 0);
    }

    // update (dt) {}
}
