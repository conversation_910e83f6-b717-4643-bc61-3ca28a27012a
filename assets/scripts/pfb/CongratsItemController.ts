


import { Publish } from "../../meshTools/tools/Publish";
import { RoomUser, UserSettlement } from "../bean/GameBean";
import { Config } from "../util/Config";
import NickNameLabel from "../util/NickNameLabel";
import { Tools } from "../util/Tools";

const { ccclass } = cc._decorator;

@ccclass
export default class CongratsItemController extends cc.Component {

    boardIconCrownNode: cc.Node;
    boardNum: cc.Node;
    avatarNode: cc.Node;
    nameNode: cc.Node;
    stepNode: cc.Node;
    numberNode: cc.Node;
    beanIcon: cc.Node;


    protected start(): void {
    

        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown');//皇冠的节点
        this.boardNum = this.node.getChildByName('board_num');//皇冠的节点
        this.avatarNode = this.node.getChildByName('avatar');//头像的节点

        // 安全获取嵌套节点
        const nameLayout = this.node.getChildByName('name_layout');
        this.nameNode = nameLayout ? nameLayout.getChildByName('name') : null;//名称的节点

        const stepLayout = this.node.getChildByName('step_layout');
        this.stepNode = stepLayout ? stepLayout.getChildByName('step') : null;// 步数的节点

        const congratsFrame = this.node.getChildByName('congrats_list_frame');
        if (congratsFrame) {
            const numberView = congratsFrame.getChildByName('number_view');
            this.numberNode = numberView ? numberView.getChildByName('number') : null;//金豆的节点
            this.beanIcon = congratsFrame.getChildByName('board_icon_beans');//金豆图标
        }

    }

    //设置数据
    //settleType:结算类型
    //intUserID:中断游戏的玩家Id
    createData(settlement: UserSettlement, gameUsers: RoomUser[]) {
        console.log("CongratsItemController: 开始创建数据", {
            settlement: settlement,
            gameUsers: gameUsers,
            nodeComponents: {
                boardIconCrownNode: !!this.boardIconCrownNode,
                boardNum: !!this.boardNum,
                avatarNode: !!this.avatarNode,
                nameNode: !!this.nameNode,
                stepNode: !!this.stepNode,
                numberNode: !!this.numberNode,
                beanIcon: !!this.beanIcon
            }
        });

        // 检查必要的节点是否存在
        if (!this.boardIconCrownNode || !this.boardNum || !this.avatarNode ||
            !this.nameNode || !this.stepNode || !this.numberNode) {
            console.error("CongratsItemController: 缺少必要的节点组件");
            return;
        }

        if (settlement.rank <= 3) {
            this.boardIconCrownNode.active = true
            this.boardNum.active = false
            //设置皇冠图片
            switch (settlement.rank) {
                case 1:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_01');
                    break;
                case 2:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_02');
                    break;
                case 3:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_03');
                    break;
            }
        } else {
            this.boardIconCrownNode.active = false
            this.boardNum.active = true
            this.boardNum.getComponent(cc.Label).string = settlement.rank + '';//显示名次
        }

        this.stepNode.getComponent(cc.Label).string = settlement.score + '';//显示分数

        // 添加安全检查，确保gameUsers不为空
        if (gameUsers && Array.isArray(gameUsers)) {
          
            let userIndex = gameUsers.findIndex((item) => item.userId === settlement.userId);//搜索

            if (userIndex != -1) {
                let user = gameUsers[userIndex];
               
                Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);
                this.nameNode.getComponent(NickNameLabel).string = user.nickName
            } else {
                console.warn("CongratsItemController: 未找到用户信息", settlement.userId, "在用户列表:", gameUsers.map((u: any) => u.userId));

                // 尝试按排名顺序匹配用户（备用方案）
                const rankBasedIndex = settlement.rank - 1; // rank从1开始，数组从0开始
                if (rankBasedIndex >= 0 && rankBasedIndex < gameUsers.length) {
                    let fallbackUser = gameUsers[rankBasedIndex];
                   
                    Tools.setNodeSpriteFrameUrl(this.avatarNode, fallbackUser.avatar);
                    this.nameNode.getComponent(NickNameLabel).string = fallbackUser.nickName
                } else {
                    // 最后的备用方案：显示默认信息
                    console.warn("CongratsItemController: 无法匹配用户，使用默认显示");
                    this.nameNode.getComponent(NickNameLabel).string = `玩家${settlement.rank}`;
                    // 不设置头像，保持默认
                }
            }
        } else {
            console.warn("CongratsItemController: gameUsers为空或无效", gameUsers);
            // 显示默认信息
            this.nameNode.getComponent(NickNameLabel).string = `玩家${settlement.rank}`;
        }
       

        // 显示余额变化，根据正负值设置前缀
        const coinChangeText = this.formatCoinChange(settlement.coinChg);
        this.numberNode.getComponent(cc.Label).string = coinChangeText;

        // 统一设置文字颜色为原本的白色，不区分红绿
        this.numberNode.color = cc.Color.WHITE;

       

        if (Publish.GetInstance().currencyIcon != null && Publish.GetInstance().currencyIcon !== '') {
            Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish.GetInstance().currencyIcon)
        }

    }

    // 格式化余额变化显示
    private formatCoinChange(coinChange: number): string {
        // 处理undefined、null或NaN的情况
        if (coinChange === undefined || coinChange === null || isNaN(coinChange)) {
            console.warn("CongratsItemController: coinChange值无效", coinChange);
            return "0";
        }

        if (coinChange > 0) {
            return "+" + Tools.NumToTBMK(coinChange);
        } else if (coinChange < 0) {
            return Tools.NumToTBMK(coinChange); // 负数本身就带负号
        } else {
            return "0";
        }
    }
}
