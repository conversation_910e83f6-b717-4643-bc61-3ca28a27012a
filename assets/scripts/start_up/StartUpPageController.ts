// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html


import { GameMgr } from "../common/GameMgr";
import StartUpCenterController from "./StartUpCenterController";
import { GlobalBean } from "../bean/GlobalBean";

const { ccclass, property } = cc._decorator;

@ccclass
export default class StartUpPageController extends cc.Component {

    @property(StartUpCenterController)
    startUpCenterController: StartUpCenterController = null

    start() {
        // 游戏启动时强制清理可能残留的roomId
       
        if (GlobalBean.GetInstance().loginData && GlobalBean.GetInstance().loginData.roomId > 0) {
           
            GlobalBean.GetInstance().loginData.roomId = 0;
        }

        //这是是通知 web 端 游戏加载完成
        this.scheduleOnce(() => {
            if ((<any>window).closeLoadingBg) {
                (<any>window).closeLoadingBg();
            }
            GameMgr.H5SDK.HideLoading();
        }, 0.1);

    }

    //设置登录成功
    setLogin() {
        this.startUpCenterController.setLogin()
    }

    // update (dt) {}
}
