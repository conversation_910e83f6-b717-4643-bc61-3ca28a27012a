// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property} = cc._decorator;

@ccclass
export default class ToastController extends cc.Component {

    @property(cc.Node)
    alertBg: cc.Node = null;

    @property(cc.Label)
    toastLabel: cc.Label = null;

    isShow:boolean = false

    // onLoad () {}

    start () {

    }

    //设置提示文案
    showContent(content: string) {
        if(this.isShow){//判断当前是否正在显示提示文案  如果显示的话 就把上一个的倒计时关掉
           this.unschedule(this.hideContent) 
        }
        this.toastLabel.string = content
        this.alertBg.active = true
        this.isShow = true
        
        this.scheduleOnce(this.hideContent, 1.5)
    }

    hideContent() {
        this.alertBg.active = false
        this.isShow = false
        this.toastLabel.string = ''
    }
}
