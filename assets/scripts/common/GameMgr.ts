

import { BaseSDK } from "../../meshTools/BaseSDK";
import MeshSdkApi from "../../meshTools/tools/MeshSdkApi";
import { EventCenter } from "./EventCenter";
import { GameData } from "./GameData";
import { GameTools } from "./GameTools";
import MineConsole from "./MineConsole";




export class GameMgr {
    private static _h5SDK: BaseSDK = null;

    public static get H5SDK(): BaseSDK {
        if (GameMgr._h5SDK == null) {
            GameMgr._h5SDK = new MeshSdkApi();
        }

        return GameMgr._h5SDK;
    }

    public static get GameData(): GameData {
        return GameData.GetInstance();
    }
    public static get Event(): EventCenter {
        return EventCenter.GetInstance();
    }
    public static get Utils(): GameTools {
        return GameTools.GetInstance();
    }
    public static get Console(): MineConsole {
        return MineConsole.GetInstance();
    }
}