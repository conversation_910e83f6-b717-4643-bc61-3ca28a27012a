// 船只控制器 - 管理船只的皮肤和显示
// 这个脚本应该添加到每个船只预制体上

import SkinManager, { ShipType } from "./SkinManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ShipController extends cc.Component {

    // 公开的精灵图组件，可以在编辑器中设置
    @property(cc.Sprite)
    public shipSprite: cc.Sprite = null;

    // 船只类型（在编辑器中设置）
    @property({
        type: cc.Enum(ShipType),
        tooltip: "船只类型，用于确定使用哪个皮肤图片"
    })
    public shipType: ShipType = ShipType.SHIP_2_1;

    // 是否为对手的沉船（用于区分显示我方船只还是对手沉船）
    @property({
        tooltip: "是否为对手的沉船，true=显示对手沉船皮肤，false=显示我方船只皮肤"
    })
    public isOpponentSunkShip: boolean = false;

    // 船只大小（格子数，用于自动推断船只类型）
    @property({
        tooltip: "船只大小（格子数），用于自动推断船只类型"
    })
    public shipSize: number = 2;

    // 船只索引（同大小船只的索引，用于区分多个相同大小的船）
    // 例如：2格船有2个，第一个设为0，第二个设为1
    // 其他船只类型只有1个，都设为0
    @property({
        tooltip: "船只索引，用于区分多个相同大小的船只\n2格船：0=第一个，1=第二个\n其他船只：都是0"
    })
    public shipIndex: number = 0;

    onLoad() {
        // 如果没有设置精灵图组件，尝试自动获取
        if (!this.shipSprite) {
            this.shipSprite = this.getComponent(cc.Sprite);
            if (!this.shipSprite) {
                console.error(`ShipController: 船只 ${this.node.name} 没有找到 Sprite 组件`);
                return;
            }
        }

        // 根据船只大小自动推断船只类型
        this.autoDetectShipType();
    }

    start() {
        // 应用当前皮肤
        this.applySkin();
    }

    // 根据船只大小自动推断船只类型
    private autoDetectShipType() {
        const skinManager = SkinManager.getInstance();
        this.shipType = skinManager.getShipTypeBySize(this.shipSize, this.shipIndex);

    }

    // 应用皮肤
    public applySkin() {
        if (!this.shipSprite) {
            console.error(`ShipController: 船只 ${this.node.name} 的 Sprite 组件未设置`);
            return;
        }

        const skinManager = SkinManager.getInstance();
        let spritePath: string;

        if (this.isOpponentSunkShip) {
            // 对手沉船：使用对手的沉船皮肤
            const opponentSkinId = skinManager.getOpponentSkinId();
            spritePath = skinManager.getOpponentSunkShipSpritePath(this.shipType);
           
        } else {
            // 我方船只：使用当前皮肤的船只图片
            const currentSkinId = skinManager.getCurrentSkinId();
            spritePath = skinManager.getMyShipSpritePath(this.shipType);
            
        }

        if (!spritePath) {
            console.error(`ShipController: 无法获取皮肤路径，船只类型=${this.shipType}, 是否对手沉船=${this.isOpponentSunkShip}`);
            return;
        }

        // 加载并应用皮肤
        this.loadAndApplySprite(spritePath);
    }

    // 加载并应用精灵图
    private loadAndApplySprite(spritePath: string) {
        cc.resources.load(spritePath, cc.SpriteFrame, (err, spriteFrame: cc.SpriteFrame) => {
            if (err) {
                console.error(`ShipController: 加载皮肤失败 ${spritePath}:`, err);
                return;
            }

            if (!this.shipSprite || !this.shipSprite.isValid) {
                console.warn(`ShipController: Sprite 组件已失效，无法应用皮肤 ${spritePath}`);
                return;
            }

            // 应用新的精灵图
            this.shipSprite.spriteFrame = spriteFrame;
        });
    }

    // 设置为对手沉船
    public setAsOpponentSunkShip(opponentSkinId?: number) {
        this.isOpponentSunkShip = true;

        // 如果指定了对手皮肤ID，先设置对手皮肤
        if (opponentSkinId !== undefined) {
            const skinManager = SkinManager.getInstance();
            skinManager.setOpponentSkin(opponentSkinId);
        }

        // 重新应用皮肤
        this.applySkin();
    }

    // 设置为我方船只
    public setAsMyShip() {
        this.isOpponentSunkShip = false;
        
        // 重新应用皮肤
        this.applySkin();
    }

    // 设置船只类型
    public setShipType(shipType: ShipType) {
        this.shipType = shipType;
        
        // 重新应用皮肤
        this.applySkin();
    }

    // 设置船只大小和索引（自动推断类型）
    public setShipSizeAndIndex(size: number, index: number = 0) {
        this.shipSize = size;
        this.shipIndex = index;
        
        // 重新推断船只类型
        this.autoDetectShipType();
        
        // 重新应用皮肤
        this.applySkin();
    }

    // 强制刷新皮肤（当皮肤管理器的皮肤发生变化时调用）
    public refreshSkin() {
        this.applySkin();
    }

    // 获取当前使用的皮肤路径（用于调试）
    public getCurrentSpritePath(): string {
        const skinManager = SkinManager.getInstance();
        
        if (this.isOpponentSunkShip) {
            return skinManager.getOpponentSunkShipSpritePath(this.shipType);
        } else {
            return skinManager.getMyShipSpritePath(this.shipType);
        }
    }

    // 静态方法：为节点添加船只控制器
    public static addToNode(node: cc.Node, shipSize: number, shipIndex: number = 0): ShipController {
        let controller = node.getComponent(ShipController);
        if (!controller) {
            controller = node.addComponent(ShipController);
        }
        
        controller.setShipSizeAndIndex(shipSize, shipIndex);
        return controller;
    }

    // 静态方法：批量刷新所有船只皮肤
    public static refreshAllShips() {
        // 查找场景中所有的 ShipController
        const allControllers = cc.director.getScene().getComponentsInChildren(ShipController);

        allControllers.forEach(controller => {
            if (controller && controller.isValid) {
                controller.refreshSkin();
            }
        });
    }

    // 静态方法：专门刷新对手沉船皮肤
    public static refreshOpponentSunkShips() {
        const allControllers = cc.director.getScene().getComponentsInChildren(ShipController);
        const sunkShips = allControllers.filter(controller =>
            controller && controller.isValid && controller.isOpponentSunkShip
        );

   

        sunkShips.forEach((controller, index) => {
           
            controller.applySkin();
        });
    }

    // 静态方法：手动测试 - 设置对手皮肤并刷新沉船
    public static testOpponentSkin(skinId: number) {
        const skinManager = SkinManager.getInstance();
        skinManager.setOpponentSkin(skinId);

        ShipController.refreshOpponentSunkShips();
    }
}
