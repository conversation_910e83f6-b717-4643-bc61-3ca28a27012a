// 皮肤管理器 - 单例模式
// 管理当前皮肤状态、皮肤资源路径映射、皮肤切换逻辑

// 皮肤配置接口
interface SkinConfig {
    id: number;           // 皮肤ID (2100-2102)
    name: string;         // 皮肤名称
    myShipPaths: string[]; // 我方船只图片路径 [10格, 5格, 4格, 3格, 2格1, 2格2]
    enemySunkPaths: string[]; // 对方沉船图片路径 [10格, 5格, 4格, 3格, 2格1, 2格2]
}

// 船只类型枚举
export enum ShipType {
    SHIP_10 = 0, // 10格船 (2*5)
    SHIP_5 = 1,  // 5格船 (1*5) - 只有一个
    SHIP_4 = 2,  // 4格船 (1*4)
    SHIP_3 = 3,  // 3格船 (1*3)
    SHIP_2_1 = 4, // 2格船1 (1*2) - 第一个
    SHIP_2_2 = 5  // 2格船2 (1*2) - 第二个
}

export default class SkinManager {
    private static instance: SkinManager = null;
    
    private currentSkinId: number = 2100; // 当前使用的皮肤ID，默认为2100
    private opponentSkinId: number = 2100; // 对手的皮肤ID
    private ownedSkins: number[] = [2100]; // 拥有的皮肤列表，默认拥有2100
    
    // 皮肤配置映射
    private skinConfigs: { [skinId: number]: SkinConfig } = {
        2100: {
            id: 2100,
            name: "默认皮肤",
            myShipPaths: [
                "skin/yuanpi/10格蓝", // 10格船
                "skin/yuanpi/5格蓝",  // 5格船
                "skin/yuanpi/4格蓝",  // 4格船
                "skin/yuanpi/3格蓝",  // 3格船
                "skin/yuanpi/2格蓝01",  // 2格船1
                "skin/yuanpi/2格蓝02"   // 2格船2
            ],
            enemySunkPaths: [
                "skin/diren/hitShip/10格红1", // 10格沉船
                "skin/diren/hitShip/5格红1",  // 5格沉船
                "skin/diren/hitShip/4格红1",  // 4格沉船
                "skin/diren/hitShip/3格红1",  // 3格沉船
                "skin/diren/hitShip/2格红1",  // 2格沉船1
                "skin/diren/hitShip/2格红1"   // 2格沉船2
            ]
        },
        2101: {
            id: 2101,
            name: "皮肤1",
            myShipPaths: [
                "skin/pifu1/10格1", // 10格船
                "skin/pifu1/5格1",  // 5格船
                "skin/pifu1/4格1",  // 4格船
                "skin/pifu1/3格1",  // 3格船
                "skin/pifu1/2格10",  // 2格船1
                "skin/pifu1/2格1"   // 2格船2
            ],
            enemySunkPaths: [
                "skin/pifu1/badpifu1/10格11", // 10格沉船
                "skin/pifu1/badpifu1/5格11",  // 5格沉船
                "skin/pifu1/badpifu1/4格11",  // 4格沉船
                "skin/pifu1/badpifu1/3格11",  // 3格沉船
                "skin/pifu1/badpifu1/2格110",  // 2格沉船1
                "skin/pifu1/badpifu1/2格11"   // 2格沉船2
            ]
        },
        2102: {
            id: 2102,
            name: "皮肤2",
            myShipPaths: [
                "skin/pifu2/10格2", // 10格船
                "skin/pifu2/5格2",  // 5格船
                "skin/pifu2/4格2",  // 4格船
                "skin/pifu2/3格2",  // 3格船
                "skin/pifu2/2格20",  // 2格船1
                "skin/pifu2/2格2"   // 2格船2
            ],
            enemySunkPaths: [
                "skin/pifu2/badpifu2/10格22", // 10格沉船
                "skin/pifu2/badpifu2/5格22",  // 5格沉船
                "skin/pifu2/badpifu2/4格22",  // 4格沉船
                "skin/pifu2/badpifu2/3格22",  // 3格沉船
                "skin/pifu2/badpifu2/2格220",  // 2格沉船1
                "skin/pifu2/badpifu2/2格22"   // 2格沉船2
            ]
        }
    };

    // 获取单例实例
    public static getInstance(): SkinManager {
        if (!SkinManager.instance) {
            SkinManager.instance = new SkinManager();
        }
        return SkinManager.instance;
    }

    private constructor() {
        // 私有构造函数，确保单例模式
    }

    // 设置当前皮肤
    public setCurrentSkin(skinId: number): boolean {
        if (!this.skinConfigs[skinId]) {
            return false;
        }

        this.currentSkinId = skinId;
        return true;
    }

    // 获取当前皮肤ID
    public getCurrentSkinId(): number {
        return this.currentSkinId;
    }

    // 设置对手皮肤
    public setOpponentSkin(skinId: number): boolean {
        if (!this.skinConfigs[skinId]) {
            return false;
        }

        this.opponentSkinId = skinId;
        return true;
    }

    // 获取对手皮肤ID
    public getOpponentSkinId(): number {
        return this.opponentSkinId;
    }

    // 设置拥有的皮肤列表
    public setOwnedSkins(ownedSkins: number[]): void {
        this.ownedSkins = ownedSkins;
    }

    // 获取拥有的皮肤列表
    public getOwnedSkins(): number[] {
        return [...this.ownedSkins];
    }

    // 检查是否拥有某个皮肤
    public hasSkin(skinId: number): boolean {
        return this.ownedSkins.includes(skinId);
    }

    // 添加拥有的皮肤
    public addSkinToOwned(skinId: number): void {
        if (!this.hasSkin(skinId)) {
            this.ownedSkins.push(skinId);
        }
    }

    // 获取我方船只图片路径
    public getMyShipSpritePath(shipType: ShipType): string {
        const config = this.skinConfigs[this.currentSkinId];
        if (!config || shipType < 0 || shipType >= config.myShipPaths.length) {
            console.error(`无法获取船只图片路径: 皮肤ID=${this.currentSkinId}, 船只类型=${shipType}`);
            return "";
        }
        
        return config.myShipPaths[shipType];
    }

    // 获取对手沉船图片路径
    public getOpponentSunkShipSpritePath(shipType: ShipType): string {
        const config = this.skinConfigs[this.opponentSkinId];
        if (!config || shipType < 0 || shipType >= config.enemySunkPaths.length) {
            return "";
        }

        return config.enemySunkPaths[shipType];
    }

    // 获取皮肤配置
    public getSkinConfig(skinId: number): SkinConfig | null {
        return this.skinConfigs[skinId] || null;
    }

    // 获取所有可用皮肤ID
    public getAvailableSkinIds(): number[] {
        return Object.keys(this.skinConfigs).map(id => parseInt(id));
    }

    // 根据船只大小获取船只类型
    public getShipTypeBySize(shipSize: number, index: number = 0): ShipType {
        switch (shipSize) {
            case 10:
                return ShipType.SHIP_10;
            case 5:
                return ShipType.SHIP_5; // 5格船只有一个
            case 4:
                return ShipType.SHIP_4;
            case 3:
                return ShipType.SHIP_3;
            case 2:
                return index === 0 ? ShipType.SHIP_2_1 : ShipType.SHIP_2_2; // 2格船有两个
            default:
                console.error(`未知的船只大小: ${shipSize}`);
                return ShipType.SHIP_2_1;
        }
    }

    // 预加载皮肤资源
    public preloadSkinResources(skinId: number, callback?: () => void): void {
        const config = this.skinConfigs[skinId];
        if (!config) {
            console.error(`皮肤ID ${skinId} 不存在，无法预加载`);
            if (callback) callback();
            return;
        }

        const allPaths = [...config.myShipPaths, ...config.enemySunkPaths];
        let loadedCount = 0;
        const totalCount = allPaths.length;

        allPaths.forEach(path => {
            cc.resources.load(path, cc.SpriteFrame, (err, _spriteFrame) => {
                loadedCount++;
                if (err) {
                    console.warn(`预加载皮肤资源失败: ${path}`, err);
                } else {
               
                }

                if (loadedCount === totalCount) {
            
                    if (callback) callback();
                }
            });
        });
    }

    // 预加载所有皮肤资源
    public preloadAllSkinResources(callback?: () => void): void {
        const skinIds = this.getAvailableSkinIds();
        let loadedSkinCount = 0;

        skinIds.forEach(skinId => {
            this.preloadSkinResources(skinId, () => {
                loadedSkinCount++;
                if (loadedSkinCount === skinIds.length) {
                
                    if (callback) callback();
                }
            });
        });
    }
}
