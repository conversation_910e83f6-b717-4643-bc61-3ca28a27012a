/*
 * @Author: <PERSON>
 * @Date: 2023-03-15 11:47:21
 */

import { MeshTools } from "../../meshTools/MeshTools";
import { Singleton } from "../../meshTools/Singleton";
import { GameServerUrl } from "../net/GameServerUrl";

export class GameData extends Singleton
{
    public GameIsInFront: boolean = true;//true 游戏中没有压后台  false 压后台了  

    // ?env=0
    // &game_id=1051
    // &game_mode=3
    // &user_id=667055436
    // &country=EN
    // &code=RW0WXfzGQmTw8gqDkIg5LfUm0UBGN6UTFAynqhENZjVgehdufnSlQhmseHHW
    // &app_id=66666666
    // &app_channel=mesh
    // &room_id=room01
    // &role=1



    public get ServerUrl (): string
    {
        let url :string= GameServerUrl.Ws+
                            '?env='+MeshTools.Publish.getEnv()+
                            '&version=' + MeshTools.Publish.getVersion() +
                            '&game_id='+MeshTools.Publish.getGameId()+
                            '&game_mode='+MeshTools.Publish.gameMode+
                            '&user_id='+MeshTools.Publish.userId+
                            '&country='+MeshTools.Publish.language+
                            '&code='+MeshTools.Publish.code+
                            '&app_id='+MeshTools.Publish.appId+
                            '&app_channel='+MeshTools.Publish.appChannel+
                            '&room_id='+MeshTools.Publish.roomId;
                            
        return url;
    }
}