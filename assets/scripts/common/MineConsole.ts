import { Singleton } from "../../meshTools/Singleton";



export default class MineConsole extends Singleton
{
    public IsShowConsole: boolean = true;

    private showInfo (callback: () => void = null): void
    {
        if (this.IsShowConsole)
        {
            callback && callback();
        }
    }

    public Log (...data: any[]): void
    {
        this.showInfo(() =>
        {
            console.log(...data);
        });
    }

    public Info (...data: any[]): void
    {
        this.showInfo(() =>
        {
            console.info(...data);
        });
    }

    public Warn (...data: any[]): void
    {
        // this.showInfo(() =>
        // {
        //     console.warn(...data);
        // });
        
        console.warn(...data);
    }

    public Error (...data: any[]): void
    {
        // this.showInfo(() =>
        // {
        //     console.error(...data);
        // });

        console.error(...data);
    }

    public Group (groupKey: string, ...data: any[]): void
    {
        this.showInfo(() =>
        {
            console.group(groupKey);
            console.log(...data);
            console.groupEnd();
        });
    }

    public LogString (str: string, color: cc.Color = cc.Color.WHITE): void
    {
        this.showInfo(() =>
        {
            let style: string = "background-color:#" + color.toHEX("#rrggbb");
            cc.sys.isBrowser ? console.log("%c" + str, style) : console.log(str);
        });
    }

    public Table (data: {}): void
    {
        this.showInfo(() =>
        {
            cc.sys.isBrowser ? console.table(data) : console.log(data);
        });
    }

    public Assert (correctConditions: boolean, ...data: any[]): void
    {
        this.showInfo(() =>
        {
            console.assert(correctConditions, ...data);
        });
    }

    public Time (funcKey: string, func: (...data: any) => void = null, ...params: any): void
    {
        this.showInfo(() =>
        {
            console.time(funcKey);
            func && func(...params);
            console.timeEnd(funcKey);
        });
    }

    public TimeInterval (key: string): () => void
    {
        let time: number = new Date().getTime();
        return () =>
        {
            let nowTime: number = new Date().getTime();
            this.Log(key + " time: " + ((nowTime - time) / 1000) + "s");
        }
    }
}