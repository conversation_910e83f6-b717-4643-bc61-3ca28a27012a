import { Singleton } from "../../meshTools/Singleton";



export class GameTools extends Singleton
{

    public GetUrlParams(url: string): {} {
        let urlArr = url.split("?");
        let data = {};
        if (urlArr.length === 1) return data;
        for (let i = 1; i <= urlArr.length - 1; i++) {
            let paramsStr = decodeURIComponent(urlArr[i]);
            if (paramsStr && paramsStr !== 'undefined') {
                let paramsArr = paramsStr.split("&");
                paramsArr.forEach((str) => {
                    let [key, ...rest] = str.split("=");
                    let value = rest.join("=");
                    if (key) data[key] = value;
                });
            }
        }
        return data;
    }

}
