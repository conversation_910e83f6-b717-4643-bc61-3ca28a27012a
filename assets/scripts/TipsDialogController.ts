// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { Config } from "./util/Config";
import { Tools } from "./util/Tools";

const { ccclass, property } = cc._decorator;

//这个是只有一个退出按钮的 弹窗
@ccclass
export default class TipsDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Label)
    content: cc.Label = null;//文案
    @property(cc.Node)
    leaveButton: cc.Node = null;//退出按钮

    callback: Function
    // onLoad () {}

    start() {

        Tools.redButton(this.leaveButton, () => {
            if (this.callback) {
                this.callback();
            }
        })

    }

    showDialog(content: string, callback: Function) {
        this.callback = callback
        this.content.string = content
        if (this.node.active == false) {
            this.node.active = true
            this.boardBg.scale = 0
            // 执行缩放动画
            cc.tween(this.boardBg)
                .to(Config.dialogScaleTime, { scale: 1 })
                .start();
        }

    }

    // update (dt) {}
}
