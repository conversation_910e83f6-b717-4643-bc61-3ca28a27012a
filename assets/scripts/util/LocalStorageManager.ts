import { Singleton } from "../../meshTools/Singleton";



//这个就是本地存储的管理类
export class LocalStorageManager extends Singleton {

    musicSwitch: string = 'MusicSwitch';//背景音乐的控制
    soundSwitch: string = 'SoundSwitch';//动效音乐的控制

   private _setValue(key: string, value: string) {
        localStorage.setItem(key, value);
    }

    private _getValue(key: string,) {
        return localStorage.getItem(key);
    }

    //设置背景音乐的开关
    setMusicSwitch(bool: boolean) {
        this._setValue(this.musicSwitch, bool ? '1' : '0');
    }
    //获取背景音乐的开关,默认是开的
    getMusicSwitch() {
        let value = this._getValue(this.musicSwitch);
        if(value){
            return value == '1' ? true : false;
        }else{
            return true;
        }
        
    }

    //设置动效音乐的开关
    setSoundSwitch(bool: boolean) {
        this._setValue(this.soundSwitch, bool ? '1' : '0');
    }
    //获取动效音乐的开关,默认是开的
    getSoundSwitch() {
        let value = this._getValue(this.soundSwitch);
        if(value){
            return value == '1' ? true : false;
        }else{
            return true;
        }
    }
}