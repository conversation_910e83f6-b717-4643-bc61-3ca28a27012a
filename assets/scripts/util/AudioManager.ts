import { AudioMgr } from "./AudioMgr";
import { LocalStorageManager } from "./LocalStorageManager";

export class AudioManager {

    //播放 Bgm
    static playBgm() {
        // 播放音乐，音乐文件名为'bgm.mp3'
        let music = LocalStorageManager.GetInstance().getMusicSwitch();//判断有没有开启背景音乐
        if (!music) {
            return;
        }
        AudioMgr.ins.playMusic('bgm');
    }
    //停止 bgm
    static stopBgm() {
        AudioMgr.ins.stopMusic();
    }


    //按键音效
    static keyingToneAudio() {
        AudioManager.playSound('keying_tone');
    }

    
    //胜利音效
    static winAudio() {
        AudioManager.playSound('you_win');
    }
    //失败音效
    static loseAudio() {
        AudioManager.playSound('you_lose');
    }
   

    //播放音效
    static playSound(audioName: string) {
        let sound = LocalStorageManager.GetInstance().getSoundSwitch();//判断有没有开启音效
        if (!sound) {
            return;
        }
        AudioMgr.ins.playSound(audioName)
    }
}