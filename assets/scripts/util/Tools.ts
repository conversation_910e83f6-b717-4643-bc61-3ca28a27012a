// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { AudioManager } from "./AudioManager";
import { Config } from "./Config";

const { ccclass, property } = cc._decorator;


export class Tools {

    static setTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction)
    }
    static setGameTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction)
    }

    //添加点击事件
    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要
    //peopleNode 节点
    //startFunction 按下事件
    //endFunction 抬起事件
    //cancelFunction 取消事件
    private static setTouchEventParent(peopleNode: cc.Node, isSound: boolean, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        if (!peopleNode || !peopleNode.isValid) {
            console.warn("Tools.setTouchEventParent: 节点无效或为空");
            return;
        }

        peopleNode.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
            if (isSound) {
                AudioManager.keyingToneAudio();
            }

            if (startFunction != null) {
                startFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            if (endFunction != null) {
                endFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, (event: cc.Event.EventTouch) => {
            if (cancelFunction != null) {
                cancelFunction(peopleNode, event);
            }
        }, this);
    }

    static cancelTouchStartListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_START, this);
    }
    static cancelTouchEndListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_END, this);
    }
    static cancelTouchCancelListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);
    }

    //为精灵添加图片
    static setNodeSpriteFrame(node: cc.Node, path: string) {
        cc.resources.load(path, cc.SpriteFrame, function (error: Error, assets: cc.SpriteFrame) {
            const sprite = node.getComponent(cc.Sprite);
            sprite.spriteFrame = assets;
        });
    }

    //添加网络图片
    static setNodeSpriteFrameUrl(node: cc.Node, url: string) {
        let avatarSp = node.getComponent(cc.Sprite)


        if (url == null || url == '') {
            return;
        }

        cc.assetManager.loadRemote(url, { ext: '.png' }, (err, texture: cc.Texture2D) => {
            if (err) {
                cc.error(err.message || err);
                return;
            }

            texture.packable = false;//加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
        });
    }

    //红色按钮
    static redButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnRedNormal, Config.btnRedPressed, Config.btnRedNormalColor, Config.btnRedPressedColor, click,label);
    }
    //绿色按钮
    static greenButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnGreenNormal, Config.btnGreenPressed, Config.btnGreenNormalColor, Config.btnGreenPressedColor, click,label);
    }
    //黄色按钮
    static yellowButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnYellowNormal, Config.btnYellowPressed, Config.btnYellowNormalColor, Config.btnYellowPressedColor, click,label);
    }
    //灰色按钮
    static grayButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnGrayNormal, Config.btnGrayNormal, Config.btnGrayNormalColor, Config.btnGrayNormalColor, click,label);
    }


    //通用的按钮点击事件，带点击变颜色的
    static buttonState(node: cc.Node, normalImg: string, pressedImg: string, normalColor: string, pressedColor: string, click?: Function,labelText?:string) {
        if (!node || !node.isValid) {
            console.warn("Tools.buttonState: 节点无效或为空");
            return;
        }

        let btnGreen = node.getChildByName('btn_color_normal');//获取按钮背景节点
        let btnLabel = node.getChildByName('button_label');//获取按钮文字节点

        if (!btnGreen) {
            console.warn("Tools.buttonState: 找不到 btn_color_normal 子节点");
            return;
        }

        if (!btnLabel) {
            console.warn("Tools.buttonState: 找不到 button_label 子节点");
            return;
        }

        let label = btnLabel.getComponent(cc.Label);
        let labelOutline = btnLabel.getComponent(cc.LabelOutline);

        if (!label) {
            console.warn("Tools.buttonState: button_label 没有 Label 组件");
            return;
        }

        if (!labelOutline) {
            console.warn("Tools.buttonState: button_label 没有 LabelOutline 组件");
            return;
        }

        // 存储按钮的普通状态信息，用于重置
        if (btnGreen && btnGreen.isValid) {
            (btnGreen as any)._normalImg = normalImg;
            (btnGreen as any)._normalColor = normalColor;
            (btnGreen as any)._btnLabel = btnLabel;
        }

        if(labelText!=null){
            label.string = labelText
        }

        Tools.setTouchEvent(btnGreen, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, pressedImg);
            label.fontSize = 34;
            label.lineHeight= 34
            let color = new cc.Color();
            cc.Color.fromHEX(color, pressedColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#B3B3B3');


        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight= 36
            let color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');
            if (click != null) {
                click();
            }
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight= 36
            let color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');
        });
    }

    //点击变颜色的图片按钮
    static imageButtonClick(node: cc.Node, normalImg: string, pressedImg: string, click: Function) {
        if (!node || !node.isValid) {
            console.warn("Tools.imageButtonClick: 节点无效或为空");
            return;
        }

        // 存储按钮的普通状态图片，用于重置
        (node as any)._normalImg = normalImg;

        Tools.setTouchEvent(node, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, pressedImg);
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            click()
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
        });
    }

    //重置按钮到普通状态 - 解决页面切换时按钮卡在按下状态的问题
    static resetButtonToNormal(node: cc.Node) {
        if (!node || !node.isValid) {
            return;
        }

        // 处理图片按钮
        const normalImg = (node as any)._normalImg;
        if (normalImg) {
            Tools.setNodeSpriteFrame(node, normalImg);
            return;
        }

        // 处理彩色按钮
        const btnGreen = node.getChildByName('btn_color_normal');
        if (btnGreen) {
            const storedNormalImg = (btnGreen as any)._normalImg;
            const storedNormalColor = (btnGreen as any)._normalColor;
            const storedBtnLabel = (btnGreen as any)._btnLabel;

            if (storedNormalImg) {
                Tools.setNodeSpriteFrame(btnGreen, storedNormalImg);
            }

            if (storedBtnLabel && storedNormalColor) {
                const label = storedBtnLabel.getComponent(cc.Label);
                const labelOutline = storedBtnLabel.getComponent(cc.LabelOutline);

                if (label) {
                    label.fontSize = 36;
                    label.lineHeight = 36;
                    storedBtnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
                }

                if (labelOutline) {
                    let color = new cc.Color();
                    cc.Color.fromHEX(color, storedNormalColor);
                    labelOutline.color = color;
                }
            }
        }

        // 检查是否有倒计时标签，如果有则保持其自定义位置不变
        const timeBtn = node.getChildByName('buttonLabel_time');
        if (timeBtn) {
            // 不重置倒计时标签的位置，保持其自定义设置
            console.log("Tools.resetButtonToNormal: 检测到倒计时标签，保持其位置不变");
        }
    }

    //重置页面中所有按钮到普通状态
    static resetAllButtonsInNode(parentNode: cc.Node) {
        if (!parentNode || !parentNode.isValid) {
            return;
        }

        // 递归查找所有可能的按钮节点
        const findAndResetButtons = (node: cc.Node) => {
            // 检查当前节点是否是按钮
            if ((node as any)._normalImg || node.getChildByName('btn_color_normal')) {
                Tools.resetButtonToNormal(node);
            }

            // 递归检查子节点
            for (let i = 0; i < node.children.length; i++) {
                findAndResetButtons(node.children[i]);
            }
        };

        findAndResetButtons(parentNode);
    }

    //格式化资金显示格式的
    static NumToTBMK(num: number, digit: number = 1, min: number = 10000): string {
        // 处理undefined、null或NaN的情况
        if (num === undefined || num === null || isNaN(num)) {
            console.warn("Tools.NumToTBMK: 输入值无效", num);
            return "0";
        }

        let intNum = num;
        if (intNum < min) {
            return intNum.toString();
        }

        let unitStrArr = ["T", "B", "M", "K"];
        let unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];
        for (let i = 0; i < unitArr.length; ++i) {
            let result = intNum / unitArr[i];
            if (result >= 1) {
                let str = result.toString();
                let strArr = str.split(".");
                let suffix = strArr[1] ?? "";
                if (suffix.length >= digit) {
                    if (digit == 0) {
                        return strArr[0] + unitStrArr[i];
                    }
                    return strArr[0] + "." + suffix.substring(0, digit) + unitStrArr[i];
                }
                else {
                    let fillStr = new Array(digit - suffix.length).fill("0").join("");
                    return strArr[0] + "." + suffix + fillStr + unitStrArr[i];
                }
            }
        }

        // 如果没有匹配到任何单位，返回原始数字的字符串形式
        return intNum.toString();
    }

    static getCurrentTimeWithMilliseconds(): string {
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const hours = String(currentDate.getHours()).padStart(2, '0');
        const minutes = String(currentDate.getMinutes()).padStart(2, '0');
        const seconds = String(currentDate.getSeconds()).padStart(2, '0');
        const milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    //赋值文本到剪切板
    static copyToClipboard(text: string) {
        const textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                console.log('文本已复制到剪切板');
            } else {
                console.error('复制到剪切板失败');
            }
        } catch (err) {
            console.error('复制到剪切板失败：', err);
        }
        document.body.removeChild(textarea);
    }

    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)
    static chunkArray<T>(arr: T[], chunkSize: number): T[][] {
        const result: T[][] = [];
        for (let i = 0; i < arr.length; i += chunkSize) {
            result.push(arr.slice(i, i + chunkSize));
        }
        return result;
    }

    //设置倒计时的秒数的位置（使用固定位置逻辑，避免位置随文字变化而移动）
    static setCountDownTimeLabel(buttonNode: cc.Node) {
        if (!buttonNode || !buttonNode.isValid) {
            console.warn("Tools.setCountDownTimeLabel: 按钮节点无效或为空");
            return;
        }

        let btn = buttonNode.getChildByName('button_label');
        let timeBtn = buttonNode.getChildByName('buttonLabel_time');

        if (!btn) {
            console.warn("Tools.setCountDownTimeLabel: 找不到 button_label 子节点");
            return;
        }

        if (!timeBtn) {
            console.warn("Tools.setCountDownTimeLabel: 找不到 buttonLabel_time 子节点");
            return;
        }

        // 设置锚点为左对齐，确保左括号位置固定
        timeBtn.anchorX = 0;
        timeBtn.anchorY = 0.5;

        // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置
        let fixedLeftPos = btn.position.x + btn.width / 2 + 5; // 5像素间距
        timeBtn.setPosition(fixedLeftPos, 0);

        console.log("Tools.setCountDownTimeLabel: 倒计时位置已设置为固定位置", {
            btnPos: btn.position,
            btnWidth: btn.width,
            fixedLeftPos: fixedLeftPos,
            timeBtnPos: timeBtn.position
        });
    }

}
