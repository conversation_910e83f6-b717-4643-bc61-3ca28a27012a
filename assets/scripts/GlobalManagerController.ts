// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MeshTools } from "../meshTools/MeshTools";
import { Publish } from "../meshTools/tools/Publish";
import { RoomType } from "./bean/EnumBean";
import { AcceptInvite, InviteKickOut, NoticeLeaveInvite, NoticeSettlement, NoticeStartGame, NoticeUserInviteStatus, UserInfo, ReconnectGameData } from "./bean/GameBean";
import { GlobalBean } from "./bean/GlobalBean";
import LanguageType from "./bean/LanguageType";
import { EventType } from "./common/EventCenter";
import { GameMgr } from "./common/GameMgr";
import SkinManager from "./common/SkinManager";
import ShipController from "./common/ShipController";
import GamePageController from "./game/GamePageController";
import HallPageController, { HallOrMatch } from "./hall/HallPageController";
import TopUpDialogController from "./hall/TopUpDialogController";
import { ErrorCode } from "./net/ErrorCode";
import { GameServerUrl } from "./net/GameServerUrl";

import { AutoMessageBean, AutoMessageId, ReceivedMessageBean } from "./net/MessageBaseBean";
import { MessageId } from "./net/MessageId";
import { WebSocketManager } from "./net/WebSocketManager";
import { WebSocketTool } from "./net/WebSocketTool";
import StartUpPageController from "./start_up/StartUpPageController";
import TipsDialogController from "./TipsDialogController";
import ToastController from "./ToastController";
import { AudioMgr } from "./util/AudioMgr";
import { Config } from "./util/Config";
import { HttpManager } from "./net/HttpManager";

const { ccclass, property } = cc._decorator;

// 全局主动退出标志，避免实例重置问题
let globalActivelyLeavingFlag = false;

window.languageName = "en";

export enum PageType {
    START_UP_PAGE,//启动页面
    HALL_PAGE,//大厅页面
    GAME_PAGE,//游戏页面
}
// 测试
@ccclass
export default class GlobalManagerController extends cc.Component {

    // 静态实例引用（单场景项目简化）
    private static instance: GlobalManagerController = null;

    @property(TipsDialogController)
    tipsDialogController: TipsDialogController = null //这个是错误弹窗 只有一个退出按钮
    @property(TopUpDialogController)
    topUpDialogController: TopUpDialogController = null //充值弹窗
    @property(cc.Node)
    netError: cc.Node = null  //这个是断网的时候展示的转圈的
    @property(ToastController)
    toastController: ToastController = null  //toast 的布局

    @property(cc.Node)
    startUpPage: cc.Node = null  //启动页
    @property(cc.Node)
    hallPage: cc.Node = null  //大厅页
    @property(cc.Node)
    gamePage: cc.Node = null  //游戏页面



    currentPage: PageType = PageType.START_UP_PAGE //当前展示的页面，默认展示的是启动页面

    startUpPageController: StartUpPageController = null  //启动页面的总管理器
    hallPageController: HallPageController = null   //大厅页面的总管理器
    gamePageController: GamePageController = null   //游戏页面的总管理器


    // 标记是否是通过结算重置的游戏，用于确保匹配界面正常显示
    public isGameResetFromSettlement: boolean = false
    // 防止重复发送EnterRoom请求的标志
    private isReconnecting: boolean = false

    // 获取静态实例
    public static getInstance(): GlobalManagerController | null {
        return GlobalManagerController.instance;
    }

    // 静态方法：检查是否允许皮肤更换 - 修改为始终允许
    public static isSkinChangeAllowedGlobally(): boolean {
        // 去掉皮肤更换限制，任何时候都可以换皮肤
        return true;
    }

    // 检查是否正在重连
    public isCurrentlyReconnecting(): boolean {
        return this.isReconnecting;
    }


    onLoad() {
        // 设置静态实例引用
        GlobalManagerController.instance = this;

        cc.resources.preloadDir(Config.hallRes, cc.SpriteFrame);//提前预加载大厅图片资源

        // 获取音频管理器实例
        const audioMgr = AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();

        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr.H5SDK.AddAPPEvent();

        this.getAppConfig();

        cc.game.on(cc.game.EVENT_SHOW, () => {
            GameMgr.Console.Log("EVENT_SHOW")
            GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool.GetInstance().atOnceReconnect();

        }, this);

        cc.game.on(cc.game.EVENT_HIDE, () => {
            GameMgr.Console.Log("EVENT_HIDE")
            GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool.GetInstance().disconnect();

        }, this);

        //这里监听程序内消息
        GameMgr.Event.AddEventListener(EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr.Event.AddEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onMessage, this);

        this.setCurrentPage(PageType.START_UP_PAGE);
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController);
        this.hallPageController = this.hallPage.getComponent(HallPageController);
        this.gamePageController = this.gamePage.getComponent(GamePageController);


      

    }
    protected onEnable(): void {

    }

    protected onDestroy(): void {
        // 清理静态实例引用
        if (GlobalManagerController.instance === this) {
            GlobalManagerController.instance = null;
        }

        GameMgr.Event.RemoveEventListener(EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr.Event.RemoveEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onMessage, this);
    }

    public getAppConfig(): void {
        GameMgr.H5SDK.GetConfig((config: any) => {
            MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools.Publish.roomId = String(config.roomId) ?? "";
            MeshTools.Publish.currencyIcon = config?.gameConfig?.currencyIcon ?? "";

            MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools.Publish.userId = String(config.userId);
            MeshTools.Publish.language = String(config.language);

            MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            this.getHpptPath();

        });
    }

    getHpptPath() {
        this.setLanguage() //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'))
        } else {
            // 获取游戏服务器地址


            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });

            GameServerUrl.Ws = "ws://************:2059/acceptor"
            WebSocketManager.GetInstance().connect();
        }
    }

    public getUrlParams(): void {
        let params: any = GameMgr.Utils.GetUrlParams(window.location.href);//获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools.Publish.userId = params.userId;
                        MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    }

    setLanguage() {

        switch (Publish.GetInstance().language) {
            case LanguageType.SimplifiedChinese: //简体中文
                window.languageName = LanguageType.SimplifiedChinese_type
                break;
            case LanguageType.TraditionalChinese: //繁体中文
                window.languageName = LanguageType.TraditionalChinese_type
                break;
            default: //默认是英语
                window.languageName = LanguageType.English_type
                break
        }

        window.refreshAllLocalizedComp()
    }

    showTips(content: string) {

        this.tipsDialogController.showDialog(content, () => {
            GameMgr.H5SDK.CloseWebView()

        })
    }


    //程序内的通知消息
    onAutoMessage(autoMessageBean: AutoMessageBean) {
        switch (autoMessageBean.msgId) {

            case AutoMessageId.JumpHallPage://跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE)
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess()//因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                } else if (autoMessageBean.data.type === 2) { //2是结算弹窗跳转的
                    // 确保显示大厅页面而不是匹配页面
                    this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT);
                    this.hallPageController.updateGold();
                }
                break;
            case AutoMessageId.ReconnectionFailureMsg://长链接重连失败
                this.netError.active = false
                this.showTips(window.getLocalizedStr('NetworkError'))
                break;
            case AutoMessageId.LinkExceptionMsg://长链接异常
                this.netError.active = true
                break;
            case AutoMessageId.GameRouteNotFoundMsg://游戏线路异常的通知
                this.showTips(autoMessageBean.data.code)
                break;

            case AutoMessageId.SwitchGameSceneMsg://切换游戏场景
                // 在切换到游戏页面之前先刷新游戏界面的用户数据
                this.refreshGameMatchUserData();
                this.setCurrentPage(PageType.GAME_PAGE)
                break;
            case AutoMessageId.WalletUpdateMsg://更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeUserInfo, {});
                break;
            case AutoMessageId.ServerCodeUpdateMsg://更新 code 的通知
                Publish.GetInstance().code = autoMessageBean.data.code
                break;



        }

    }

    //长链接消息(异常)
    onErrorMessage(messageBean: ReceivedMessageBean) {
        switch (messageBean.code) {
            case ErrorCode.ErrInvalidInviteCode://无效的邀请码
                this.hallPageController.joinError()
                break;
            case ErrorCode.ErrRequestUser://获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'))
                // 断开WebSocket连接
                WebSocketTool.GetInstance().disconnect();

                break;
            case ErrorCode.ErrNotFoundRoom://没有找到指定的房间
                console.log("GlobalManagerController: 收到房间不存在错误", {
                    msgId: messageBean.msgId,
                    currentPage: this.currentPage
                });

                // 对GetUserSkin消息的房间不存在错误进行特殊处理，不显示弹窗
                if (messageBean.msgId === MessageId.GetUserSkin) {

                    // 清理无效的roomId，但不显示错误弹窗
                    if (GlobalBean.GetInstance().loginData) {
                        GlobalBean.GetInstance().loginData.roomId = 0;
                    }
                    return;
                }

                // 对Settlement消息的房间不存在错误进行特殊处理
                if (messageBean.msgId === MessageId.MsgTypeSettlement) {
                  
                    // 不显示错误弹窗，不跳转页面，让Settlement消息正常处理
                    // 清理无效的roomId
                    if (GlobalBean.GetInstance().loginData) {
                        GlobalBean.GetInstance().loginData.roomId = 0;
                    }
                    return;
                }

                // 对DeployShips消息的房间不存在错误进行特殊处理
                if (messageBean.msgId === MessageId.DeployShips) {
                 
                    // 不显示错误弹窗，不跳转页面，避免干扰结算流程
                    // 清理无效的roomId
                    if (GlobalBean.GetInstance().loginData) {
                        GlobalBean.GetInstance().loginData.roomId = 0;
                    }
                    return;
                }

                if (messageBean.msgId != MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'))
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE)

                    // 清理无效的roomId，防止重复尝试重连
                    if (GlobalBean.GetInstance().loginData) {

                        GlobalBean.GetInstance().loginData.roomId = 0;
                    }
                }

                break;
            case ErrorCode.ErrNotFoundUser:// 没有找到玩家信息
                if (messageBean.msgId === MessageId.MsgTypeEnterRoom) {//只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE)
                }
                break
            case ErrorCode.ErrEnoughUser://房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'))
                break;
            case ErrorCode.ErrChangeBalance://扣除金币失败
            case ErrorCode.ErrNotEnoughCoin://金币不足
                this.topUpDialogController.show(() => { })
                break;
            case ErrorCode.ErrPlaying://玩家已经在游戏中了
                //执行一遍 enterroom
                if (!this.isReconnecting) {
                    this.isReconnecting = true;
                    const lastRoomID = GlobalBean.GetInstance().loginData.roomId;
                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {
                        lastRoomID: lastRoomID
                    });//重连进来的 玩家请求进入房间
                }
                break

        }

    }

    //长链接消息(正常)
    onMessage(messageBean: ReceivedMessageBean) {

        switch (messageBean.msgId) {
            case MessageId.MsgTypeCreateWs://创建ws连接 成功
                this.netError.active = false

                // 通知游戏网络已恢复
                let networkReconnectedMessage: AutoMessageBean = {
                    'msgId': 'NetworkReconnected',
                    'data': {}
                }
                GameMgr.Event.Send(EventType.AutoMessage, networkReconnectedMessage);

                //登录
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLogin, {});
                break;
            case MessageId.MsgTypeLogin://获取登录数据并存储

                GlobalBean.GetInstance().loginData = messageBean.data;

                // 根据roomId判断是否需要重置游戏或断线重连
                const roomId = GlobalBean.GetInstance().loginData.roomId;
               

                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin()
                } else {
                    this.hallPageController.updateGold()

                    // 先调用LoginSuccess，让它处理重连逻辑
                    this.hallPageController.LoginSuccess()

                    // 根据roomId判断处理逻辑
                    if (roomId > 0) {
                        // roomId不等于0，走断线重连逻辑（保持原有逻辑不变）
                       
                        if (!this.isReconnecting) {
                            this.isReconnecting = true;
                            const lastRoomID = roomId;

                            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {
                                lastRoomID: lastRoomID
                            });
                        }
                    } else {
                        // roomId等于0，走游戏重置逻辑
                       ``
                        this.resetGameOnLogin();
                    }
                }

                break;
            case MessageId.MsgTypePairRequest: //开始匹配
                // 检查响应码
                if (messageBean.code === 0) {
                    // 匹配请求成功，取消之前的结算延迟逻辑
                    if (this.settlementTimeoutId) {
                        clearTimeout(this.settlementTimeoutId);
                        this.settlementTimeoutId = null;
                    }

                    // 匹配请求成功，进入匹配页面
                    
                    this.hallPageController.setHallOrMatch(HallOrMatch.MATCH_PARENT)
                    this.hallPageController.createMatchView();
                } else if (messageBean.code === 6 && messageBean.msg === "玩家已经在匹配队列中") {
                    // 取消之前的结算延迟逻辑
                    if (this.settlementTimeoutId) {
                        clearTimeout(this.settlementTimeoutId);
                        this.settlementTimeoutId = null;
                    }

                    // 玩家已经在匹配队列中，直接进入匹配页面
                    
                    this.hallPageController.setHallOrMatch(HallOrMatch.MATCH_PARENT)
                    this.hallPageController.createMatchView();
                } else {
                    // 其他错误情况
                    console.warn(`匹配请求失败: code=${messageBean.code}, msg=${messageBean.msg}`);
                }
                break
            case MessageId.MsgTypeCancelPair: //取消匹配
                
                this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT)
                break
            case MessageId.MsgTypeGameStart: //游戏开始
                let noticeStartGame: NoticeStartGame = messageBean.data
                GlobalBean.GetInstance().noticeStartGame = noticeStartGame //存储游戏数据

                // 立即备份游戏开始数据，供后续结算对话框使用
                const gameStartDataBackup = JSON.parse(JSON.stringify(noticeStartGame));
                (window as any).tempGameStartData = gameStartDataBackup;

                const index = GlobalBean.GetInstance().noticeStartGame.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin
                }

                // 允许皮肤更换（GameStart到BattleStart之间）
                this.setSkinChangeAllowed(true);

                // 匹配成功后请求用户皮肤信息
                WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});

                // 如果是通过结算重置的游戏，强制显示匹配动画（无论房间类型）
                if (this.isGameResetFromSettlement) {
                    this.hallPageController.setGameData();
                    // 延迟转发GameStart消息，按原始匹配动画时间
                    this.scheduleOnce(() => {
                        let GameStart: AutoMessageBean = {
                            'msgId': AutoMessageId.GameStart,
                            'data': messageBean.data
                        }
                        GameMgr.Event.Send(EventType.AutoMessage, GameStart);
                        // 在GameStart消息转发后再重置标志，确保结算延迟逻辑能正确检查
                        this.isGameResetFromSettlement = false;
                    }, 0.87); // 延迟0.87秒，匹配原始动画时间
                } else if (noticeStartGame.roomType === RoomType.RoomTypeCommon) {// 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData()
                    // 立即转发 GameStart 消息到游戏场景
                    let GameStart: AutoMessageBean = {
                        'msgId': AutoMessageId.GameStart,
                        'data': messageBean.data
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, GameStart);
                } else {
                    // 在进入游戏页面之前先刷新游戏界面的用户数据
                    this.refreshGameMatchUserData();
                    this.setCurrentPage(PageType.GAME_PAGE) //开始游戏进入游戏页面

                    // 立即转发 GameStart 消息到游戏场景
                    let GameStart: AutoMessageBean = {
                        'msgId': AutoMessageId.GameStart,
                        'data': messageBean.data
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, GameStart);
                }

                break

            case MessageId.MsgTypeEnterRoom://重连的游戏数据
                this.isReconnecting = false; // 重置重连标志
                this.handleReconnectData(messageBean.data);
                break;
            case MessageId.MsgTypeLeaveRoom:// 玩家主动离开房间
                let inviteLeve: InviteKickOut = messageBean.data;
                if (inviteLeve.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 如果是主动退出，不立即跳转，等待Settlement消息处理
                    if (!globalActivelyLeavingFlag) {
                        GlobalBean.GetInstance().cleanData() //清空数据
                        let autoMessageBean: AutoMessageBean = {
                            'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息
                            'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                        }
                        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                    }
                }
                break;
            case MessageId.MsgTypeCreateInvite://创建邀请（也就是创建私人游戏房间）
                GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom()
                break
            case MessageId.MsgTypeAcceptInvite://接受邀请
                let acceptInvite: AcceptInvite = messageBean.data
                GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite)
                break
            case MessageId.MsgTypeLeaveInvite://收到离开房间的信息
                let noticeLeaveInvite: NoticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite)
                break
            case MessageId.MsgTypeInviteReady://收到有玩家准备的消息
                let noticeUserInviteStatus: NoticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus)
                break
            case MessageId.MsgTypeNoticeInviteStatus://广播邀请状态
                let noticeUserInviteStatus2: NoticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2)
                break;
            case MessageId.MsgTypeInviteKickOut://收到玩家被踢出的信息
                let inviteKickOut: InviteKickOut = messageBean.data;

                if (inviteKickOut.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'))
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE)
                } else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    let noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false }
                    this.hallPageController.leaveRoom(noticeLeaveInvite1)
                }
                break;
            case MessageId.MsgTypeUserInfo://更新用户信息的消息
                let userInfo: UserInfo = messageBean.data;
                GlobalBean.GetInstance().loginData.userInfo = userInfo
                this.hallPageController.updateGold()
                break;


            case MessageId.MsgTypeSettlement: //大结算
               

                // 修复Settlement数据结构问题
                const fixedSettlement = this.fixSettlementData(messageBean.data);

                // 处理余额变化并更新账户余额
                this.handleCoinChangeFromSettlement(fixedSettlement);

                // 确保在游戏页面显示结算对话框
                this.ensureGamePageAndShowSettlement(fixedSettlement, messageBean.data);

                break;

            case MessageId.NoticePlayerReady:
                let autoMessageBean: AutoMessageBean = {
                    'msgId': AutoMessageId.isReady,
                    'data': messageBean.data
                }
                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                break;

            case MessageId.BattleStart:
               
                // 禁止皮肤更换（战斗开始后不允许更换皮肤）
                this.setSkinChangeAllowed(false);

                let BattleStart: AutoMessageBean = {
                    'msgId': AutoMessageId.BattleStart,
                    'data': messageBean.data
                }
                GameMgr.Event.Send(EventType.AutoMessage, BattleStart);
                break;

            case MessageId.NoticeAttack:
                let NoticeAttack: AutoMessageBean = {
                    'msgId': AutoMessageId.NoticeAttack,
                    'data': messageBean.data
                }
                GameMgr.Event.Send(EventType.AutoMessage, NoticeAttack);
                break;

            case "NoticeSkinChange":
                let NoticeSkinChange: AutoMessageBean = {
                    'msgId': AutoMessageId.NoticeSkinChange,
                    'data': messageBean.data
                }
                GameMgr.Event.Send(EventType.AutoMessage, NoticeSkinChange);
                break;

            case MessageId.GetUserSkin: // 获取用户皮肤信息响应
                this.handleGetUserSkinResponse(messageBean.data);
                break;

            case MessageId.NoticeUserCoin: // 服务器主动推送金币变化
                this.handleNoticeUserCoin(messageBean.data);
                break;

            case MessageId.NoticeAutoManagedChange: // 托管状态变更广播
                this.handleNoticeAutoManagedChange(messageBean.data);
                break;

            case MessageId.CancelAutoManaged: // 取消托管响应
                this.handleCancelAutoManagedResponse(messageBean.data);
                break;
        }
    }

    // 处理获取用户皮肤信息响应
    private handleGetUserSkinResponse(data: any): void {
        if (!data) {
            return;
        }

        // 更新皮肤管理器
        const skinManager = SkinManager.getInstance();

        // 设置当前使用的皮肤
        if (data.currentSkinId !== undefined) {
            if (typeof skinManager.setCurrentSkin === 'function') {
                skinManager.setCurrentSkin(data.currentSkinId);
            }
        }

        // 设置拥有的皮肤列表
        if (data.ownedSkins && Array.isArray(data.ownedSkins)) {
            try {
                // 尝试设置拥有的皮肤
                if (typeof skinManager.setOwnedSkins === 'function') {
                    skinManager.setOwnedSkins(data.ownedSkins);
                } else if (skinManager['ownedSkins'] !== undefined) {
                    skinManager['ownedSkins'] = data.ownedSkins;
                } else {
                    // 备用方案：存储到全局变量
                    (window as any).tempUserSkinData = {
                        ownedSkins: data.ownedSkins,
                        currentSkinId: data.currentSkinId
                    };
                }
            } catch (error) {
                return;
            }
        }

        // 通知ItemController更新UI（无论当前在哪个页面）
        // 总是尝试更新大厅页面的UI，即使当前不在大厅页面
        const itemController = this.hallPageController?.itemController;
        if (itemController && itemController.onGetUserSkinResponse) {
            itemController.onGetUserSkinResponse(data);

            // 更新choose节点显示
            if (data.currentSkinId !== undefined && itemController.showChooseNode) {
                itemController.showChooseNode(data.currentSkinId);
            }

            // 同时通知ItemBuyController更新价格节点
            const itemBuyController = itemController.itemBuyController;
            if (itemBuyController) {
                if (itemBuyController.onGetUserSkinResponse) {
                    itemBuyController.onGetUserSkinResponse(data);
                }
                // 强制刷新价格节点
                if (itemBuyController.forceRefreshPriceNodes) {
                    itemBuyController.forceRefreshPriceNodes();
                }
            }
        }
    }

    // 存储结算延迟的定时器ID（公开访问，供结算弹窗取消定时器使用）
    public settlementTimeoutId: any = null;

    // 刷新游戏界面的用户数据
    private refreshGameMatchUserData(): void {
        try {
            // 查找GameMatch组件并刷新数据
            const gameMatchComponent = this.gamePage.getComponentInChildren('GameMatch');
            if (gameMatchComponent && gameMatchComponent.refreshUserData) {
                gameMatchComponent.refreshUserData();
            }
        } catch (error) {
            console.error("GlobalManagerController: 刷新GameMatch用户数据时出错:", error);
        }
    }

    // 处理断线重连数据
    private handleReconnectData(reconnectData: ReconnectGameData): void {
        
        // 详细记录攻击历史数据
        if (reconnectData.attackHistory) {
          
            reconnectData.attackHistory.forEach((attack, index) => {
                console.log(`GlobalManagerController: 攻击记录 ${index}:`, {
                    attackerId: attack.attackerId,
                    x: attack.x,
                    y: attack.y,
                    hit: attack.hit,
                    sunk: attack.sunk,
                    shipId: attack.shipId
                });
            });
        } else {
     
        }

        // 检查游戏状态，如果游戏已结束则直接处理
        if (reconnectData.gameStatus === 4) {
            
            this.restoreGameEndState(reconnectData);
            return; // 游戏已结束，不需要继续后续的游戏页面处理
        }

        // 检查是否有无效的游戏状态（可能是数据异常）
        if (!reconnectData.gameStatus || reconnectData.gameStatus < 1 || reconnectData.gameStatus > 4) {
            console.warn("GlobalManagerController: 检测到无效的游戏状态", reconnectData.gameStatus, "，返回大厅");
            this.restoreGameEndState(reconnectData);
            return;
        }

        // 1. 恢复关键数据到GlobalBean
        if (reconnectData.roomId && GlobalBean.GetInstance().loginData) {
            
            GlobalBean.GetInstance().loginData.roomId = reconnectData.roomId;
        }
        GlobalBean.GetInstance().noticeStartGame = reconnectData;

        // 2. 先跳转到游戏页面，确保MainBattle组件被加载
        this.setCurrentPage(PageType.GAME_PAGE);

        // 3. 等待一帧，确保组件完全加载后再发送消息
        this.scheduleOnce(() => {
            // 4. 重置场景状态
            this.resetSceneForReconnect();

            // 5. 根据游戏状态恢复场景
            this.restoreGameStateFromReconnect(reconnectData);

            // 6. 检查并处理托管状态
            this.checkAndHandleAutoManagedStateOnReconnect(reconnectData);
        }, 0.1);

        
    }

    // 重置场景为断线重连准备
    private resetSceneForReconnect(): void {
        // 只发送一次重置消息，避免重复处理
        // 两个组件都会监听同一个消息，但各自处理自己的重置逻辑
        let resetMessage: AutoMessageBean = {
            'msgId': 'ResetForReconnect',
            'data': {}
        }
        GameMgr.Event.Send(EventType.AutoMessage, resetMessage);
    }

    // 根据重连数据恢复游戏状态
    private restoreGameStateFromReconnect(reconnectData: ReconnectGameData): void {


        // 根据gameStatus恢复不同阶段的游戏状态
        switch (reconnectData.gameStatus) {
            case 1: // 等待中
                this.restoreWaitingState(reconnectData);
                break;
            case 2: // 布置战舰阶段
                this.restoreDeploymentState(reconnectData);
                break;
            case 3: // 战斗阶段
                this.restoreBattleState(reconnectData);
                break;
            case 4: // 游戏结束
                this.restoreGameEndState(reconnectData);
                break;
            default:
                console.warn("GlobalManagerController: 未知的游戏状态", reconnectData.gameStatus);
                break;
        }
    }

    // 恢复等待状态
    private restoreWaitingState(reconnectData: ReconnectGameData): void {

        // 等待状态允许皮肤更换
        this.setSkinChangeAllowed(true);

        // 处理用户皮肤信息
        this.updatePlayerSkinsFromReconnectData(reconnectData);

        // 重连时请求用户皮肤信息，更新拥有状态
        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});

        // 发送恢复等待状态的消息
        let restoreMessage: AutoMessageBean = {
            'msgId': 'RestoreWaitingState',
            'data': reconnectData
        }
        GameMgr.Event.Send(EventType.AutoMessage, restoreMessage);
    }

    // 恢复布置战舰状态
    private restoreDeploymentState(reconnectData: ReconnectGameData): void {


        // 布置阶段允许皮肤更换
        this.setSkinChangeAllowed(true);

        // 处理用户皮肤信息
        this.updatePlayerSkinsFromReconnectData(reconnectData);

        // 重连时请求用户皮肤信息，更新拥有状态
        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});

        // 发送恢复布置状态的消息给BattleshipGame
        let restoreMessage: AutoMessageBean = {
            'msgId': 'RestoreDeploymentState',
            'data': reconnectData
        }
        GameMgr.Event.Send(EventType.AutoMessage, restoreMessage);
    }

    // 根据重连数据更新玩家皮肤信息
    private updatePlayerSkinsFromReconnectData(reconnectData: ReconnectGameData): void {
        if (!reconnectData.users || reconnectData.users.length === 0) {
            console.warn("GlobalManagerController: 重连数据中没有用户信息，无法更新皮肤");
            return;
        }

        const skinManager = SkinManager.getInstance();
        const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;

        // 遍历所有用户，更新皮肤信息
        reconnectData.users.forEach(user => {
            if (user.skinId && user.skinId >= 2100 && user.skinId <= 2102) {
                if (user.userId === myUserId) {
                    // 更新自己的皮肤
               
                    skinManager.setCurrentSkin(user.skinId);
                } else {
                    // 更新对手的皮肤
                   
                    skinManager.setOpponentSkin(user.skinId);
                }
            } else {
                console.warn(`GlobalManagerController: 用户 ${user.userId} 的皮肤ID无效: ${user.skinId}`);
            }
        });

        // 刷新所有船只的皮肤显示
        ShipController.refreshAllShips();
    }

    // 恢复战斗状态
    private restoreBattleState(reconnectData: ReconnectGameData): void {

        // 战斗阶段禁止皮肤更换
        this.setSkinChangeAllowed(false);

        // 处理用户皮肤信息（即使在战斗阶段也需要恢复正确的皮肤显示）
        this.updatePlayerSkinsFromReconnectData(reconnectData);

        // 重连时请求用户皮肤信息，更新拥有状态（虽然不能更换，但需要正确显示UI）
        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});

        // 首先发送恢复战斗状态的消息给BattleshipGame，设置正确的棋盘位置
        let restoreBattleshipMessage: AutoMessageBean = {
            'msgId': 'RestoreBattleStateForBattleship',
            'data': reconnectData
        }
        GameMgr.Event.Send(EventType.AutoMessage, restoreBattleshipMessage);

        // 然后发送恢复战斗状态的消息给MainBattle
        let restoreMessage: AutoMessageBean = {
            'msgId': 'RestoreBattleState',
            'data': reconnectData
        }
        GameMgr.Event.Send(EventType.AutoMessage, restoreMessage);
    }

    // 恢复游戏结束状态
    private restoreGameEndState(_reconnectData: ReconnectGameData): void {


        // 游戏结束状态禁止皮肤更换
        this.setSkinChangeAllowed(false);

        // 游戏已结束且结算已完成，清理游戏数据并返回大厅
        // 不需要跳转到游戏页面，直接处理结算后的清理工作

        // 清理全局游戏数据
        GlobalBean.GetInstance().cleanData();

        // 清理临时备份数据
        if ((window as any).tempGameStartData) {
            delete (window as any).tempGameStartData;
        }

        // 确保停止所有可能的匹配状态
        this.stopMatchingProcess();

        // 直接跳转到大厅页面，不显示游戏界面
        this.setCurrentPage(PageType.HALL_PAGE);

        // 确保大厅页面状态正确
        if (this.hallPageController) {
            this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT);
            this.hallPageController.updateGold();

            // 确保匹配页面状态被重置
            if (this.hallPageController.matchParentController) {
                this.hallPageController.matchParentController.resetMatchState();
            }
        }

        // 显示提示信息
        this.toastController.showContent("游戏已结束，已返回大厅");


    }

    // 登录时的游戏重置逻辑 - 用于处理断网后收不到大结算通知的情况
    private resetGameOnLogin(): void {
       

        // 允许皮肤更换
        this.setSkinChangeAllowed(true);

        // 清理全局游戏数据
        GlobalBean.GetInstance().cleanData();

        // 清理临时备份数据
        if ((window as any).tempGameStartData) {
            delete (window as any).tempGameStartData;
        }

        // 清理结算相关的定时器
        if (this.settlementTimeoutId) {
            clearTimeout(this.settlementTimeoutId);
            this.settlementTimeoutId = null;
        }

        // 重置结算标志
        this.isGameResetFromSettlement = false;

        // 确保停止所有可能的匹配状态
        this.stopMatchingProcess();

        // 如果当前在游戏页面，跳转到大厅页面
        if (this.currentPage === PageType.GAME_PAGE) {
            this.setCurrentPage(PageType.HALL_PAGE);
        }

        // 确保大厅页面状态正确
        if (this.hallPageController) {
            this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT);
            this.hallPageController.updateGold();

            // 确保匹配页面状态被重置
            if (this.hallPageController.matchParentController) {
                this.hallPageController.matchParentController.resetMatchState();
            }
        }

        // 发送游戏重置消息，清理游戏场景中的状态
        let resetGameMessage: AutoMessageBean = {
            'msgId': 'ResetGameFromLogin',
            'data': {}
        }
        GameMgr.Event.Send(EventType.AutoMessage, resetGameMessage);

       
    }

    // 停止匹配过程
    private stopMatchingProcess(): void {
       

        // 清理可能的匹配定时器
        if (this.hallPageController && this.hallPageController.matchParentController) {
            const matchController = this.hallPageController.matchParentController;

            // 停止匹配相关的定时器
            if (matchController.normalTime) {
                matchController.unschedule(matchController.normalTime);
                matchController.normalTime = null;
            }

            // 重置匹配状态
            matchController.resetMatchState();
        }
    }

    // 处理Settlement数据 - 严格按照后端返回的数据，不添加任何前端逻辑
    private fixSettlementData(rawData: any): NoticeSettlement {
       

        // 如果后端返回的数据结构正常，直接使用
        if (rawData && rawData.users && Array.isArray(rawData.users)) {
           

            // 只进行基本的字段标准化，不修改任何业务逻辑
            const processedUsers = rawData.users.map((user: any, index: number) => {
                const processedUser = {
                    userId: user.userId || user.user_id || `unknown_user_${index}`,
                    coinChg: this.getValidNumber(user.coinChg || user.coinChange || user.coin_change, 0),
                    coin: user.currentCoin || 0, // 直接使用currentCoin字段，不通过getValidNumber
                    rank: this.getValidNumber(user.rank, 0), // 严格使用后端返回的rank，不修改
                    score: this.getValidNumber(user.score, 0)
                };

              

                return processedUser;
            });

           
            return { users: processedUsers };
        }

        // 如果后端数据结构异常，返回空数据而不是自己创造数据
        console.error("GlobalManagerController: 后端Settlement数据结构异常，无法处理", rawData);
        return { users: [] };
    }



    // 获取有效的数字值
    private getValidNumber(value: any, defaultValue: number): number {
        if (typeof value === 'number' && !isNaN(value)) {
            return value;
        }
        if (typeof value === 'string') {
            const parsed = parseFloat(value);
            if (!isNaN(parsed)) {
                return parsed;
            }
        }
        return defaultValue;
    }



    // 处理NoticeUserCoin消息 - 服务器主动推送金币变化
    private handleNoticeUserCoin(data: any): void {
        // 根据api.md，NoticeUserCoin响应参数包含：coin字段（Number类型）
        // 直接使用服务器返回的最终金币数量，不做任何计算
        if (data.coin !== undefined && data.coin !== null && typeof data.coin === 'number') {
            // 更新全局账户余额为服务器返回的最终值
            GlobalBean.GetInstance().loginData.userInfo.coin = data.coin;

            // 立即更新UI显示，无论在哪个页面
            if (this.currentPage === PageType.HALL_PAGE) {
                // 在大厅页面，更新金币显示
                this.hallPageController.updateGold();
            }
        }
    }

    // 处理NoticeAutoManagedChange消息 - 托管状态变更广播
    private handleNoticeAutoManagedChange(data: any): void {
        

       
        // userId: String - 状态变更的玩家ID
        // nickName: String - 玩家昵称
        // isAutoManaged: Boolean - 新的托管状态
        // reason: String - 状态变更原因

        if (!data.userId || data.isAutoManaged === undefined) {
            console.warn("GlobalManagerController: 托管状态变更数据不完整", data);
            return;
        }

        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;

        // 如果是当前玩家的托管状态变更
        if (data.userId === currentUserId) {
            if (data.isAutoManaged) {
                // 进入托管状态，显示托管UI
                this.showAutoManagedDialog(data.userId, data.nickName, data.reason);
            } else {
                // 取消托管状态，隐藏托管UI
                this.hideAutoManagedDialog();
            }
        }

        // 转发消息给游戏组件处理（如果需要在游戏界面显示其他玩家的托管状态）
        let autoMessageBean: AutoMessageBean = {
            'msgId': AutoMessageId.NoticeAutoManagedChange,
            'data': data
        }
        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
    }

    // 处理CancelAutoManaged响应
    private handleCancelAutoManagedResponse(data: any): void {
       

        // 根据api.md，CancelAutoManaged响应参数包含：
        // success: Boolean - 是否成功取消托管
        // message: String - 响应消息

        if (data.success) {
            // 取消托管成功，隐藏托管UI
            this.hideAutoManagedDialog();
            // 显示成功提示
            if (data.message) {
                this.toastController.showContent(data.message);
            }
        } else {
            // 取消托管失败，显示错误信息
            if (data.message) {
                this.toastController.showContent(data.message);
            }
        }
    }

    // 显示托管对话框
    private showAutoManagedDialog(userId: string, nickName: string, reason: string): void {
       

        // 只有在游戏页面才显示托管对话框
        if (this.currentPage === PageType.GAME_PAGE && this.gamePageController) {
            this.gamePageController.showAutoManagedDialog(userId, nickName, reason);
        }
    }

    // 隐藏托管对话框
    private hideAutoManagedDialog(): void {
       
        if (this.currentPage === PageType.GAME_PAGE && this.gamePageController) {
            this.gamePageController.hideAutoManagedDialog();
        }
    }

    // 断线重连时检查并处理托管状态
    private checkAndHandleAutoManagedStateOnReconnect(reconnectData: ReconnectGameData): void {
       

        // 只在战斗阶段检查托管状态
        if (reconnectData.gameStatus !== 3) {
            
            return;
        }

        // 检查playerStats中的托管状态
        if (!reconnectData.playerStats) {
           
            return;
        }

        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;
        if (!currentUserId) {
            console.warn("GlobalManagerController: 无法获取当前用户ID，跳过托管状态检查");
            return;
        }

        const playerStats = reconnectData.playerStats[currentUserId];
        if (!playerStats) {
           
            return;
        }

        // 检查是否处于托管状态
        if (playerStats.isAutoManaged === true) {
            

            // 查找当前玩家信息以获取昵称
            const currentUser = reconnectData.users?.find(user => user.userId === currentUserId);
            const nickName = currentUser ? currentUser.nickName : "未知玩家";

            // 延迟显示托管对话框，确保游戏页面已完全加载
            this.scheduleOnce(() => {
                this.showAutoManagedDialog(currentUserId, nickName, "断线重连时恢复托管状态");
            }, 0.5);
        } else {
           
        }
    }



    // 处理Settlement消息中的余额变化
    private handleCoinChangeFromSettlement(noticeSettlement: NoticeSettlement): void {
        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        const currentUserSettlement = noticeSettlement.users.find(user => user.userId === currentUserId);

        if (currentUserSettlement) {
            // 更新全局账户余额
            GlobalBean.GetInstance().loginData.userInfo.coin = currentUserSettlement.coin;

            // 通知大厅页面更新金币显示（如果当前在大厅页面）
            if (this.currentPage === PageType.HALL_PAGE) {
                this.hallPageController.updateGold();
            }
        }
    }

    // 确保在游戏页面显示结算对话框
    private ensureGamePageAndShowSettlement(fixedSettlement: any, settlementData: any): void {
       

        // 如果当前不在游戏页面，先切换到游戏页面
        if (this.currentPage !== PageType.GAME_PAGE) {
           
            this.setCurrentPage(PageType.GAME_PAGE);
        }

        // 确保gamePageController存在，如果不存在则尝试重新获取
        if (!this.gamePageController && this.gamePage) {
            
            this.gamePageController = this.gamePage.getComponent(GamePageController);
        }

        if (!this.gamePageController) {
            console.error("GlobalManagerController: gamePageController未初始化，无法显示结算对话框");
            // 如果gamePageController不存在，直接跳转到大厅页面
            this.handleSettlementWithoutDialog(settlementData);
            return;
        }

        // 等待一帧确保页面完全激活后再显示结算
        this.scheduleOnce(() => {
            // 如果是主动退出，立即显示结算
            if (globalActivelyLeavingFlag) {
                this.showSettlementImmediately(fixedSettlement, settlementData);
            } else {
                // 等待沉船动画完成后再显示结算对话框和清理预制体
                this.waitForSunkShipAnimationAndShowSettlement(fixedSettlement, settlementData);
            }
        }, 0.1);
    }

    // 立即显示结算（主动退出时使用）
    private showSettlementImmediately(fixedSettlement: any, settlementData: any): void {
        // 清理指定的预制体
        let cleanSpecificMessage: AutoMessageBean = {
            'msgId': 'CleanSpecificMarkers',
            'data': {}
        }
        GameMgr.Event.Send(EventType.AutoMessage, cleanSpecificMessage);

        // 显示结算对话框
        this.gamePageController.setCongratsDialog(fixedSettlement);

        // 执行完整的游戏重置流程
        this.handleGameSettlement(settlementData);

        // 重置主动退出标志
        globalActivelyLeavingFlag = false;
    }

    // 延迟显示结算对话框
    private waitForSunkShipAnimationAndShowSettlement(fixedSettlement: any, settlementData: any): void {
        // 根据是否主动退出决定延迟时间
        const delay = globalActivelyLeavingFlag ? 0.0 : 2.0;

        this.scheduleOnce(() => {
            // 清理指定的预制体
            let cleanSpecificMessage: AutoMessageBean = {
                'msgId': 'CleanSpecificMarkers',
                'data': {}
            }
            GameMgr.Event.Send(EventType.AutoMessage, cleanSpecificMessage);

            // 显示结算对话框
            this.gamePageController.setCongratsDialog(fixedSettlement);

            // 执行完整的游戏重置流程
            this.handleGameSettlement(settlementData);

            // 如果是主动退出，在结算处理完成后执行页面跳转
            if (globalActivelyLeavingFlag) {
               
                GlobalBean.GetInstance().cleanData(); //清空数据
                let autoMessageBean: AutoMessageBean = {
                    'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息
                    'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                }
                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
            }

            // 重置主动退出标志
            globalActivelyLeavingFlag = false;
        }, delay);
    }

    // 处理无法显示结算对话框的情况
    private handleSettlementWithoutDialog(settlementData: any): void {
        

        // 执行游戏重置流程
        this.handleGameSettlement(settlementData);

        // 延迟跳转到大厅页面
        this.scheduleOnce(() => {
            
            GlobalBean.GetInstance().cleanData();
            this.setCurrentPage(PageType.HALL_PAGE);
            if (this.hallPageController) {
                this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT);
                this.hallPageController.updateGold();
            }
        }, 1.0);
    }

    // 处理游戏结算和重置
    private handleGameSettlement(data: any): void {
        // 游戏结束，禁止皮肤更换
        this.setSkinChangeAllowed(false);

        // 标记这是通过正常结算流程重置的游戏
        this.isGameResetFromSettlement = true;

        // 发送游戏结束消息给MainBattle进行清理
        let gameOverMessage: AutoMessageBean = {
            'msgId': 'GameOver',
            'data': data
        }
        GameMgr.Event.Send(EventType.AutoMessage, gameOverMessage);

        // 发送重置游戏状态的消息给BattleshipGame
        let resetGameMessage: AutoMessageBean = {
            'msgId': 'HideBattleshipAndPrepareCreatShip',
            'data': {}
        }
        GameMgr.Event.Send(EventType.AutoMessage, resetGameMessage);


        // 设置10秒延迟清理，与结算弹窗倒计时同步
        this.settlementTimeoutId = setTimeout(() => {
            // 如果用户已经开始新游戏（结算重置标志被清除），则不执行清理
            if (this.isGameResetFromSettlement === false) {
                return;
            }

            // 清理全局游戏数据
            GlobalBean.GetInstance().cleanData();

            // 清理临时备份数据
            if ((window as any).tempGameStartData) {
                delete (window as any).tempGameStartData;
            }

            // 跳转到大厅页面
            this.setCurrentPage(PageType.HALL_PAGE);

            // 确保大厅页面状态正确
            if (this.hallPageController) {
                this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT);
                this.hallPageController.updateGold();
            }

            // 清除定时器ID
            this.settlementTimeoutId = null;
        }, 10000); // 改为10秒延迟，与结算弹窗倒计时同步
    }

    // 检查是否允许更换皮肤 - 修改为始终允许
    public isSkinChangeAllowed(): boolean {
        // 去掉皮肤更换限制，任何时候都可以换皮肤
        return true;
    }

    // 设置主动退出标志
    public setActivelyLeaving(value: boolean): void {
        globalActivelyLeavingFlag = value;
    }

    // 设置皮肤更换允许状态 - 现在始终允许，保留方法以避免破坏现有调用
    private setSkinChangeAllowed(_allowed: boolean): void {
        // 不再需要设置状态，因为皮肤更换始终允许
    }





    //设置展示页面的
    setCurrentPage(pageType: PageType) {
        this.currentPage = pageType
        this.startUpPage.active = false
        this.hallPage.active = false
        this.gamePage.active = false

        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true
                break
            case PageType.HALL_PAGE:
                this.hallPage.active = true
                // 切换到大厅页面时，请求用户皮肤信息以更新UI状态
                this.scheduleOnce(() => {
                    const loginData = GlobalBean.GetInstance().loginData;
                    if (loginData && loginData.roomId > 0) {
                        // 有房间上下文时才请求皮肤信息
                        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});
                    }
                }, 0.1);
                break
            case PageType.GAME_PAGE:
                this.gamePage.active = true
                break
        }

    }

    // update (dt) {}
}

if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;

        // 确保 spriteFrame 和其纹理存在
        if (this.spriteFrame && this.spriteFrame.getTexture()) {
            this.spriteFrame.getTexture().setPremultiplyAlpha(true);
        } else {
            console.warn("SpriteFrame or Texture is null");
        }
    }

    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    }
    cc.macro.ALLOW_IMAGE_BITMAP = false;// 禁用 Bitmap 图片格式


}