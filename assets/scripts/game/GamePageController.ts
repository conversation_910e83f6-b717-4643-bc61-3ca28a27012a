// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { NoticeMoveBlock, NoticeScoreChg, NoticeSettlement } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import LeaveDialogController from "../hall/LeaveDialogController";
import SettingDialogController from "../hall/SettingDialogController";
import { AudioManager } from "../util/AudioManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";
import CongratsDialogController from "./CongratsDialogController";
import AutoManagedDialogController from "./AutoManagedDialogController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GamePageController extends cc.Component {

    @property(cc.Node)
    boardBtnBack: cc.Node = null //返回按钮

    @property(cc.Node)
    settingBtn: cc.Node = null

   
    @property(LeaveDialogController)
    leaveDialogController: LeaveDialogController = null // 退出游戏弹窗
    
    @property(CongratsDialogController)
    congratsDialogController: CongratsDialogController = null //结算弹窗

    @property(SettingDialogController)
    settingDialogController: SettingDialogController = null;

    @property(AutoManagedDialogController)
    autoManagedDialogController: AutoManagedDialogController = null; // 托管对话框

    isLeaveGameDialogShow: boolean = false;  //是否显示退出游戏的弹窗
    isCongratsDialog: boolean = false;  //是否显示结算的弹窗
    isSettingDialog: boolean = false;
    isAutoManagedDialog: boolean = false; //是否显示托管的弹窗


    // onLoad () {}

    protected onEnable(): void {
        // 重置页面中所有按钮到普通状态，解决长按按钮后页面切换导致的按钮卡住问题
        Tools.resetAllButtonsInNode(this.node);
    }

    protected start(): void {
       

        // 检查返回按钮
        if (this.boardBtnBack) {
            Tools.imageButtonClick(this.boardBtnBack, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {
                this.isLeaveGameDialogShow = true
                this.leaveDialogController.show(1,()=>{
                    this.isLeaveGameDialogShow = false
                })
            });
        } else {
            console.error("GamePageController: boardBtnBack 未设置，请在编辑器中绑定节点");
        }

        // 检查设置按钮
        if (this.settingBtn) {
            Tools.imageButtonClick(this.settingBtn, Config.buttonRes + 'board_btn_setting_normal', Config.buttonRes + 'board_btn_setting_pressed', () => {
                this.isSettingDialog = true;
                this.settingDialogController.show(()=>{
                    this.isSettingDialog = false
                })
            });
        } else {
            console.error("GamePageController: settingBtn 未设置，请在编辑器中绑定节点");
        }
    }
    

    //结算
    setCongratsDialog(noticeSettlement: NoticeSettlement) {
       
        // 检查congratsDialogController是否存在
        if (!this.congratsDialogController) {
            console.error("GamePageController: congratsDialogController未设置，无法显示结算对话框");
            return;
        }

        this.setCongrats(noticeSettlement)

        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow && this.leaveDialogController) {
            this.leaveDialogController.hide()
        }

        this.isCongratsDialog = true
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, () => {
            this.isCongratsDialog = false
        })

    }

    protected onDisable(): void {
      

        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
           
            this.leaveDialogController.hide()
        }

        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            
            this.congratsDialogController.hide()
        }

        //托管弹窗正在显示的话就先关闭掉
        if (this.isAutoManagedDialog) {
            
            this.autoManagedDialogController.hide()
        }
    }


    //结算
    setCongrats(noticeSettlement: NoticeSettlement) {

        const index = noticeSettlement.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (noticeSettlement.users[index].rank === 1) { //判断自己是不是第一名
                // 取消胜利音效播放
                // AudioManager.winAudio();
            } else {
                // 取消失败音效播放
                // AudioManager.loseAudio();
            }
        } else {
            // 取消胜利音效播放
            // AudioManager.winAudio();
        }

    }

    //显示托管对话框
    showAutoManagedDialog(userId: string, nickName: string, reason: string) {
       

        // 检查autoManagedDialogController是否存在
        if (!this.autoManagedDialogController) {
            console.error("GamePageController: autoManagedDialogController未设置，无法显示托管对话框");
            return;
        }

        //其他弹窗正在显示的话就先关闭
        if (this.isLeaveGameDialogShow && this.leaveDialogController) {
            this.leaveDialogController.hide()
        }

        if (this.isCongratsDialog && this.congratsDialogController) {
            this.congratsDialogController.hide()
        }

        if (this.isSettingDialog && this.settingDialogController) {
            this.settingDialogController.hide()
        }

        this.isAutoManagedDialog = true
        //弹出托管弹窗
        this.autoManagedDialogController.show(userId, nickName, reason, () => {
            this.isAutoManagedDialog = false
        })
    }

    //隐藏托管对话框
    hideAutoManagedDialog() {
      
        if (this.isAutoManagedDialog && this.autoManagedDialogController) {
            this.autoManagedDialogController.hide()
            this.isAutoManagedDialog = false
        }
    }

    // update (dt) {}
}
