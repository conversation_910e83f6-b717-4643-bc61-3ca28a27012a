// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

/**
 * 托管状态对话框控制器
 * 当玩家处于托管状态时显示，只显示取消托管按钮
 */
@ccclass
export default class AutoManagedDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null; // 对话框背景

    // 移除文本标签，托管页面只需要按钮

    @property(cc.Node)
    cancelAutoManagedBtn: cc.Node = null; // 取消托管按钮

    @property(cc.Node)
    buttonLabel: cc.Node = null; // 按钮文字节点（直接挂载，不用查找）



    private backCallback: Function = null; // 隐藏弹窗的回调
    private currentUserId: string = null; // 当前托管的用户ID
    private currentNickName: string = null; // 当前托管的用户昵称

    // onLoad () {}

    start() {
        // 取消托管按钮点击事件
        if (this.cancelAutoManagedBtn) {
            if (this.buttonLabel) {
                // 使用自定义按压效果（直接使用挂载的文字节点）
                this.setupCustomButtonEffect();
            } else {
                // 使用简单的点击事件
                this.cancelAutoManagedBtn.on(cc.Node.EventType.TOUCH_END, () => {
                    this.onCancelAutoManagedClick();
                }, this);
            }
        } else {
            console.error("AutoManagedDialogController: cancelAutoManagedBtn未设置，请在编辑器中绑定节点");
        }
    }

    /**
     * 设置自定义按钮按压效果（使用直接挂载的文字节点）
     */
    private setupCustomButtonEffect() {
        if (!this.buttonLabel) {
            console.error("AutoManagedDialogController: buttonLabel未设置，请在编辑器中绑定文字节点");
            return;
        }

        const label = this.buttonLabel.getComponent(cc.Label);
        if (!label) {
            console.error("AutoManagedDialogController: buttonLabel 没有 Label 组件");
            return;
        }

        // 存储原始状态
        const originalFontSize = label.fontSize;
        const originalColor = this.buttonLabel.color.clone();
        const originalScale = this.cancelAutoManagedBtn.scale;

        // 设置触摸事件
        Tools.setTouchEvent(this.cancelAutoManagedBtn,
            // 按下时的效果
            (node: cc.Node) => {
                // 文字颜色变成 #BEBEBE 100%
                this.buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#BEBEBE');
                // 字号变成 30
                label.fontSize = 30;
                label.lineHeight = 30;
                // 按钮缩小（根据字体大小比例缩放）
                const scaleRatio = 30 / originalFontSize;
                node.scale = originalScale * scaleRatio;
            },
            // 抬起时的效果（点击完成）
            (node: cc.Node) => {
                // 恢复原始状态
                this.buttonLabel.color = originalColor;
                label.fontSize = originalFontSize;
                label.lineHeight = originalFontSize;
                node.scale = originalScale;
                // 执行点击回调
                this.onCancelAutoManagedClick();
            },
            // 取消时的效果（手指移出按钮区域）
            (node: cc.Node) => {
                // 恢复原始状态
                this.buttonLabel.color = originalColor;
                label.fontSize = originalFontSize;
                label.lineHeight = originalFontSize;
                node.scale = originalScale;
            }
        );
    }

    /**
     * 显示托管对话框
     * @param userId 托管的用户ID
     * @param nickName 托管的用户昵称
     * @param reason 托管原因
     * @param backCallback 隐藏弹窗的回调
     */
    show(userId: string, nickName: string, reason: string, backCallback?: Function) {
        this.currentUserId = userId;
        this.currentNickName = nickName;
        this.backCallback = backCallback;

        // 显示对话框（只显示取消托管按钮，不显示文本）
        if (this.node.active === false) {
            this.node.active = true;
            if (this.boardBg) {
                this.boardBg.scale = 0;
                // 执行缩放动画
                cc.tween(this.boardBg)
                    .to(Config.dialogScaleTime, { scale: 1 })
                    .start();
            }
        }
    }

    /**
     * 隐藏托管对话框
     */
    hide() {
        if (this.backCallback) {
            this.backCallback();
        }

        if (this.boardBg) {
            // 执行缩放动画
            cc.tween(this.boardBg)
                .to(Config.dialogScaleTime, { scale: 0 })
                .call(() => {
                    this.node.active = false;
                    // 清理数据
                    this.currentUserId = null;
                    this.currentNickName = null;
                    this.backCallback = null;
                })
                .start();
        } else {
            this.node.active = false;
            // 清理数据
            this.currentUserId = null;
            this.currentNickName = null;
            this.backCallback = null;
        }
    }

    /**
     * 取消托管按钮点击处理
     */
    private onCancelAutoManagedClick() {
        // 发送取消托管消息到服务器
        WebSocketManager.GetInstance().sendMsg(MessageId.CancelAutoManaged, {});

        // 隐藏对话框
        this.hide();
    }

    /**
     * 检查当前是否显示中
     */
    public isShowing(): boolean {
        return this.node && this.node.active;
    }

    /**
     * 获取当前托管的用户ID
     */
    public getCurrentUserId(): string {
        return this.currentUserId;
    }

    // update (dt) {}
}
