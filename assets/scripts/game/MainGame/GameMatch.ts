import { RoomUser, UserInfo } from "../../bean/GameBean";
import { GlobalBean } from "../../bean/GlobalBean";
import MatchItemController from "../../pfb/MatchItemController";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameMatch extends cc.Component {
    [x: string]: any;

   @property(cc.Layout)
   matchUserLay: cc.Layout = null;
   @property(cc.Prefab)
   matchItem: cc.Prefab = null;

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start () {
        this.initializeUserData();
    }

    // 初始化用户数据
    private initializeUserData() {
        let user_s: RoomUser[] = [];
        this._userListCol = [];

        let peopleNumber: number = GlobalBean.GetInstance().players;
        // 强制设置人数为 2
        //peopleNumber = 2;

        // 从 GlobalBean 中获取用户信息
        const globalUsers = GlobalBean.GetInstance().noticeStartGame.users;

        // 生成用户数据数组
        for (let i = 0; i < Math.min(peopleNumber, globalUsers.length); i++) {
            const globalUser = globalUsers[i];
            const user: RoomUser = {
                userId: globalUser.userId,
                nickName: globalUser.nickName,
                avatar: globalUser.avatar,
                pos: i,
                coin: globalUser.coin,
                status: globalUser.status,
                score: globalUser.score,
                rank: globalUser.rank
            };
            user_s.push(user);
        }

        // 清理旧的子节点
        this.matchUserLay.node.removeAllChildren();

        // 实例化预制体并添加到布局节点
        for (let i = 0; i < user_s.length; i++) {
            const item = cc.instantiate(this.matchItem);
            this.matchUserLay.node.addChild(item);
            let matchingItemController = item.getComponent(MatchItemController);
            this._userListCol.push(matchingItemController);
            matchingItemController.setData(user_s[i]);
        }
    }

    // 刷新用户数据（供外部调用）
    public refreshUserData() {
        this.initializeUserData();
    }

    // update (dt) {}
}
