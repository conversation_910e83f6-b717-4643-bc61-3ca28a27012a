// SeaBattleGrid.ts
// 海战游戏网格管理脚本，实现10x10网格及触摸高亮效果

/**
 * 海战游戏网格管理器
 * 负责处理触摸事件并显示高亮效果
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class SeaBattleGrid extends cc.Component {
    // 行高亮图片资源(10*1长条)
    @property(cc.SpriteFrame)
    rowHighlightSpriteFrame: cc.SpriteFrame = null;

    // 列高亮图片资源(10*1长条)
    @property(cc.SpriteFrame)
    columnHighlightSpriteFrame: cc.SpriteFrame = null;

    // 现有10*10网格图片节点
    @property(cc.Node)
    existingGridNode: cc.Node = null;

    // 网格属性
    private gridSize: number = 10;        // 10x10网格
    private cellSize: cc.Size = null;     // 单元格大小
    private gridTotalSize: cc.Size = cc.size(644, 644); // 网格总尺寸
    
    // 当前高亮位置
    private currentRow: number = -1;
    private currentCol: number = -1;
    
    // 动态创建的高亮精灵
    private rowHighlightSprite: cc.Sprite = null;
    private columnHighlightSprite: cc.Sprite = null;

    // 初始化
    onLoad(): void {
        // 校验配置
        if (!this.existingGridNode || !this.rowHighlightSpriteFrame || !this.columnHighlightSpriteFrame) {
            cc.error("请确保配置了网格和高亮精灵帧");
            return;
        }
        
        // 计算单元格大小
        this.cellSize = cc.size(
            this.gridTotalSize.width / this.gridSize,
            this.gridTotalSize.height / this.gridSize
        );
        
        // 创建高亮精灵
        this.createHighlightSprites();
        
        // 注册触摸事件
        this.registerTouchEvents();
    }

    // 创建高亮精灵
    private createHighlightSprites(): void {
        // 创建行高亮精灵
        const rowHighlightNode = new cc.Node("RowHighlight");
        rowHighlightNode.parent = this.existingGridNode;
        rowHighlightNode.active = false;
        this.rowHighlightSprite = rowHighlightNode.addComponent(cc.Sprite);
        this.rowHighlightSprite.spriteFrame = this.rowHighlightSpriteFrame;
        rowHighlightNode.setContentSize(this.gridTotalSize.width, this.cellSize.height);
        
        // 创建列高亮精灵
        const columnHighlightNode = new cc.Node("ColumnHighlight");
        columnHighlightNode.parent = this.existingGridNode;
        columnHighlightNode.active = false;
        this.columnHighlightSprite = columnHighlightNode.addComponent(cc.Sprite);
        this.columnHighlightSprite.spriteFrame = this.columnHighlightSpriteFrame;
        columnHighlightNode.setContentSize(this.cellSize.width, this.gridTotalSize.height);
    }

    // 注册触摸事件
    private registerTouchEvents(): void {
        // 启用节点的触摸事件
        this.existingGridNode.on(cc.Node.EventType.TOUCH_START, this.onGridTouch, this);
        this.existingGridNode.on(cc.Node.EventType.TOUCH_MOVE, this.onGridTouch, this);
        this.existingGridNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.existingGridNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    // 处理网格触摸
    private onGridTouch(event: cc.Event.EventTouch): void {
        // 阻止事件冒泡，确保高亮效果优先处理
        event.stopPropagation();

        // 将触摸位置转换为网格本地坐标
        const touchPos = this.existingGridNode.convertToNodeSpaceAR(event.getLocation());

        // 计算触摸的行列索引
        const col = Math.floor((touchPos.x + this.gridTotalSize.width / 2) / this.cellSize.width);
        const row = Math.floor((this.gridTotalSize.height / 2 - touchPos.y) / this.cellSize.height);

        // 边界检查
        if (row >= 0 && row < this.gridSize && col >= 0 && col < this.gridSize) {
            // 更新高亮
            this.updateHighlight(row, col);
        } else {
            // 如果触摸超出边界，隐藏高亮
            this.hideHighlights();
        }
    }

    // 处理触摸结束事件
    private onTouchEnd(event: cc.Event.EventTouch): void {
        // 阻止事件冒泡
        event.stopPropagation();
        // 手指抬起时立即隐藏高亮效果
        this.hideHighlights();
    }

    // 销毁时清理事件监听器
    onDestroy(): void {
        if (this.existingGridNode && this.existingGridNode.isValid) {
            this.existingGridNode.off(cc.Node.EventType.TOUCH_START, this.onGridTouch, this);
            this.existingGridNode.off(cc.Node.EventType.TOUCH_MOVE, this.onGridTouch, this);
            this.existingGridNode.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.existingGridNode.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
    }

    // 隐藏高亮显示
    private hideHighlights(): void {
        if (this.rowHighlightSprite) {
            this.rowHighlightSprite.node.active = false;
        }

        if (this.columnHighlightSprite) {
            this.columnHighlightSprite.node.active = false;
        }

        this.currentRow = -1;
        this.currentCol = -1;

       
    }

    // 更新高亮显示
    private updateHighlight(row: number, col: number): void {
        // 如果位置未变化则不更新
        if (row === this.currentRow && col === this.currentCol) {
            return;
        }
        
        // 更新当前位置
        this.currentRow = row;
        this.currentCol = col;
        
        // 计算高亮位置
        const rowPosY = (this.gridSize - 1 - row) * this.cellSize.height - this.gridTotalSize.height / 2 + this.cellSize.height / 2;
        const colPosX = col * this.cellSize.width - this.gridTotalSize.width / 2 + this.cellSize.width / 2;
        
        // 更新行高亮
        if (this.rowHighlightSprite) {
            const rowNode = this.rowHighlightSprite.node;
            rowNode.active = true;
            rowNode.setPosition(0, rowPosY);
        }
        
        // 更新列高亮
        if (this.columnHighlightSprite) {
            const colNode = this.columnHighlightSprite.node;
            colNode.active = true;
            colNode.setPosition(colPosX, 0);
        }
    }
}