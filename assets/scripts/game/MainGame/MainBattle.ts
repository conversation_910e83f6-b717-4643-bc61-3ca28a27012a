// BattleshipGame.ts - 海战游戏主逻辑

import { GlobalBean } from "../../bean/GlobalBean";
import { EventType } from "../../common/EventCenter";
import { GameMgr } from "../../common/GameMgr";
import ShipController from "../../common/ShipController";
import SkinManager from "../../common/SkinManager";
import { AutoMessageBean, AutoMessageId } from "../../net/MessageBaseBean";
import { MessageId } from "../../net/MessageId";
import { WebSocketManager } from "../../net/WebSocketManager";
import { WebSocketToolState } from "../../net/WebSocketTool";
import { ReconnectGameData, AttackHistoryItem } from "../../bean/GameBean";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BattleshipGame extends cc.Component {
    // 游戏配置
    private config = {
        gridSize: 10,               // 网格大小
        cellSize: 64.4,             // 单元格大小(像素)
        initialAmmo: 3,             // 初始弹药数
        countdownTime: 20,          // 默认倒计时时间(秒)
    };

    // 游戏状态
    public gameState = {
        isPlayerTurn: false,        // 是否玩家回合
        gameStarted: false,         // 游戏是否已开始
        playerAmmo: 0,              // 玩家剩余弹药
        opponentAmmo: 0,            // 对手剩余弹药
        playerGrid: [],             // 玩家网格数据
        opponentGrid: [],           // 对手网格数据
        countdownInterval: null,    // 倒计时计时器
        currentTime: 0,             // 当前倒计时时间
        gameStartMessageTime: 0,    // 游戏开始消息显示时间
        isInWarningState: false,    // 是否处于警告动画状态（5秒内）
        isAnimationPlaying: false,  // 是否正在播放动画
        isAmmoAnimationPlaying: false, // 是否有弹药消耗动画正在播放
        isSunkShipGenerating: false, // 是否正在生成沉船预制体
        isReconnecting: false       // 是否正在重连恢复中
    };

    // UI组件引用
    @property(cc.Node)
    private playerGridImage: cc.Node = null;      // 玩家网格图片

    @property(cc.Node)
    private opponentGridImage: cc.Node = null;    // 对手网格图片

    @property(cc.Label)
    private gameStatusLabel: cc.Label = null;     // 游戏状态文本

    @property(cc.Label)
    private countdownLabel: cc.Label = null;      // 倒计时文本

    @property([cc.Prefab])
    private shipHitPrefabs: cc.Prefab[] = [];     // 6种战舰被击中预制体




    @property(cc.Prefab)
    private hitPrefab: cc.Prefab = null;          // 命中预制体

    @property(cc.Prefab)
    private missPrefab: cc.Prefab = null;         // 未命中预制体

    // Boom节点 - 骨骼动画节点
    @property(sp.Skeleton)
    boom1: sp.Skeleton = null;

    @property(sp.Skeleton)
    boom2: sp.Skeleton = null;

    @property(sp.Skeleton)
    boom3: sp.Skeleton = null;

    // 炸弹动画节点
    @property(sp.Skeleton)
    baozha: sp.Skeleton = null;      // 爆炸动画节点

    @property(sp.Skeleton)
    zhadan: sp.Skeleton = null;      // 炸弹下落动画节点

    @property(sp.Skeleton)
    shuihuatexiao: sp.Skeleton = null; // 水花特效动画节点

    // 音效属性
    @property(cc.AudioClip)
    hitSound: cc.AudioClip = null;        // 炮弹命中音效（击中船）

    @property(cc.AudioClip)
    missSound: cc.AudioClip = null;       // 炮弹落水音效（未击中船）

    @property(cc.AudioClip)
    shipDestroyedSound: cc.AudioClip = null; // 船被击毁音效（整个船被击沉）

    @property(cc.AudioClip)
    gameEndSound: cc.AudioClip = null;    // 结算音效（游戏结束跳出结算页面）

    // 倒计时相关动画节点
    @property(sp.Skeleton)
    blueClockSkeleton: sp.Skeleton = null;  // 蓝色闹钟骨骼动画节点（包含lannaozhong和lantixing动画）

    @property(sp.Skeleton)
    redClockSkeleton: sp.Skeleton = null;   // 红色闹钟骨骼动画节点（包含hongnaozhong和hongtixing动画）

    @property(sp.Skeleton)
    blueTimer: sp.Skeleton = null;  // 蓝计时（自己的头像圈）

    @property(sp.Skeleton)
    redTimer: sp.Skeleton = null;   // 红计时（对面的头像圈）

    // 标记节点集合
    private markers: { [key: string]: cc.Node } = {};

    public firstPlayer: string = "";

    // BattleshipGame组件引用
    private battleshipGameComponent: any = null;

    // 动画状态监控
    private animationStateStartTime: number | null = null;

    // 攻击锁，防止快速重复攻击
    private isAttackLocked: boolean = false;

    // NoticeAttack消息队列，确保攻击按顺序处理
    private noticeAttackQueue: any[] = [];
    private isProcessingNoticeAttack: boolean = false;

    // 生命周期函数
    onLoad() {
        // 查找BattleshipGame组件
        this.battleshipGameComponent = this.node.parent.getComponentInChildren('BattleshipGame');

        // 初始化UI事件监听
        this.initUIEvents();

        // 初始化网格数据
        this.initGridData();

        // 初始化网络连接
        this.initNetwork();

        // 启动动画状态监控定时器
        this.startAnimationStateMonitor();

        //这里监听程序内消息
        GameMgr.Event.AddEventListener(EventType.AutoMessage, this.onAutoMessage, this);

    }

    start() {
        // 初始状态
        this.updateCountdown(0);
        // 不在start中调用updateAmmoDisplay，等待游戏开始消息确定回合后再更新

        // 初始化动画节点状态
        this.initAnimationNodes();

        // 只在游戏未开始时隐藏弹药UI
        if (!this.gameState.gameStarted) {
            this.hideAllAmmoNodes();
        }
    }

    // 初始化动画节点状态
    private initAnimationNodes() {
        // 隐藏所有炸弹动画节点并禁用触摸
        if (this.zhadan) {
            this.zhadan.node.active = false;
            // 禁用动画节点的触摸事件，防止阻挡点击
            this.zhadan.node.getComponent(cc.Button)?.destroy();
            this.zhadan.node.off(cc.Node.EventType.TOUCH_START);
            this.zhadan.node.off(cc.Node.EventType.TOUCH_END);
            this.zhadan.node.off(cc.Node.EventType.TOUCH_MOVE);
            this.zhadan.node.off(cc.Node.EventType.TOUCH_CANCEL);
        }
        if (this.baozha) {
            this.baozha.node.active = false;
            // 禁用动画节点的触摸事件，防止阻挡点击
            this.baozha.node.getComponent(cc.Button)?.destroy();
            this.baozha.node.off(cc.Node.EventType.TOUCH_START);
            this.baozha.node.off(cc.Node.EventType.TOUCH_END);
            this.baozha.node.off(cc.Node.EventType.TOUCH_MOVE);
            this.baozha.node.off(cc.Node.EventType.TOUCH_CANCEL);
        }
        if (this.shuihuatexiao) {
            this.shuihuatexiao.node.active = false;
            // 禁用动画节点的触摸事件，防止阻挡点击
            this.shuihuatexiao.node.getComponent(cc.Button)?.destroy();
            this.shuihuatexiao.node.off(cc.Node.EventType.TOUCH_START);
            this.shuihuatexiao.node.off(cc.Node.EventType.TOUCH_END);
            this.shuihuatexiao.node.off(cc.Node.EventType.TOUCH_MOVE);
            this.shuihuatexiao.node.off(cc.Node.EventType.TOUCH_CANCEL);
        }
    }

    // 初始化UI事件监听
    private initUIEvents() {
        // 对手网格图片点击事件
        this.opponentGridImage.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            this.onOpponentGridClick(event);
        });
    }

    // 初始化网络连接
    private initNetwork() {
        // 监听后端消息
        cc.systemEvent.on("backend-message", this.handleBackendMessage, this);
    }

    // 初始化网格数据
    private initGridData() {
        // 初始化玩家和对手的网格数据
        for (let y = 0; y < this.config.gridSize; y++) {
            this.gameState.playerGrid[y] = [];
            this.gameState.opponentGrid[y] = [];

            for (let x = 0; x < this.config.gridSize; x++) {
                // 0: 未探索, 1: 已探索未命中, 2: 已探索命中, 3: 战舰(仅玩家自己可见)
                this.gameState.playerGrid[y][x] = 0;
                this.gameState.opponentGrid[y][x] = 0;
            }
        }
    }

    // 开始游戏
    private startGame() {
        // 检查 firstPlayer 是否已设置
        if (!this.firstPlayer) {
            return;
        }

        // 发送开始游戏请求到后端
        if (this.firstPlayer == GlobalBean.GetInstance().loginData.userInfo.userId) {
            this.handleBackendMessage({
                type: "gameStarted",
                isPlayerTurn: true
            });
        } else {
            this.handleBackendMessage({
                type: "gameStarted",
                isPlayerTurn: false
            });
        }
    }

    // 处理对手网格点击事件
    private onOpponentGridClick(event: cc.Event.EventTouch) {
        // 获取点击位置相对于网格图片的坐标
        const location = this.opponentGridImage.convertToNodeSpaceAR(event.getLocation());

        // 计算点击的格子坐标（注意：左上角为原点）
        const gridX = Math.floor((location.x + this.opponentGridImage.width / 2) / this.config.cellSize);
        const gridY = Math.floor((this.opponentGridImage.height / 2 - location.y) / this.config.cellSize);




        if (gridX >= 0 && gridX < this.config.gridSize && gridY >= 0 && gridY < this.config.gridSize) {
            // 检查网格状态
        }

        // 检查是否是玩家回合、游戏已开始、有弹药、以及是否有动画正在播放
        // 恢复isAnimationPlaying检查，确保攻击序列化执行
        // 添加攻击锁检查，防止快速重复触摸
        if (!this.gameState.isPlayerTurn || !this.gameState.gameStarted || this.gameState.playerAmmo <= 0 || this.gameState.isAnimationPlaying || this.gameState.isAmmoAnimationPlaying || this.isAttackLocked) {
            this.showClickFeedback(false);
            return;
        }

        // 检查坐标是否在有效范围内
        if (gridX >= 0 && gridX < this.config.gridSize && gridY >= 0 && gridY < this.config.gridSize) {
            // 检查该位置是否未被攻击过
            const cellState = this.gameState.opponentGrid[gridY][gridX];


            if (cellState === 0) {


                // 立即设置攻击锁，防止快速重复触摸
                this.isAttackLocked = true;


                // 立即标记为正在攻击，防止快速重复点击
                this.gameState.opponentGrid[gridY][gridX] = -1;


                // 立即设置动画播放状态，防止其他攻击
                this.gameState.isAnimationPlaying = true;


                // 添加点击反馈
                this.showClickFeedback(true);

                // 刷新20秒倒计时 - 每次点击都重新开始倒计时
                this.resetCountdown();

                // 发送攻击请求到后端
                if (WebSocketManager.GetInstance().webState === WebSocketToolState.Connected) {
                    try {
                        WebSocketManager.GetInstance().sendMsg(MessageId.Attack, {
                            x: gridX,
                            y: gridY
                        });

                    } catch (error) {
                        console.error("MainBattle: 发送攻击请求失败", error);
                        // 发送失败时恢复状态
                        this.gameState.opponentGrid[gridY][gridX] = 0;
                        this.gameState.isAnimationPlaying = false;
                        this.isAttackLocked = false;
                    }
                } else {
                    console.warn("MainBattle: WebSocket未连接，尝试重连");
                    WebSocketManager.GetInstance().connect();
                    // 连接失败时恢复状态
                    this.gameState.opponentGrid[gridY][gridX] = 0;
                    this.gameState.isAnimationPlaying = false;
                    this.isAttackLocked = false;
                }
            } else {

                // 该位置已被攻击过
                this.showClickFeedback(false);
            }
        } else {

            // 点击超出网格范围
            this.showClickFeedback(false);
        }
    }

    // 显示点击反馈
    private showClickFeedback(isValid: boolean) {
        if (isValid) {
            // 有效点击
            this.gameStatusLabel.node.color = cc.Color.GREEN;
            this.scheduleOnce(() => {
                this.gameStatusLabel.node.color = cc.Color.WHITE;
            }, 0.3);
        } else {
            // 无效点击
            this.gameStatusLabel.node.color = cc.Color.RED;
            this.scheduleOnce(() => {
                this.gameStatusLabel.node.color = cc.Color.WHITE;
            }, 0.3);
        }
    }

    // 显示标记（使用预制体）
    private showMarker(parentNode: cc.Node, x: number, y: number, prefab: cc.Prefab) {
        // 生成标记ID
        const markerId = `${parentNode.name}_${x}_${y}`;

        // 如果已有标记，先移除
        if (this.markers[markerId]) {
            this.markers[markerId].removeFromParent();
        }

        // 创建新标记
        const markerNode = cc.instantiate(prefab);

        // 设置位置（注意：左上角为原点）
        markerNode.setPosition(
            x * this.config.cellSize - parentNode.width / 2 + this.config.cellSize / 2,
            parentNode.height / 2 - y * this.config.cellSize - this.config.cellSize / 2
        );

        // 添加到父节点
        parentNode.addChild(markerNode);

        // 确保命中预制体的图层在最上面
        if (prefab === this.hitPrefab) {
            // 命中预制体设置最高的zIndex
            markerNode.zIndex = 1000;
        } else {
            // 其他预制体使用默认zIndex
            markerNode.zIndex = 0;
        }

        // 保存标记引用
        this.markers[markerId] = markerNode;

        // 添加出现动画
        markerNode.setScale(0);
        markerNode.runAction(cc.scaleTo(0.3, 1));
    }

    // 处理来自后端的消息
    private handleBackendMessage(message: any) {
        switch (message.type) {
            case "gameStarted":


                // 清理上一局的动画状态
                this.stopAllAnimations();

                // 清理上一局的预制体（Hit、Miss、SunkShip等）
                this.clearAllMarkers();


                // 重新初始化网格数据
                this.initGridData();


                // 重置弹药动画状态
                this.resetBoomAnimations();


                // 游戏已开始，接收先手玩家信息

                this.gameState.isPlayerTurn = message.isPlayerTurn;
                this.gameState.gameStarted = true;
                this.gameState.playerAmmo = this.config.initialAmmo;
                this.gameState.opponentAmmo = this.config.initialAmmo;
                this.isAttackLocked = false;
                

                // 更新游戏状态

                if (this.gameState.isPlayerTurn) {

                    let str1 = window.getLocalizedStr("mystartturn")
                    this.updateGameStatus(str1);
                    this.startPlayerTurn();
                } else {
                    let str2 = window.getLocalizedStr("youstartturn")
                    this.updateGameStatus(str2);
                    this.startOpponentTurn();
                }


                break;

            case "attackResult":
                // 攻击结果现在由NoticeAttack直接处理
                break;

            case "opponentAttack":
                // 对手攻击结果现在由NoticeAttack直接处理
                break;

            case "gameOver":
                // 处理游戏结束
                this.gameOver(message.playerWon);
                break;
        }
    }

    // 开始玩家回合
    private startPlayerTurn() {
        // 设置玩家回合状态
        this.gameState.isPlayerTurn = true;
        // 确保有满弹药
        this.gameState.playerAmmo = this.config.initialAmmo;

        // 启用玩家交互
        this.enablePlayerInteraction();

        // 重置boom动画到luoshuiqian状态（在updateAmmoDisplay之后调用，确保不被覆盖）
        this.resetBoomAnimations();

        // 显示弹药UI（玩家回合时应该显示弹药）
        this.updateAmmoDisplay();

        // 大回合切换：启动我方动画，停止对方动画
        this.startMyMajorTurn();

        // 开始倒计时
        this.startCountdown();
    }

    // 开始对手回合
    private startOpponentTurn() {
        // 设置对手回合状态
        this.gameState.isPlayerTurn = false;
        this.gameState.opponentAmmo = this.config.initialAmmo;

        // 禁用玩家交互
        this.disablePlayerInteraction();

        // 检查是否需要保护游戏开始消息
        const currentTime = Date.now();
        const timeSinceGameStart = currentTime - this.gameState.gameStartMessageTime;
        const shouldProtectStartMessage = timeSinceGameStart < 3000; // 保护3秒

        // 更新UI - 只有在保护期外才更新状态消息
        if (!shouldProtectStartMessage) {

            let str = window.getLocalizedStr("youturn")
            this.updateGameStatus(str);
        }

        // 检查是否有弹药动画正在播放，如果没有则立即更新弹药显示
        if (this.gameState.isAmmoAnimationPlaying) {
            // 延迟更新弹药显示，避免打断正在播放的xialuo动画
            this.scheduleOnce(() => {
                this.updateAmmoDisplay();
            }, 2.0); // 延迟2秒，确保xialuo动画有足够时间播放完成
        } else {
            // 没有弹药动画播放时立即更新（比如游戏开始时）
            this.updateAmmoDisplay();
        }

        // 大回合切换：启动对方动画，停止我方动画
        this.startOpponentMajorTurn();

        // 开始倒计时
        this.startCountdown();

        // 对手攻击由服务器控制，不需要客户端模拟
    }






    // 结束玩家回合
    private endPlayerTurn() {
        // 停止倒计时
        this.stopCountdown();

        // 回合结束时只重置jishi动画，不重置闹钟动画
        this.resetJishiAnimations();
    }



    // 开始倒计时
    private startCountdown() {
        this.startCountdownWithTime(this.config.countdownTime);
    }

    // 开始倒计时（指定时间）
    private startCountdownWithTime(timeLeft: number) {
        // 停止现有的倒计时
        this.stopCountdown();

        // 设置初始时间
        this.gameState.currentTime = timeLeft;
        this.updateCountdown(this.gameState.currentTime);

        // 启动jishi头像圈动画
        this.startJishiAnimations();

        // 根据初始时间设置正确的闹钟动画状态和状态标记
        if (timeLeft <= 5 && timeLeft > 0) {
            this.gameState.isInWarningState = true;
            this.switchToWarningAnimations();
        } else {
            this.gameState.isInWarningState = false;
            this.switchToClockAnimations();
        }

      

        // 开始新的倒计时 - 使用setTimeout而不是setInterval，避免立即执行
        const countdownTick = () => {
            this.gameState.currentTime--;
            this.updateCountdown(this.gameState.currentTime);

            // 根据剩余时间切换动画：只在状态改变时切换，避免重复设置
            const shouldBeInWarningState = this.gameState.currentTime <= 5 && this.gameState.currentTime > 0;

            if (shouldBeInWarningState && !this.gameState.isInWarningState) {
                // 进入警告状态（从>5秒变为≤5秒）
                this.gameState.isInWarningState = true;
                this.switchToWarningAnimations();

            } else if (!shouldBeInWarningState && this.gameState.isInWarningState) {
                // 退出警告状态（从≤5秒变为>5秒，通常不会发生，但为了完整性保留）
                this.gameState.isInWarningState = false;
                this.switchToClockAnimations();
                
            }

            // 倒计时结束
            if (this.gameState.currentTime <= 0) {
               
                this.stopCountdown();

                if (this.gameState.isPlayerTurn) {
                    // 玩家回合倒计时结束，直接结束回合
                    this.endPlayerTurn();
                }
            } else {
                // 继续倒计时
                this.gameState.countdownInterval = setTimeout(countdownTick, 1000);
            }
        };

        // 1秒后开始第一次倒计时递减
        this.gameState.countdownInterval = setTimeout(countdownTick, 1000);
    }



    // 禁用玩家交互
    private disablePlayerInteraction() {
        this.opponentGridImage.off(cc.Node.EventType.TOUCH_END);
    }

    // 启用玩家交互
    private enablePlayerInteraction() {
        this.opponentGridImage.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            this.onOpponentGridClick(event);
        });
    }

    // 启动动画状态监控定时器
    private startAnimationStateMonitor() {
        // 每5秒检查一次动画状态，防止状态卡死
        this.schedule(() => {
            if (this.gameState.isAnimationPlaying || this.gameState.isAmmoAnimationPlaying) {
                console.warn("MainBattle: 动画状态监控 - 检测到长时间动画播放", {
                    isAnimationPlaying: this.gameState.isAnimationPlaying,
                    isAmmoAnimationPlaying: this.gameState.isAmmoAnimationPlaying,
                    isPlayerTurn: this.gameState.isPlayerTurn
                });

                // 如果动画状态持续超过10秒，强制重置
                if (!this.animationStateStartTime) {
                    this.animationStateStartTime = Date.now();
                } else if (Date.now() - this.animationStateStartTime > 10000) {

                    this.gameState.isAnimationPlaying = false;
                    this.gameState.isAmmoAnimationPlaying = false;
                    this.isAttackLocked = false;
                    this.animationStateStartTime = null;
                }
            } else {
                // 动画状态正常，重置计时器
                this.animationStateStartTime = null;
            }
        }, 5.0);
    }





    // 重置倒计时（小回合）
    private resetCountdown() {
        // 停止现有的倒计时
        this.stopCountdown();

        // 重新开始倒计时
        this.startCountdown();
    }

    // 停止倒计时
    private stopCountdown() {
        if (this.gameState.countdownInterval) {
            clearTimeout(this.gameState.countdownInterval);
            this.gameState.countdownInterval = null;
        }

        // 重置倒计时UI
        this.countdownLabel.node.color = cc.Color.WHITE;
        this.countdownLabel.node.stopAllActions();
    }

    // 处理游戏结束消息
    private handleGameOver(data: any) {
        // 判断玩家是否获胜
        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        const playerWon = data.winner === currentUserId;

        // 调用游戏结束处理
        this.gameOver(playerWon, data);
    }

    // 游戏结束
    private gameOver(playerWon: boolean, _gameData?: any) {
        // 停止游戏
        this.gameState.gameStarted = false;
        this.stopCountdown();

        // 播放结算音效
        if (this.gameEndSound) {
            cc.audioEngine.playEffect(this.gameEndSound, false);
        }

        // 更新游戏状态
        if (playerWon) {
            this.updateGameStatus("恭喜你获胜！");
        } else {
            this.updateGameStatus("你输了，再接再厉！");
        }

        // 禁用所有交互
        this.disablePlayerInteraction();




    }

    // 返回大厅页面（公开方法，供结算弹窗调用）
    public returnToHallPage() {
        // 清理游戏数据
        this.cleanupGameData();

        // 隐藏battleship页面并在后台准备creatship页面
        this.hideBattleshipAndPrepareCreatShip();

        // 跳转到大厅页面
        this.jumpToHallPage();
    }

    // 隐藏battleship页面并在后台准备creatship页面
    private hideBattleshipAndPrepareCreatShip() {
        // 尝试直接调用BattleshipGame组件的方法
        if (this.battleshipGameComponent && this.battleshipGameComponent.hideBattleshipAndPrepareCreatShip) {
            this.battleshipGameComponent.hideBattleshipAndPrepareCreatShip();
        } else {
            // 发送消息通知BattleshipGame隐藏battleship并准备creatship
            const autoMessageBean = {
                msgId: 'HideBattleshipAndPrepareCreatShip',
                data: {}
            };
            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
        }
    }

    // 跳转到大厅页面
    private jumpToHallPage() {
        // 发送跳转大厅页面的消息
        const autoMessageBean = {
            msgId: AutoMessageId.JumpHallPage,
            data: { type: 3 } // 3表示游戏结束后跳转
        };
        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
    }

    // ==================== 断线重连相关方法 ====================

    // 为断线重连重置场景
    private resetForReconnect(): void {
        // 停止所有动画和定时器
        this.stopAllAnimations();
        this.stopCountdown();

        // 重置所有计时器动画
        this.resetAllTimerAnimations();

        // 停止棋盘震动动画
        if (this.playerGridImage && this.playerGridImage.isValid) {
            this.playerGridImage.stopAllActions();
        }
        if (this.opponentGridImage && this.opponentGridImage.isValid) {
            this.opponentGridImage.stopAllActions();
        }

        // 清理棋盘上的标记
        this.clearAllMarkers();

        // 重新初始化网格数据
        this.initGridData();

        // 重置游戏状态
        this.gameState.gameStarted = false;
        this.gameState.isPlayerTurn = false;
        this.gameState.playerAmmo = 0;
        this.gameState.opponentAmmo = 0;
        this.gameState.isAnimationPlaying = false;
        this.isAttackLocked = false;
        this.gameState.isAmmoAnimationPlaying = false;
        this.gameState.isSunkShipGenerating = false;
        this.gameState.isInWarningState = false;

        // 清空NoticeAttack队列
        this.noticeAttackQueue = [];
        this.isProcessingNoticeAttack = false;

        // 重置UI显示
        this.updateCountdown(0);
        this.resetBoomAnimations();
    }

    // 处理网络断开
    private handleNetworkDisconnection(): void {
        // 立即停止倒计时，防止自动攻击触发
        this.stopCountdown();



        // 清空NoticeAttack队列
        this.noticeAttackQueue = [];
        this.isProcessingNoticeAttack = false;

        // 停止所有动画
        this.stopAllAnimations();

        // 禁用玩家交互，防止在断网状态下继续操作
        this.disablePlayerInteraction();

        // 更新游戏状态显示
        let str = window.getLocalizedStr("netdisconnected")
        this.updateGameStatus(str);

    }

    // 处理网络重连
    private handleNetworkReconnected(): void {
        // 网络恢复后，等待断线重连数据到达
        // 不在这里立即恢复倒计时，而是等待RestoreBattleState消息
        // 因为需要先恢复游戏状态，再恢复倒计时

        // 更新游戏状态显示
        let str = window.getLocalizedStr("netreconnectd")
        this.updateGameStatus(str);

    }

    // 确保网格数据已正确初始化
    private ensureGridDataInitialized(): void {

        // 检查网格数组是否存在且正确初始化
        if (!this.gameState.playerGrid || !this.gameState.opponentGrid) {
            this.gameState.playerGrid = [];
            this.gameState.opponentGrid = [];
        }

        // 确保网格数组有正确的大小
        let needReinitialize = false;
        if (this.gameState.playerGrid.length !== this.config.gridSize ||
            this.gameState.opponentGrid.length !== this.config.gridSize) {
            needReinitialize = true;
        } else {
            // 检查每一行是否正确初始化
            for (let y = 0; y < this.config.gridSize; y++) {
                if (!this.gameState.playerGrid[y] || !this.gameState.opponentGrid[y] ||
                    this.gameState.playerGrid[y].length !== this.config.gridSize ||
                    this.gameState.opponentGrid[y].length !== this.config.gridSize) {
                    needReinitialize = true;
                    break;
                }
            }
        }

        if (needReinitialize) {
            this.initGridData();
        }
    }

    // 恢复战斗状态
    private restoreBattleState(reconnectData: ReconnectGameData): void {
      

        // 设置重连标志，防止在恢复过程中重复启动倒计时
        this.gameState.isReconnecting = true;

        // 确保网格数据已正确初始化
        this.ensureGridDataInitialized();

        // 设置游戏已开始
        this.gameState.gameStarted = true;

        // 恢复当前攻击者状态和回合信息
        if (reconnectData.currentAttacker) {
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
            this.gameState.isPlayerTurn = (reconnectData.currentAttacker === myUserId);
        }

        // 恢复剩余攻击次数（重要：确保弹药数量正确）
        if (reconnectData.remainingAttacks !== undefined) {

            if (this.gameState.isPlayerTurn) {
                // 玩家回合：remainingAttacks是玩家的剩余弹药
                this.gameState.playerAmmo = reconnectData.remainingAttacks;
                // 对手弹药数量需要根据攻击历史推算或使用默认值
                this.gameState.opponentAmmo = this.calculateOpponentAmmoFromHistory(reconnectData.attackHistory);

            } else {
                // 对手回合：remainingAttacks是对手的剩余弹药
                this.gameState.opponentAmmo = reconnectData.remainingAttacks;
                // 我方弹药数量需要根据攻击历史推算
                this.gameState.playerAmmo = this.calculatePlayerAmmoFromHistory(reconnectData.attackHistory);

            }



            // 重连时强制更新弹药显示，确保状态正确
            this.forceUpdateAmmoDisplayForReconnect();
        } else {
            // 如果没有剩余攻击次数信息，使用默认值并根据攻击历史计算
            this.gameState.playerAmmo = this.config.initialAmmo;
            this.gameState.opponentAmmo = this.config.initialAmmo;
            if (reconnectData.attackHistory) {
                this.gameState.playerAmmo = this.calculatePlayerAmmoFromHistory(reconnectData.attackHistory);
                this.gameState.opponentAmmo = this.calculateOpponentAmmoFromHistory(reconnectData.attackHistory);
            }


            // 重连时强制更新弹药显示，确保状态正确
            this.forceUpdateAmmoDisplayForReconnect();
        }

        // 恢复攻击历史（关键：根据坐标和命中结果生成相应预制体）
        if (reconnectData.attackHistory && reconnectData.attackHistory.length > 0) {
            // 在恢复攻击历史之前，确保网格状态是干净的（全部为0）
            for (let y = 0; y < this.config.gridSize; y++) {
                for (let x = 0; x < this.config.gridSize; x++) {
                    this.gameState.playerGrid[y][x] = 0;
                    this.gameState.opponentGrid[y][x] = 0;
                }
            }

            this.restoreAttackHistory(reconnectData.attackHistory);
        }

        // 恢复倒计时和动画状态
        if (reconnectData.countDown > 0) {
           
            this.gameState.currentTime = reconnectData.countDown;
            this.updateCountdown(reconnectData.countDown);

            // 启动倒计时（使用恢复的时间）
            this.startCountdownWithTime(this.gameState.currentTime);

            // 恢复jishi动画状态（根据剩余时间调整播放位置）
            this.startJishiAnimationsWithReconnectTime(reconnectData.countDown);

            // 恢复闹钟动画状态（根据剩余时间设置正确的动画和状态标记）
            if (reconnectData.countDown <= 5 && reconnectData.countDown > 0) {
                this.gameState.isInWarningState = true;
                this.switchToWarningAnimations();
            } else {
                this.gameState.isInWarningState = false;
                this.switchToClockAnimations();
            }
        } else {
            
        }

        // 恢复玩家统计数据
        if (reconnectData.playerStats) {
            this.restorePlayerStats(reconnectData.playerStats);
        }

        // 恢复玩家交互状态和回合动画（重要：确保正确的回合状态和UI更新）
        this.restorePlayerInteractionState();

        // 确保弹药动画状态正确
        this.updateAmmoDisplay();

        // 延迟再次强制更新弹药显示，确保在所有状态恢复完成后弹药显示正确
        this.scheduleOnce(() => {

            this.forceUpdateAmmoDisplayForReconnect();

            // 重连恢复完成，清除重连标志
            this.gameState.isReconnecting = false;
            
        }, 0.1);

    }

    // 恢复玩家交互状态和回合动画
    private restorePlayerInteractionState(): void {

        if (this.gameState.isPlayerTurn) {
            // 我的回合 - 启用玩家交互
            this.enablePlayerInteraction();

            // 启动我方回合动画
            this.startMyMajorTurn();

            // 更新状态显示
            let str = window.getLocalizedStr("ituturn");
            this.updateGameStatus(str);
        } else {
            // 对手回合 - 禁用玩家交互
            this.disablePlayerInteraction();

            // 启动对手回合动画
            this.startOpponentMajorTurn();

            // 更新状态显示
            let str = window.getLocalizedStr("itoturn");
            this.updateGameStatus(str);
        }

        // 确保弹药动画状态正确（重要：恢复弹药动画的正确状态）
        this.gameState.isAmmoAnimationPlaying = false;

        // 强制更新弹药显示（确保UI正确显示当前弹药数量，重连时忽略动画状态限制）
        this.forceUpdateAmmoDisplayForReconnect();


    }

    // 恢复攻击历史
    private restoreAttackHistory(attackHistory: AttackHistoryItem[]): void {

        const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        let myAttackCount = 0;
        let opponentAttackCount = 0;
        let validAttackCount = 0;
        let invalidAttackCount = 0;

        for (const attack of attackHistory) {
            // 验证攻击数据的有效性（使用新的字段名）
            if (!attack || attack.x === undefined || attack.y === undefined || attack.hit === undefined) {
                invalidAttackCount++;
                console.warn("MainBattle: 无效的攻击历史数据", attack);
                continue;
            }

            validAttackCount++;

            // 验证坐标范围（使用新的字段名）
            if (attack.x < 0 || attack.x >= this.config.gridSize ||
                attack.y < 0 || attack.y >= this.config.gridSize) {
                console.warn(`MainBattle: 攻击坐标超出范围 (${attack.x}, ${attack.y})`);
                continue;
            }

            const isMyAttack = (attack.attackerId === myUserId);
            const gridArray = isMyAttack ? this.gameState.opponentGrid : this.gameState.playerGrid;
            const boardNode = isMyAttack ? this.opponentGridImage : this.playerGridImage;

            // 统计攻击次数
            if (isMyAttack) {
                myAttackCount++;
            } else {
                opponentAttackCount++;
            }

            // 验证网格数组是否正确初始化（使用新的字段名）
            if (!gridArray || !gridArray[attack.y]) {
                console.warn(`MainBattle: 网格数组未正确初始化，坐标 (${attack.x}, ${attack.y})`);
                continue;
            }

            // 转换hit/sunk为result格式：0=未命中, 1=命中, 2=击沉
            const result = attack.hit ? (attack.sunk ? 2 : 1) : 0;

            // 更新网格状态（使用正确的状态值）
            // 网格状态含义：0=未探索, 1=已探索未命中, 2=已探索命中, 4=命中状态
            const gridState = result === 0 ? 1 : 2;
            gridArray[attack.y][attack.x] = gridState;

         
            // 创建攻击标记（传递完整的攻击数据用于沉船恢复）
            this.createAttackMarker(attack.x, attack.y, result, boardNode, isMyAttack, attack);
        }

       
    }

    // 恢复玩家统计数据
    private restorePlayerStats(_playerStats: { [userId: string]: any }): void {
        // 这里可以根据需要更新UI显示玩家统计信息
    }

    // 创建攻击标记（用于恢复攻击历史）
    private createAttackMarker(x: number, y: number, result: number, boardNode: cc.Node, isOpponentBoard: boolean, attackData?: AttackHistoryItem): void {
        // 使用现有的showMarker方法来创建标记
        if (result === 0) {
            // 未命中 - 创建Miss标记
            this.showMarker(boardNode, x, y, this.missPrefab);
        } else if (result === 1) {
            // 命中 - 创建Hit标记
            this.showMarker(boardNode, x, y, this.hitPrefab);
        } else if (result === 2) {
            // 击沉 - 创建Hit标记
            this.showMarker(boardNode, x, y, this.hitPrefab);

            // 如果是击沉且在对手棋盘上，生成沉船预制体
            if (isOpponentBoard && attackData && this.shouldGenerateSunkShipForReconnect(attackData)) {
               

                // 延迟生成沉船预制体，确保命中标记先创建完成
                this.scheduleOnce(() => {
                    this.generateSunkShipForReconnect(attackData, boardNode);
                }, 0.1);
            }
        }
    }

    // 检查是否应该为断线重连生成沉船预制体
    private shouldGenerateSunkShipForReconnect(attackData: AttackHistoryItem): boolean {
        // 检查必要的沉船信息是否完整
        return attackData.shipId !== undefined &&
               attackData.anchorX !== undefined &&
               attackData.anchorY !== undefined &&
               attackData.direction !== undefined;
    }

    // 为断线重连生成沉船预制体
    private generateSunkShipForReconnect(attackData: AttackHistoryItem, parentNode: cc.Node): void {
        const shipId = attackData.shipId!;
        const anchorX = attackData.anchorX!;
        const anchorY = attackData.anchorY!;
        const direction = attackData.direction!;

        

        // 检查是否已经存在相同位置的沉船预制体，避免重复生成
        const shipKey = `sunk_ship_${anchorX}_${anchorY}`;
        if (this.markers[shipKey]) {
            
            return;
        }

        // 检查shipHitPrefabs数组是否正确配置
        if (!this.shipHitPrefabs || this.shipHitPrefabs.length === 0) {
        
            return;
        }

        // 根据船只ID获取对应的预制体 (shipId从1到6对应shipHitPrefabs[0]到[5])
        let shipPrefab = null;
        if (shipId >= 1 && shipId <= 6) {
            shipPrefab = this.shipHitPrefabs[shipId - 1];
        } else {
            console.warn(`MainBattle: 无效的shipId=${shipId}`);
            return;
        }

        if (!shipPrefab) {
            console.error(`MainBattle: 预制体未找到，shipId=${shipId}, 索引=${shipId - 1}`);
            return;
        }

        // 获取当前对手皮肤ID和对应的皮肤路径
        const skinManager = SkinManager.getInstance();
        const opponentSkinId = skinManager.getOpponentSkinId();
        const shipSize = this.getShipSizeByShipId(shipId);
        const shipIndex = this.getShipIndexByShipId(shipId);
        const shipType = skinManager.getShipTypeBySize(shipSize, shipIndex);

        // 设置对手皮肤（确保皮肤管理器有正确的对手皮肤）
        skinManager.setOpponentSkin(opponentSkinId);
        const spritePath = skinManager.getOpponentSunkShipSpritePath(shipType);

        // 直接创建沉船节点，让ShipController处理皮肤加载
        this.createSunkShipNodeForReconnect(shipPrefab, shipId, anchorX, anchorY, direction, parentNode);
    }

    // 为断线重连创建沉船节点的辅助方法（完全复制原本的createSunkShipNode逻辑）
    private createSunkShipNodeForReconnect(shipPrefab: cc.Prefab, shipId: number, anchorX: number, anchorY: number, direction: number, parentNode: cc.Node): void {

        // 创建船只节点（使用预制体）
        const shipNode = cc.instantiate(shipPrefab);
        shipNode.name = `ReconnectSunkShip_${shipId}_${anchorX}_${anchorY}`;

        // 添加ShipController组件并设置为对手沉船
        let shipController = shipNode.getComponent(ShipController);
        if (!shipController) {
            shipController = shipNode.addComponent(ShipController);
        }

        // 设置船只信息
        const shipSize = this.getShipSizeByShipId(shipId);
        const shipIndex = this.getShipIndexByShipId(shipId);
        shipController.setShipSizeAndIndex(shipSize, shipIndex);

        // 获取当前对手皮肤ID并设置为对手沉船
        const skinManager = SkinManager.getInstance();
        const opponentSkinId = skinManager.getOpponentSkinId();

        shipController.setAsOpponentSunkShip(opponentSkinId);


        // 根据方向设置旋转 (0-向上，1-向右，2-向下，3-向左) -
        switch (direction) {
            case 0: // 向上
                shipNode.rotation = 0;
                break;
            case 1: // 向右
                shipNode.rotation = 90;
                break;
            case 2: // 向下
                shipNode.rotation = 180;
                break;
            case 3: // 向左
                shipNode.rotation = 270;
                break;
            default:
                shipNode.rotation = 0; // 默认向上
                break;
        }

        // 使用与原本createSunkShipNode相同的位置计算公式
        const worldX = (anchorX + 0.5) * this.config.cellSize;
        const worldY = (this.config.gridSize - anchorY - 0.5) * this.config.cellSize; // 反转y轴并调整偏移

        // 根据船只ID和方向设置具体位置，完全复制原本的逻辑
        let finalX: number, finalY: number;

        if (shipId === 3 || shipId === 5 || shipId === 6) {
            if (direction === 0 || direction === 2) { // UP或DOWN
                finalX = worldX - parentNode.width / 2;
                finalY = worldY - parentNode.height / 2 + 32;
            } else { // LEFT或RIGHT
                finalX = worldX - parentNode.width / 2 + 32;
                finalY = worldY - parentNode.height / 2;
            }
        } else if (shipId === 1) { // 2*5的船
            if (direction === 0 || direction === 2) { // UP或DOWN
                finalX = worldX - parentNode.width / 2 + 32;
                finalY = worldY - parentNode.height / 2;
            } else { // LEFT或RIGHT
                finalX = worldX - parentNode.width / 2;
                finalY = worldY - parentNode.height / 2 + 32;
            }
        } else { // 其他船只 (shipId === 2, 4)
            finalX = worldX - parentNode.width / 2;
            finalY = worldY - parentNode.height / 2;
        }

        shipNode.setPosition(finalX, finalY);

        // 设置zIndex，确保船只在命中预制体的下面
        shipNode.zIndex = 500; // 比命中预制体(1000)低，但比未命中预制体(0)高

        // 添加到父节点
        parentNode.addChild(shipNode);

        // 直接显示，不播放动画（断线重连时应该立即显示）
        shipNode.setScale(1.0);

        // 保存船只节点引用，用于清理
        const shipKey = `sunk_ship_${anchorX}_${anchorY}`;
        this.markers[shipKey] = shipNode;

        
    }

    // 清理游戏数据
    private cleanupGameData() {
        // 停止所有动画和清理动画状态
        this.stopAllAnimations();

        // 游戏完全结束时重置所有倒计时动画
        this.resetAllTimerAnimations();

        // 清理棋盘上的标记
        this.clearAllMarkers();

        // 重新初始化网格数据
        this.initGridData();

        // 重置UI显示
        this.updateCountdown(0);
        this.resetBoomAnimations();

        // 清理全局游戏数据
        GlobalBean.GetInstance().cleanData();
    }

    // 登录时的游戏重置 - 用于处理断网后收不到大结算通知的情况
    private resetGameFromLogin(): void {
        

        // 停止所有动画和定时器
        this.stopAllAnimations();
        this.stopCountdown();

        // 重置所有计时器动画
        this.resetAllTimerAnimations();

        // 清理棋盘上的标记
        this.clearAllMarkers();

        // 重新初始化网格数据
        this.initGridData();

        // 重置游戏状态
        this.gameState.gameStarted = false;
        this.gameState.isPlayerTurn = false;
        this.gameState.playerAmmo = 0;
        this.gameState.opponentAmmo = 0;
        this.gameState.isAnimationPlaying = false;
        this.isAttackLocked = false;
        this.gameState.isAmmoAnimationPlaying = false;
        this.gameState.isSunkShipGenerating = false;
        this.gameState.isInWarningState = false;

        // 清空NoticeAttack队列
        this.noticeAttackQueue = [];
        this.isProcessingNoticeAttack = false;

        // 重置UI显示
        this.updateCountdown(0);
        this.resetBoomAnimations();

        // 重置所有动画节点状态
        this.initAnimationNodes();

        
    }
    // 停止所有动画和清理动画状态
    private stopAllAnimations() {
        // 重置动画播放状态
        this.gameState.isAnimationPlaying = false;
        this.gameState.isSunkShipGenerating = false;

        // 停止所有定时器
        this.unscheduleAllCallbacks();

        // 停止并隐藏所有动画节点
        if (this.zhadan) {
            this.zhadan.setCompleteListener(null);
            this.zhadan.node.active = false;
        }

        if (this.baozha) {
            this.baozha.setCompleteListener(null);
            this.baozha.node.active = false;
        }

        if (this.shuihuatexiao) {
            this.shuihuatexiao.setCompleteListener(null);
            this.shuihuatexiao.node.active = false;
        }

        // 停止boom动画
        if (this.boom1) {
            this.boom1.setCompleteListener(null);
            this.boom1.clearTracks();
            this.boom1.timeScale = 0;
            this.boom1.node.active = false;
        }

        if (this.boom2) {
            this.boom2.setCompleteListener(null);
            this.boom2.clearTracks();
            this.boom2.timeScale = 0;
            this.boom2.node.active = false;
        }

        if (this.boom3) {
            this.boom3.setCompleteListener(null);
            this.boom3.clearTracks();
            this.boom3.timeScale = 0;
            this.boom3.node.active = false;
        }

        // 停止所有计时器动画（但保持节点可见）
        if (this.blueTimer) {
            this.blueTimer.clearTracks();
            this.blueTimer.setToSetupPose();
            this.blueTimer.timeScale = 0;
            // 不隐藏节点，保持可见
        }

        if (this.redTimer) {
            this.redTimer.clearTracks();
            this.redTimer.setToSetupPose();
            this.redTimer.timeScale = 0;
            // 不隐藏节点，保持可见
        }

        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.clearTracks();
            this.blueClockSkeleton.setToSetupPose();
            this.blueClockSkeleton.timeScale = 0;
            // 不隐藏节点，保持可见
        }

        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
            this.redClockSkeleton.setToSetupPose();
            this.redClockSkeleton.timeScale = 0;
            // 不隐藏节点，保持可见
        }
    }





    // 清除所有标记
    private clearAllMarkers() {
        for (const markerId in this.markers) {
            if (this.markers.hasOwnProperty(markerId)) {
                this.markers[markerId].removeFromParent();
            }
        }
        this.markers = {};

        // 额外清理：确保棋盘上没有遗留的攻击标记节点
        this.clearBoardMarkers();

    }

    // 清理棋盘上的攻击标记节点
    private clearBoardMarkers() {
        // 清理玩家棋盘上的标记
        if (this.playerGridImage) {
            const playerChildren = this.playerGridImage.children.slice();
            for (let child of playerChildren) {
                if (child.name && (child.name.includes('hit') || child.name.includes('miss') || child.name.includes('marker') || child.name.includes('zhadan') || child.name.includes('baozha') || child.name.includes('shuihua'))) {
                    child.removeFromParent();
                }
            }
        }

        // 清理对手棋盘上的标记
        if (this.opponentGridImage) {
            const opponentChildren = this.opponentGridImage.children.slice();
            for (let child of opponentChildren) {
                if (child.name && (child.name.includes('hit') || child.name.includes('miss') || child.name.includes('marker') || child.name.includes('zhadan') || child.name.includes('baozha') || child.name.includes('shuihua'))) {
                    child.removeFromParent();
                }
            }
        }
    }

    // 更新游戏状态文本
    private updateGameStatus(text: string) {
        this.gameStatusLabel.string = text;

        // 添加状态文本动画
        this.gameStatusLabel.node.stopAllActions();
        this.gameStatusLabel.node.setScale(1);
        this.gameStatusLabel.node.opacity = 255;

        const scaleUp = cc.scaleTo(0.2, 1.1);
        const scaleDown = cc.scaleTo(0.2, 1);
        const sequence = cc.sequence(scaleUp, scaleDown);
        this.gameStatusLabel.node.runAction(sequence);
    }

    // 更新倒计时显示
    private updateCountdown(seconds: number) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        this.countdownLabel.string = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // 更新弹药显示
    private updateAmmoDisplay() {
        // 如果弹药消耗动画正在播放，跳过更新
        if (this.gameState.isAmmoAnimationPlaying) {
            return;
        }

        if (this.gameState.isPlayerTurn) {
            // 玩家回合：显示弹药状态
            this.updatePlayerAmmoDisplay();
        } else {
            // 对手回合：隐藏所有弹药节点
            this.hideAllAmmoNodes();
        }
    }



    // 更新玩家弹药显示
    private updatePlayerAmmoDisplay() {
        // Boom节点数组（从右到左：boom1, boom2, boom3）
        const boomNodes = [this.boom1, this.boom2, this.boom3];



        for (let i = 0; i < this.config.initialAmmo; i++) {
            if (boomNodes[i]) {
                boomNodes[i].node.active = true;

                // 只在必要时更新动画状态，避免重复播放
                const currentAnimName = boomNodes[i].getCurrent(0) ? boomNodes[i].getCurrent(0).animation.name : '';

                // 修正逻辑：根据弹药索引和剩余弹药数量决定状态
                // boom1(索引0): remainingAttacks >= 3 时显示luoshuiqian，否则显示luoshuihou
                // boom2(索引1): remainingAttacks >= 2 时显示luoshuiqian，否则显示luoshuihou
                // boom3(索引2): remainingAttacks >= 1 时显示luoshuiqian，否则显示luoshuihou
                const requiredAmmoForThisBoom = this.config.initialAmmo - i; // boom1需要3，boom2需要2，boom3需要1



                if (this.gameState.playerAmmo >= requiredAmmoForThisBoom) {
                    // 有足够弹药：显示luoshuiqian状态
                    if (currentAnimName !== 'luoshuiqian') {

                        boomNodes[i].setAnimation(0, 'luoshuiqian', false);
                    }
                } else {
                    // 弹药不足：显示luoshuihou状态（但避免打断xialuo动画）
                    if (currentAnimName !== 'luoshuihou' && currentAnimName !== 'xialuo') {

                        boomNodes[i].setAnimation(0, 'luoshuihou', false);
                    } else {
                       
                    }
                }
            }
        }
    }



    // 专门用于重连的弹药显示更新（确保重连时弹药状态正确）
    private forceUpdateAmmoDisplayForReconnect() {

        // 如果是对手回合，直接隐藏弹药UI，不显示动画
        if (!this.gameState.isPlayerTurn) {
            
            this.hideAllAmmoNodes(true); // 强制隐藏
            return;
        }

        // 只有在玩家回合时才显示弹药状态
        // 重连时先完全重置所有弹药动画状态
        this.resetAllAmmoAnimations();

        // 根据剩余弹药数量恢复正确的弹药UI状态（包括已消耗的弹药）
        this.restoreAmmoUIFromHistory();
    }

    // 根据攻击历史恢复弹药UI状态（重连专用）
    private restoreAmmoUIFromHistory() {
        const boomNodes = [this.boom1, this.boom2, this.boom3];
        const consumedAmmoCount = this.config.initialAmmo - this.gameState.playerAmmo;



        // 确保弹药动画状态被重置
        this.gameState.isAmmoAnimationPlaying = false;

        for (let i = 0; i < this.config.initialAmmo; i++) {
            if (boomNodes[i]) {
                boomNodes[i].node.active = true;

                // 清除任何现有的动画监听器
                boomNodes[i].setCompleteListener(null);

                // 判断这个弹药是否已经被消耗
                // 弹药从右到左消耗：boom1(索引0) -> boom2(索引1) -> boom3(索引2)
                const isConsumed = i < consumedAmmoCount;

                if (isConsumed) {

                    try {
                        // 清除任何现有的完成监听器
                        boomNodes[i].setCompleteListener(null);

                        // 播放xialuo动画（不循环）
                        boomNodes[i].setAnimation(0, 'xialuo', false);
                        boomNodes[i].timeScale = 1.0;

                        // 设置动画完成监听器，xialuo播放完成后播放luoshuihou
                        boomNodes[i].setCompleteListener((trackEntry: any) => {
                            if (trackEntry && trackEntry.animation && trackEntry.animation.name === 'xialuo') {
                                try {

                                    boomNodes[i].setAnimation(0, 'luoshuihou', false);
                                    boomNodes[i].setCompleteListener(null);
                                } catch (error) {
                                    console.error(`MainBattle: boom${i + 1} 播放luoshuihou失败:`, error);
                                }
                            }
                        });

                    } catch (error) {
                        console.error(`MainBattle: 设置boom${i + 1}为xialuo失败:`, error);
                        // 如果播放动画失败，直接设置为luoshuihou
                        try {
                            boomNodes[i].setAnimation(0, 'luoshuihou', false);
                        } catch (fallbackError) {
                            console.error(`MainBattle: boom${i + 1} 回退到luoshuihou也失败:`, fallbackError);
                        }
                    }
                } else {
                    // 未消耗的弹药显示为luoshuiqian状态

                    try {
                        boomNodes[i].setAnimation(0, 'luoshuiqian', false);
                        // 确保动画立即生效
                        boomNodes[i].timeScale = 1.0;
                    } catch (error) {
                        console.error(`MainBattle: 设置boom${i + 1}为luoshuiqian失败:`, error);
                    }
                }
            }
        }

        // 延迟验证弹药状态是否正确设置
        this.scheduleOnce(() => {
            this.verifyAmmoUIState();
        }, 0.1);

    }

    // 验证弹药UI状态是否正确
    private verifyAmmoUIState() {
        const boomNodes = [this.boom1, this.boom2, this.boom3];
        const consumedAmmoCount = this.config.initialAmmo - this.gameState.playerAmmo;



        for (let i = 0; i < this.config.initialAmmo; i++) {
            if (boomNodes[i]) {
                const currentAnim = boomNodes[i].getCurrent(0);
                const currentAnimName = currentAnim ? currentAnim.animation.name : 'none';
                const isConsumed = i < consumedAmmoCount;

                if (isConsumed) {
                    // 已消耗的弹药：可能是xialuo（播放中）或luoshuihou（播放完成）
                    const validStates = ['xialuo', 'luoshuihou'];
                    const isValidState = validStates.includes(currentAnimName);


                    // 如果状态无效，强制设置为luoshuihou
                    if (!isValidState) {

                        boomNodes[i].setAnimation(0, 'luoshuihou', false);
                    }
                } else {
                    // 未消耗的弹药：应该是luoshuiqian
                    const expectedAnim = 'luoshuiqian';


                    // 如果状态不匹配，强制修正
                    if (currentAnimName !== expectedAnim) {

                        boomNodes[i].setAnimation(0, expectedAnim, false);
                    }
                }
            }
        }
    }

    // 重置所有弹药动画状态（用于重连时清理状态）
    private resetAllAmmoAnimations() {
        const boomNodes = [this.boom1, this.boom2, this.boom3];


        for (let i = 0; i < boomNodes.length; i++) {
            if (boomNodes[i]) {
                // 清除所有动画轨道
                boomNodes[i].clearTracks();
                // 确保节点激活
                boomNodes[i].node.active = true;

            }
        }

        // 等待一帧确保清理完成
        this.scheduleOnce(() => {

        }, 0.05);
    }

    // 隐藏所有弹药节点
    private hideAllAmmoNodes(forceHide: boolean = false) {
        // 如果弹药消耗动画正在播放且不是强制隐藏，不隐藏boom节点
        if (this.gameState.isAmmoAnimationPlaying && !forceHide) {
            return;
        }

        // 只处理boom节点，隐藏所有boom节点
        const boomNodes = [this.boom1, this.boom2, this.boom3];

        for (let boomNode of boomNodes) {
            if (boomNode) {
                boomNode.node.active = false;
            }
        }

    }

    onDestroy() {
        // 移除事件监听器
        GameMgr.Event.RemoveEventListener(EventType.AutoMessage, this.onAutoMessage, this);
    }



    onAutoMessage(autoMessageBean: AutoMessageBean) {

        switch (autoMessageBean.msgId) {

            case AutoMessageId.BattleStart:
                ; this.BattleStart(autoMessageBean.data)
                break;
            case AutoMessageId.NoticeAttack:
                this.NoticeAttack(autoMessageBean.data);
                break;
            case 'GameOver':
                this.handleGameOver(autoMessageBean.data);
                break;
            case 'CleanSpecificMarkers':
                this.cleanSpecificMarkers();
                break;
            case 'SetSkin':
                this.onSetSkinResponse(autoMessageBean.data);
                break;
            case AutoMessageId.NoticeSkinChange:
                this.onNoticeSkinChange(autoMessageBean.data);
                break;
            case 'ResetForReconnect':

                this.resetForReconnect();
                break;
            case 'RestoreBattleState':

                this.restoreBattleState(autoMessageBean.data);
                break;
            case AutoMessageId.LinkExceptionMsg:

                this.handleNetworkDisconnection();
                break;
            case 'NetworkReconnected':

                this.handleNetworkReconnected();
                break;
            case 'ResetGameFromLogin':
                // 处理登录时的游戏重置
                this.resetGameFromLogin();
                break;

        }
    }
    BattleStart(data: any) {
        this.firstPlayer = data.firstPlayer;

        // 显示游戏开始状态
        const isPlayerTurn = this.firstPlayer === GlobalBean.GetInstance().loginData.userInfo.userId;
        this.handleGameStartStatus(isPlayerTurn);

        this.startGame();
    }

    // 处理游戏开始状态显示
    private handleGameStartStatus(isPlayerTurn: boolean) {
        // 记录游戏开始消息显示时间
        this.gameState.gameStartMessageTime = Date.now();

        if (isPlayerTurn) {
            let str = window.getLocalizedStr("mystartturn");
            this.updateGameStatus(str);
        } else {
            let str = window.getLocalizedStr("youstartturn");
            this.updateGameStatus(str);
        }
    }



    NoticeAttack(data: any) {

        // 将攻击消息添加到队列
        this.noticeAttackQueue.push(data);

        // 如果当前没有在处理攻击，开始处理队列
        if (!this.isProcessingNoticeAttack) {
            this.processNoticeAttackQueue();
        }
    }

    // 处理NoticeAttack消息队列
    private processNoticeAttackQueue() {
        if (this.noticeAttackQueue.length === 0) {
            this.isProcessingNoticeAttack = false;
         
            return;
        }

        this.isProcessingNoticeAttack = true;
        const data = this.noticeAttackQueue.shift();

        

        this.processNoticeAttackData(data);
    }

    // 处理单个NoticeAttack数据
    private processNoticeAttackData(data: any) {
        let attackPlayer = data.userId;
        let x = data.x;
        let y = data.y;
        let hit = data.hit;
        let sunk = data.sunk;
        let nextTurn = data.nextTurn;

        // 判断是自己的攻击还是对手的攻击
        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        const isMyAttack = attackPlayer === currentUserId;

        // 检查是否需要保护游戏开始消息
        const currentTime = Date.now();
        const timeSinceGameStart = currentTime - this.gameState.gameStartMessageTime;
        const shouldProtectStartMessage = timeSinceGameStart < 3000; // 保护3秒

        if (isMyAttack) {
            // 处理自己的攻击结果 - 在对方棋盘显示


            if (hit) {
                // 命中：播放爆炸动画，然后显示命中预制体，不消耗弹药
                this.gameState.opponentGrid[y][x] = 4;

                // 播放爆炸动画，动画完成后显示命中预制体
                this.playExplosionAnimation(x, y, () => {
                    // 如果没有击沉船只，攻击处理完成，继续处理队列
                    if (!sunk) {
                        this.scheduleOnce(() => {
                            this.processNoticeAttackQueue();
                        }, 0.1);
                    }
                });

                // 延迟显示命中预制体，等待爆炸动画播放
                this.scheduleOnce(() => {
                    this.showMarker(this.opponentGridImage, x, y, this.hitPrefab);
                    // 在命中预制体生成时播放对手棋盘震动效果
                    this.playBoardShakeEffect(this.opponentGridImage);
                }, 0.5); // 假设爆炸动画持续0.5秒

                if (sunk) {
                    if (!shouldProtectStartMessage) {
                        let str = window.getLocalizedStr("usunkship");
                        this.updateGameStatus(str);
                    }

                    // 先显示完整的被击沉船只，然后播放整船击沉爆炸动画
                    this.scheduleOnce(() => {
                        // 先显示击沉船只
                        this.showSunkShip(data, this.opponentGridImage);

                        // 然后播放整船击沉爆炸动画
                        this.playSunkShipExplosionAnimation(data, this.opponentGridImage, () => {
                            // 沉船动画完成，继续处理队列
                            this.scheduleOnce(() => {
                                this.processNoticeAttackQueue();
                            }, 0.5); // 给沉船音效一点时间


                        });
                    }, 0.5);
                } else {
                    if (!shouldProtectStartMessage) {
                        let str = window.getLocalizedStr("uhitship");
                        this.updateGameStatus(str);
                    }
                }



            } else {
                // 未命中：播放水花特效动画，然后显示未命中预制体，消耗弹药
                this.gameState.opponentGrid[y][x] = 1;

                // 播放水花特效动画，动画完成后显示未命中预制体
                this.playSplashAnimation(x, y, () => {
                    // 攻击处理完成，继续处理队列
                    this.scheduleOnce(() => {
                        this.processNoticeAttackQueue();
                    }, 0.1);
                });

                // 延迟显示未命中预制体，等待水花动画播放
                this.scheduleOnce(() => {
                    this.showMarker(this.opponentGridImage, x, y, this.missPrefab);
                }, 0.5); // 假设水花动画持续0.5秒

                if (!shouldProtectStartMessage) {
                    let str = window.getLocalizedStr("umiss");
                    this.updateGameStatus(str);
                }

                // 未命中时减少弹药并更新弹药UI
                this.gameState.playerAmmo--;

                // 播放对应boom节点的xialuo动画（与手动点击逻辑一致）
                this.playAmmoConsumeAnimation();

                // 不立即调用updateAmmoDisplay()，让xialuo动画有时间播放
            }


        } else {
            // 处理对手的攻击结果 - 在自己棋盘显示
            if (hit) {
                // 对手命中：播放爆炸动画，然后在自己棋盘显示命中预制体，对手不消耗弹药
                this.gameState.playerGrid[y][x] = 4;

                // 播放爆炸动画（在玩家棋盘上）
                this.playExplosionAnimationOnPlayerBoard(x, y, () => {
                    // 如果没有击沉船只，攻击处理完成，继续处理队列
                    if (!sunk) {
                        this.scheduleOnce(() => {
                            this.processNoticeAttackQueue();
                        }, 0.1);
                    }
                });

                // 延迟显示命中预制体，等待爆炸动画播放
                this.scheduleOnce(() => {
                    this.showMarker(this.playerGridImage, x, y, this.hitPrefab);
                    // 在命中预制体生成时播放玩家棋盘震动效果
                    this.playBoardShakeEffect(this.playerGridImage);
                }, 0.5);

                if (sunk) {
                    if (!shouldProtectStartMessage) {
                        let str = window.getLocalizedStr("osunkship");
                        this.updateGameStatus(str);
                    }

                    // 立即设置沉船流程状态，防止Settlement消息过早弹出
                    this.gameState.isSunkShipGenerating = true;
                   

                    // 播放整船击沉爆炸动画（在玩家棋盘上）
                    this.scheduleOnce(() => {
                        this.playSunkShipExplosionAnimation(data, this.playerGridImage, () => {
                            // 沉船动画完成，继续处理队列
                            this.scheduleOnce(() => {
                                this.processNoticeAttackQueue();
                            }, 0.5); // 给沉船音效一点时间
                        });
                    }, 0.5);
                } else {
                    if (!shouldProtectStartMessage) {
                        let str = window.getLocalizedStr("ohitship");
                        this.updateGameStatus(str);
                    }
                }
                // 对手命中时不减少弹药
            } else {
                // 对手未命中：播放水花特效动画，然后在自己棋盘显示未命中预制体，对手消耗弹药
                this.gameState.playerGrid[y][x] = 1;

                // 播放水花特效动画（在玩家棋盘上）
                this.playSplashAnimationOnPlayerBoard(x, y, () => {
                    // 攻击处理完成，继续处理队列
                    this.scheduleOnce(() => {
                        this.processNoticeAttackQueue();
                    }, 0.1);
                });

                // 延迟显示未命中预制体，等待水花动画播放
                this.scheduleOnce(() => {
                    this.showMarker(this.playerGridImage, x, y, this.missPrefab);
                }, 0.5);

                if (!shouldProtectStartMessage) {
                    let str = window.getLocalizedStr("omiss");
                    this.updateGameStatus(str);
                }
                // 对手未命中时减少对手弹药
                this.gameState.opponentAmmo--;
            }

            // 对手攻击后，如果还是对手回合（小回合切换），重置倒计时和jishi动画
            const isMyTurn = nextTurn === currentUserId;
            if (!isMyTurn && !this.gameState.isPlayerTurn) {
                // 对手小回合切换：重置倒计时，这会重新启动对手的jishi动画
                this.resetCountdown();

            }
        }

        // 检查是否需要切换回合
        const isMyTurn = nextTurn === currentUserId;
        const currentlyMyTurn = this.gameState.isPlayerTurn;

        // 如果服务器指示的回合状态与当前状态不同，则需要切换回合
        if (isMyTurn !== currentlyMyTurn) {
            this.updateTurnState(nextTurn);
        } else if (isMyAttack && this.gameState.playerAmmo <= 0) {
            // 自己的弹药用完了，但服务器说还是自己的回合，重置弹药
            this.gameState.playerAmmo = this.config.initialAmmo;
            // 重置boom动画到luoshuiqian状态
            this.resetBoomAnimations();
        } else if (isMyAttack && isMyTurn && currentlyMyTurn) {
            // 自己的攻击且还是自己的回合（小回合切换），重置倒计时刷新回合时间
            this.resetCountdown();
        }
    }

    // 更新回合状态
    private updateTurnState(nextTurn: string) {
        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        const isMyTurn = nextTurn === currentUserId;

        // 停止当前倒计时
        this.stopCountdown();

        // 延迟更新状态，让攻击结果文字显示一段时间
        this.scheduleOnce(() => {
            // 检查是否需要保护游戏开始消息
            const currentTime = Date.now();
            const timeSinceGameStart = currentTime - this.gameState.gameStartMessageTime;
            const shouldProtectStartMessage = timeSinceGameStart < 3000; // 保护3秒

            if (isMyTurn) {
                this.gameState.isPlayerTurn = true;
                // 只有在真正切换到自己回合时才重置弹药
                this.gameState.playerAmmo = this.config.initialAmmo;
                

                // 重置动画播放状态
                this.gameState.isAnimationPlaying = false;
                // 不强制重置弹药动画状态，让动画自然完成
                // this.gameState.isAmmoAnimationPlaying = false; // 注释掉，避免打断正在播放的弹药动画
                // 确保启用玩家交互
                this.enablePlayerInteraction();
                // 只有在保护期外才更新状态消息
                if (!shouldProtectStartMessage) {
                    let str = window.getLocalizedStr("ituturn");
                    this.updateGameStatus(str);
                }
                // 重置boom动画到luoshuiqian状态（在最后调用，确保不被覆盖）
                this.resetBoomAnimations();
                // 大回合切换：启动我方动画，停止对方动画
                this.startMyMajorTurn();

                // 只有在非重连状态下才启动新的倒计时
                if (!this.gameState.isReconnecting) {
                   
                    this.startCountdown();
                } else {
                 
                }
            } else {
                this.gameState.isPlayerTurn = false;

                
                // 重置动画播放状态
                this.gameState.isAnimationPlaying = false;
                // 不强制重置弹药动画状态，让动画自然完成
                // this.gameState.isAmmoAnimationPlaying = false; // 注释掉，避免打断正在播放的弹药动画
                // 对手回合时禁用玩家交互
                this.disablePlayerInteraction();
                // 只有在保护期外才更新状态消息
                if (!shouldProtectStartMessage) {
                    let str = window.getLocalizedStr("itoturn");
                    this.updateGameStatus(str);
                }

                // 延迟检查弹药UI隐藏（确保弹药动画完成后能正确隐藏）
                this.scheduleOnce(() => {
                    if (!this.gameState.isPlayerTurn && !this.gameState.isAmmoAnimationPlaying) {

                        this.hideAllAmmoNodes();
                    }
                }, 0.5); // 延迟0.5秒检查
                // 延迟更新弹药显示，避免打断正在播放的xialuo动画
                this.scheduleOnce(() => {
                    this.updateAmmoDisplay();
                }, 2.0); // 延迟2秒，确保xialuo动画有足够时间播放完成
                // 大回合切换：启动对方动画，停止我方动画
                this.startOpponentMajorTurn();

                // 只有在非重连状态下才启动新的倒计时
                if (!this.gameState.isReconnecting) {
                   
                    this.startCountdown();
                } else {
                   
                }
            }
        }, 1.5); // 延迟1.5秒，让用户看清攻击结果
    }
    // 显示被击沉的完整船只（仅在对手棋盘上）
    private showSunkShip(attackData: any, parentNode: cc.Node) {
        // 沉船预制体生成状态已在外部设置，这里只是确认

        // 从攻击数据中获取船只信息
        const shipId = attackData.shipId;
        const anchorX = attackData.anchorX;
        const anchorY = attackData.anchorY;
        const direction = attackData.direction || 0; // 默认水平方向

        if (shipId === undefined || anchorX === undefined || anchorY === undefined) {

            return;
        }

        // 检查shipHitPrefabs数组是否正确配置
        if (!this.shipHitPrefabs || this.shipHitPrefabs.length === 0) {

            return;
        }

        // 根据船只ID获取对应的预制体 (shipId从1到6对应shipHitPrefabs[0]到[5])
        let shipPrefab = null;
        if (shipId >= 1 && shipId <= 6) {
            shipPrefab = this.shipHitPrefabs[shipId - 1];

        } else {
            console.warn(`showSunkShip: 无效的shipId=${shipId}`);
            return;
        }

        if (!shipPrefab) {
            console.error(`showSunkShip: 预制体未找到，shipId=${shipId}, 索引=${shipId - 1}`);
            return;
        }

        // 获取当前对手皮肤ID和对应的皮肤路径
        const skinManager = SkinManager.getInstance();
        const opponentSkinId = skinManager.getOpponentSkinId();
        const shipSize = this.getShipSizeByShipId(shipId);
        const shipIndex = this.getShipIndexByShipId(shipId);
        const shipType = skinManager.getShipTypeBySize(shipSize, shipIndex);

        // 设置对手皮肤（确保皮肤管理器有正确的对手皮肤）
        skinManager.setOpponentSkin(opponentSkinId);
        const spritePath = skinManager.getOpponentSunkShipSpritePath(shipType);



        // 先预加载皮肤资源，然后创建节点
        cc.resources.load(spritePath, cc.SpriteFrame, (err, spriteFrame: cc.SpriteFrame) => {
            if (err) {
                console.error(`showSunkShip: 加载对手沉船皮肤失败 ${spritePath}:`, err);
                // 如果皮肤加载失败，使用原始预制体
                this.createSunkShipNode(shipPrefab, shipId, anchorX, anchorY, direction, parentNode, null);
                return;
            }

            // 皮肤加载成功，创建节点并立即应用皮肤
            this.createSunkShipNode(shipPrefab, shipId, anchorX, anchorY, direction, parentNode, spriteFrame);
        });
    }

    // 创建沉船节点的辅助方法
    private createSunkShipNode(shipPrefab: cc.Prefab, shipId: number, anchorX: number, anchorY: number, direction: number, parentNode: cc.Node, spriteFrame: cc.SpriteFrame | null) {
        // 创建船只节点（使用预制体）
        const shipNode = cc.instantiate(shipPrefab);
        shipNode.name = `SunkShip_${shipId}`;

        // 添加ShipController组件并设置为对手沉船
        let shipController = shipNode.getComponent(ShipController);
        if (!shipController) {
            shipController = shipNode.addComponent(ShipController);
        }

        // 设置船只信息
        const shipSize = this.getShipSizeByShipId(shipId);
        const shipIndex = this.getShipIndexByShipId(shipId);
        shipController.setShipSizeAndIndex(shipSize, shipIndex);

        // 获取当前对手皮肤ID并设置为对手沉船
        const skinManager = SkinManager.getInstance();
        const opponentSkinId = skinManager.getOpponentSkinId();
       
        shipController.setAsOpponentSunkShip(opponentSkinId);

        // 如果有预加载的皮肤，立即应用（这个逻辑现在由ShipController处理）
        if (spriteFrame) {
            const sprite = shipNode.getComponent(cc.Sprite);
            if (sprite) {
                sprite.spriteFrame = spriteFrame;
            }
        }

        // 根据方向设置旋转 (0-向上，1-向右，2-向下，3-向左)
        switch (direction) {
            case 0: // 向上
                shipNode.rotation = 0;
                break;
            case 1: // 向右
                shipNode.rotation = 90;
                break;
            case 2: // 向下
                shipNode.rotation = 180;
                break;
            case 3: // 向左
                shipNode.rotation = 270;
                break;
            default:
                shipNode.rotation = 0; // 默认向上
                break;
        }

        // 使用与BattleshipGame.ts相同的位置计算公式
        const worldX = (anchorX + 0.5) * this.config.cellSize;
        const worldY = (this.config.gridSize - anchorY - 0.5) * this.config.cellSize; // 反转y轴并调整偏移

        // 根据船只ID和方向设置具体位置，参考BattleshipGame.ts的逻辑
        let finalX: number, finalY: number;

        if (shipId === 3 || shipId === 5 || shipId === 6) {
            if (direction === 0 || direction === 2) { // UP或DOWN
                finalX = worldX - parentNode.width / 2;
                finalY = worldY - parentNode.height / 2 + 32;
            } else { // LEFT或RIGHT
                finalX = worldX - parentNode.width / 2 + 32;
                finalY = worldY - parentNode.height / 2;
            }
        } else if (shipId === 1) { // 2*5的船
            if (direction === 0 || direction === 2) { // UP或DOWN
                finalX = worldX - parentNode.width / 2 + 32;
                finalY = worldY - parentNode.height / 2;
            } else { // LEFT或RIGHT
                finalX = worldX - parentNode.width / 2;
                finalY = worldY - parentNode.height / 2 + 32;
            }
        } else { // 其他船只 (shipId === 2, 4)
            finalX = worldX - parentNode.width / 2;
            finalY = worldY - parentNode.height / 2;
        }

        shipNode.setPosition(finalX, finalY);

        // 设置zIndex，确保船只在命中预制体的下面
        shipNode.zIndex = 500; // 比命中预制体(1000)低，但比未命中预制体(0)高

        // 添加到父节点
        parentNode.addChild(shipNode);

        // 取消变大动画，直接显示生成
        shipNode.setScale(1.0);

        // 保存船只节点引用，用于清理
        const shipKey = `sunk_ship_${anchorX}_${anchorY}`;
        this.markers[shipKey] = shipNode;

        // 在沉船预制体生成时播放强烈震动效果
        this.playBoardShakeEffect(parentNode, true);

    }



    // 根据shipHitPrefabs的映射关系和提供的公式计算船只占据的所有格子位置
    private getShipOccupiedPositions(shipId: number, anchorX: number, anchorY: number, direction: number): { x: number, y: number }[] {
        const positions: { x: number, y: number }[] = [];

        // 根据shipId确定船只大小
        let width = 1, height = 1;

        switch (shipId) {
            case 1: // 2*5船
                width = 2;
                height = 5;
                break;
            case 2: // 1*5船
                width = 1;
                height = 5;
                break;
            case 3: // 1*4船
                width = 1;
                height = 4;
                break;
            case 4: // 1*3船
                width = 1;
                height = 3;
                break;
            case 5: // 1*2船
            case 6: // 1*2船
                width = 1;
                height = 2;
                break;
        }

        // 使用锚点坐标
        let gridx = anchorX;
        let gridy = anchorY;

        // 把锚点中心坐标转换为左上角坐标
        if (shipId === 1) {
            if (direction === 1 || direction === 3) { // RIGHT || LEFT
                gridx = gridx - 2;
                gridy = gridy - 1;
            }
            else if (direction === 0 || direction === 2) { // UP || DOWN
                gridy = gridy - 2;
            }
        }
        else if (shipId === 2) {
            if (direction === 1 || direction === 3) { // RIGHT || LEFT
                gridx = gridx - 2;
            }
            else if (direction === 0 || direction === 2) { // UP || DOWN
                gridy = gridy - 2;
            }
        }
        else if (shipId === 3) {
            if (direction === 1) { // RIGHT
                gridx = gridx - 1;
            }
            else if (direction === 3) { // LEFT
                gridx = gridx - 1;
            }
            else if (direction === 0) { // UP
                gridy = gridy - 2;
            }
            else if (direction === 2) { // DOWN
                gridy = gridy - 2;
            }
        }
        else if (shipId === 4) {
            if (direction === 1 || direction === 3) { // RIGHT || LEFT
                gridx = gridx - 1;
            }
            else if (direction === 0 || direction === 2) { // UP || DOWN
                gridy = gridy - 1;
            }
        }
        else if (shipId === 5) {
            if (direction === 2) { // DOWN
                gridy = gridy - 1;
            }
            else if (direction === 0) { // UP
                gridy = gridy - 1;
            }
        }
        else if (shipId === 6) {
            if (direction === 2) { // DOWN
                gridy = gridy - 1;
            }
            else if (direction === 0) { // UP
                gridy = gridy - 1;
            }
        }

        // 根据方向调整宽高
        if (direction === 1 || direction === 3) { // RIGHT || LEFT
            [width, height] = [height, width];
        }

        // 计算所有占据的位置
        for (let x = gridx; x < gridx + width; x++) {
            for (let y = gridy; y < gridy + height; y++) {
                positions.push({ x: x, y: y });
            }
        }

        return positions;
    }

    // 播放整船击沉动画 - 依次在所有船只位置播放baozha动画
    private playSunkShipExplosionAnimation(attackData: any, parentBoard: cc.Node, onComplete?: () => void) {
        const shipId = attackData.shipId;
        const anchorX = attackData.anchorX;
        const anchorY = attackData.anchorY;
        const direction = attackData.direction || 0;

        if (shipId === undefined || anchorX === undefined || anchorY === undefined) {
            if (onComplete) onComplete();
            return;
        }

        // 获取船只占据的所有位置
        const positions = this.getShipOccupiedPositions(shipId, anchorX, anchorY, direction);

        if (positions.length === 0) {
            if (onComplete) onComplete();
            return;
        }

        // 沉船动画开始时立即播放船被击毁音效
        if (this.shipDestroyedSound) {
            cc.audioEngine.playEffect(this.shipDestroyedSound, false);
        }

        // 设置沉船动画播放状态，禁用玩家操作
        this.gameState.isAnimationPlaying = true;

        // 备用定时器，确保动画状态能被重置
        this.scheduleOnce(() => {
            this.gameState.isAnimationPlaying = false;

        }, 5.0); // 5秒后强制重置状态

        // 依次播放每个位置的爆炸动画
        this.playExplosionAnimationsSequentially(positions, parentBoard, 0, () => {
            // 沉船动画播放完成，恢复玩家操作
            this.gameState.isAnimationPlaying = false;


            // 直接执行回调，不再延迟
            if (onComplete) {
                onComplete();
            }
        });
    }

    // 依次播放爆炸动画的递归方法
    private playExplosionAnimationsSequentially(positions: { x: number, y: number }[], parentBoard: cc.Node, index: number, onComplete?: () => void) {
        if (index >= positions.length) {
            if (onComplete) {
                onComplete();
            }
            return;
        }

        const position = positions[index];

        // 确保baozha节点存在
        if (!this.baozha) {
            // 跳过当前动画，继续下一个
            this.scheduleOnce(() => {
                this.playExplosionAnimationsSequentially(positions, parentBoard, index + 1, onComplete);
            }, 0.1);
            return;
        }

        // 播放当前位置的爆炸动画（只播放baozha，不播放zhadan）
        let callbackExecuted = false;


        const executeCallback = () => {
            if (!callbackExecuted) {
                callbackExecuted = true;
                this.playExplosionAnimationsSequentially(positions, parentBoard, index + 1, onComplete);
            }
        };

        // 使用加快速度的baozha动画播放
        this.playFastAnimationOnBoard(this.baozha, 'baozha', position.x, position.y, parentBoard, executeCallback, true);

        // 备用定时器，确保即使动画回调失败也能继续（加快沉船动画速度）
        this.scheduleOnce(() => {
            executeCallback();
        }, 0.15); // 从0.25减少到0.15，进一步加快速度
    }
    // 播放弹药消耗动画
    private playAmmoConsumeAnimation() {
        // 根据当前剩余弹药数确定要播放动画的boom节点
        // 弹药从右到左消耗：boom1(右) -> boom2(中) -> boom3(左)
        const boomNodes = [this.boom1, this.boom2, this.boom3];
        const consumedAmmoIndex = this.config.initialAmmo - this.gameState.playerAmmo - 1;


        if (consumedAmmoIndex >= 0 && consumedAmmoIndex < boomNodes.length) {
            const targetBoom = boomNodes[consumedAmmoIndex];
            if (targetBoom) {


                

                // 设置弹药动画播放状态，防止被打断
                this.gameState.isAmmoAnimationPlaying = true;

                // 确保节点是激活状态
                targetBoom.node.active = true;

                // 播放xialuo动画（不循环）
                try {
                    // 确保动画时间尺度正常
                    targetBoom.timeScale = 1.0;
                    targetBoom.setAnimation(0, 'xialuo', false);

                    // 检查动画是否真的开始播放
                    this.scheduleOnce(() => {
                        const currentAnim = targetBoom.getCurrent(0);
                        

                        // 如果动画不是xialuo，说明被其他代码覆盖了
                        if (currentAnim && currentAnim.animation.name !== 'xialuo') {
                            console.warn(`MainBattle: boom${consumedAmmoIndex + 1}动画被覆盖! 当前动画: ${currentAnim.animation.name}`);
                        }
                    }, 0.1);
                } catch (error) {
                    console.error(`MainBattle: 播放xialuo动画失败:`, error);
                    // 动画失败时清除标志位
                    this.gameState.isAmmoAnimationPlaying = false;
                }

                // 动画播放完成后切换到luoshuihou状态
                targetBoom.setCompleteListener((trackEntry: any) => {
                    // 只处理xialuo动画的完成事件
                    if (trackEntry && trackEntry.animation && trackEntry.animation.name === 'xialuo') {
                        try {

                            targetBoom.setAnimation(0, 'luoshuihou', false);
                            // 清除完成监听器，避免重复触发
                            targetBoom.setCompleteListener(null);
                            // 清除弹药动画播放状态
                            this.gameState.isAmmoAnimationPlaying = false;


                            // 检查是否需要立即隐藏弹药UI（如果已经切换到对手回合）
                            if (!this.gameState.isPlayerTurn) {

                                this.hideAllAmmoNodes();
                            }
                        } catch (error) {
                            console.error(`MainBattle: 切换到luoshuihou动画失败:`, error);
                            // 出错时也要清除标志位
                            this.gameState.isAmmoAnimationPlaying = false;
                        }
                    } else {
                       
                    }
                });

                // 添加备用定时器，模拟动画完成（以防完成监听器不工作）
                this.scheduleOnce(() => {
                    if (this.gameState.isAmmoAnimationPlaying) {

                        try {
                            targetBoom.setAnimation(0, 'luoshuihou', false);
                            targetBoom.setCompleteListener(null);
                            this.gameState.isAmmoAnimationPlaying = false;


                            // 检查是否需要立即隐藏弹药UI（如果已经切换到对手回合）
                            if (!this.gameState.isPlayerTurn) {

                                this.hideAllAmmoNodes();
                            }
                        } catch (error) {

                            this.gameState.isAmmoAnimationPlaying = false;
                        }
                    }
                }, 1.5); // 1.5秒后备用处理
            } else {
                console.warn(`MainBattle: boom节点${consumedAmmoIndex + 1}不存在`);
            }
        } else {
            console.warn(`MainBattle: consumedAmmoIndex=${consumedAmmoIndex}超出范围[0, ${boomNodes.length - 1}]`);
        }
    }

    // 重置所有boom节点到luoshuiqian状态
    private resetBoomAnimations() {
        const boomNodes = [this.boom1, this.boom2, this.boom3];

        for (let i = 0; i < boomNodes.length; i++) {
            const boomNode = boomNodes[i];
            if (boomNode) {
                boomNode.node.active = true;
                try {
                    // 清除所有完成监听器，避免旧的监听器继续触发
                    boomNode.setCompleteListener(null);
                    // 强制重置到luoshuiqian状态，不管当前状态是什么
                    boomNode.setAnimation(0, 'luoshuiqian', false);
                } catch (error) {

                }
            }
        }

        // 延迟一帧后再次确认状态，确保重置生效
        this.scheduleOnce(() => {
            for (let i = 0; i < boomNodes.length; i++) {
                const boomNode = boomNodes[i];
                if (boomNode) {
                    try {
                        const currentAnimName = boomNode.getCurrent(0) ? boomNode.getCurrent(0).animation.name : '';
                        if (currentAnimName !== 'luoshuiqian') {
                            boomNode.setAnimation(0, 'luoshuiqian', false);
                        }
                    } catch (error) {

                    }
                }
            }
        }, 0.1);
    }



    // 播放爆炸动画（在对手棋盘上）- 先播放zhadan再播放baozha
    private playExplosionAnimation(gridX: number, gridY: number, onComplete?: () => void) {
        // 注意：isAnimationPlaying状态已在点击时设置，这里不再重复设置

        // 创建独立的动画节点，避免覆盖问题
        this.createAndPlayAnimation('zhadan', gridX, gridY, this.opponentGridImage, () => {
            // zhadan动画完成后播放命中音效
            if (this.hitSound) {
                cc.audioEngine.playEffect(this.hitSound, false);
            }

            // 然后播放baozha动画
            this.createAndPlayAnimation('baozha', gridX, gridY, this.opponentGridImage, () => {
                // baozha动画完成后清除动画状态和攻击锁
                this.gameState.isAnimationPlaying = false;
                this.isAttackLocked = false;

                // 执行完成回调
                if (onComplete) {
                    onComplete();
                }
            });
        });
    }

    // 播放水花特效动画（在对手棋盘上）- 先播放zhadan再播放shuihuatexiao
    private playSplashAnimation(gridX: number, gridY: number, onComplete?: () => void) {
        // 注意：isAnimationPlaying状态已在点击时设置，这里不再重复设置


        // 创建独立的动画节点，避免覆盖问题
        this.createAndPlayAnimation('zhadan', gridX, gridY, this.opponentGridImage, () => {
            // zhadan动画完成后播放落水音效
            if (this.missSound) {
                cc.audioEngine.playEffect(this.missSound, false);
            }

            // 然后播放shuihuatexiao动画
            this.createAndPlayAnimation('shuihuatexiao', gridX, gridY, this.opponentGridImage, () => {
                // shuihuatexiao动画完成后清除动画状态和攻击锁
                this.gameState.isAnimationPlaying = false;
                this.isAttackLocked = false;

                // 执行完成回调
                if (onComplete) {
                    onComplete();
                }
            });
        });
    }

    // 在玩家棋盘上播放爆炸动画 - 先播放zhadan再播放baozha
    private playExplosionAnimationOnPlayerBoard(gridX: number, gridY: number, onComplete?: () => void) {
        // 对手攻击时设置动画播放状态（因为这不是玩家主动攻击）
        this.gameState.isAnimationPlaying = true;

        // 创建独立的动画节点，避免覆盖问题
        this.createAndPlayAnimation('zhadan', gridX, gridY, this.playerGridImage, () => {
            // zhadan动画完成后播放命中音效
            if (this.hitSound) {
                cc.audioEngine.playEffect(this.hitSound, false);
            }

            // 然后播放baozha动画
            this.createAndPlayAnimation('baozha', gridX, gridY, this.playerGridImage, () => {
                // baozha动画完成后清除动画状态
                this.gameState.isAnimationPlaying = false;

                // 执行完成回调
                if (onComplete) {
                    onComplete();
                }
            });
        });
    }

    // 在玩家棋盘上播放水花特效动画 - 先播放zhadan再播放shuihuatexiao
    private playSplashAnimationOnPlayerBoard(gridX: number, gridY: number, onComplete?: () => void) {
        // 创建独立的动画节点，避免覆盖问题
        this.createAndPlayAnimation('zhadan', gridX, gridY, this.playerGridImage, () => {
            // zhadan动画完成后播放落水音效
            if (this.missSound) {
                cc.audioEngine.playEffect(this.missSound, false);
            }

            // 然后播放shuihuatexiao动画
            this.createAndPlayAnimation('shuihuatexiao', gridX, gridY, this.playerGridImage, () => {
                // 执行完成回调
                if (onComplete) {
                    onComplete();
                }
            });
        });
    }

    // 创建独立的动画节点并播放动画，避免覆盖问题
    private createAndPlayAnimation(animationName: string, gridX: number, gridY: number, parentBoard: cc.Node, onComplete?: () => void, highPriority: boolean = false) {
        // 根据动画名称获取对应的原始节点作为模板
        let templateNode: sp.Skeleton = null;
        switch (animationName) {
            case 'zhadan':
                templateNode = this.zhadan;
                break;
            case 'baozha':
                templateNode = this.baozha;
                break;
            case 'shuihuatexiao':
                templateNode = this.shuihuatexiao;
                break;
            default:

                if (onComplete) onComplete();
                return;
        }

        if (!templateNode) {

            if (onComplete) onComplete();
            return;
        }

        // 创建新的动画节点
        const animationNode = new cc.Node(`${animationName}_${gridX}_${gridY}_${Date.now()}`);
        const skeleton = animationNode.addComponent(sp.Skeleton);

        // 复制模板节点的骨骼数据
        skeleton.skeletonData = templateNode.skeletonData;
        skeleton.defaultSkin = templateNode.defaultSkin;
        skeleton.defaultAnimation = templateNode.defaultAnimation;

        // 使用与showMarker完全相同的位置计算方式
        const animationX = gridX * this.config.cellSize - parentBoard.width / 2 + this.config.cellSize / 2;
        const animationY = parentBoard.height / 2 - gridY * this.config.cellSize - this.config.cellSize / 2;

        // 设置动画节点位置
        animationNode.setPosition(animationX, animationY);

        // 设置动画节点的图层优先级
        if (highPriority) {
            // 高优先级动画（如沉船动画）设置在Hit预制体之上
            animationNode.zIndex = 1500;
        } else if (animationName === 'zhadan') {
            // zhadan动画应该在命中和未命中预制体之上
            animationNode.zIndex = 1200;
        } else {
            // 其他普通动画使用默认图层
            animationNode.zIndex = 100;
        }

        // 添加到父节点
        parentBoard.addChild(animationNode);

        // 播放动画（不循环）
        try {
            skeleton.setAnimation(0, animationName, false);

            // 设置动画完成监听器
            skeleton.setCompleteListener((_trackEntry: any) => {
                // 动画播放完成后销毁节点
                animationNode.destroy();
                // 执行回调
                if (onComplete) {
                    onComplete();
                }
            });
        } catch (error) {

            animationNode.destroy();
            // 即使动画失败也要执行回调
            if (onComplete) {
                onComplete();
            }
        }

        // 添加备用定时器，防止动画卡死导致状态无法恢复
        this.scheduleOnce(() => {
            if (animationNode && animationNode.isValid) {

                animationNode.destroy();
                if (onComplete) {
                    onComplete();
                }
            }
        }, 3.0); // 3秒超时
    }

    // 播放棋盘震动效果
    private playBoardShakeEffect(boardNode: cc.Node, isIntenseShake: boolean = false) {
        if (!boardNode || !boardNode.isValid) {
            return;
        }

        // 断线重连时不播放震动效果
        if (this.gameState.isReconnecting) {
            
            return;
        }

        // 保存原始位置
        const originalPosition = cc.v2(boardNode.position.x, boardNode.position.y);

        // 震动参数 - 根据是否是强烈震动调整参数
        const shakeIntensity = isIntenseShake ? 15 : 8; // 震动强度（像素）- 沉船时更强烈
        const shakeDuration = isIntenseShake ? 0.5 : 0.3; // 震动持续时间（秒）- 沉船时更长
        const shakeFrequency = 0.05; // 震动频率（每次震动间隔）

        // 停止之前可能存在的震动动画
        boardNode.stopAllActions();

        // 创建震动序列
        const shakeActions: cc.ActionInterval[] = [];
        const shakeCount = Math.floor(shakeDuration / shakeFrequency);

        for (let i = 0; i < shakeCount; i++) {
            // 随机震动偏移
            const offsetX = (Math.random() - 0.5) * 2 * shakeIntensity;
            const offsetY = (Math.random() - 0.5) * 2 * shakeIntensity;

            // 创建移动到震动位置的动作
            const shakePosition = cc.v2(originalPosition.x + offsetX, originalPosition.y + offsetY);
            const moveAction = cc.moveTo(shakeFrequency, shakePosition);

            shakeActions.push(moveAction);
        }

        // 最后回到原始位置
        const returnAction = cc.moveTo(shakeFrequency, originalPosition);
        shakeActions.push(returnAction);

        // 执行震动序列
        const shakeSequence = cc.sequence(shakeActions);
        boardNode.runAction(shakeSequence);
    }



    // 加快速度的动画播放方法，专门用于沉船动画
    private playFastAnimationOnBoard(animationNode: sp.Skeleton, animationName: string, gridX: number, gridY: number, parentBoard: cc.Node, onComplete?: () => void, highPriority: boolean = false) {
        if (!animationNode) {

            if (onComplete) onComplete();
            return;
        }

        // 使用与showMarker完全相同的位置计算方式
        const animationX = gridX * this.config.cellSize - parentBoard.width / 2 + this.config.cellSize / 2;
        const animationY = parentBoard.height / 2 - gridY * this.config.cellSize - this.config.cellSize / 2;

        // 设置动画节点位置
        animationNode.node.setPosition(animationX, animationY);

        // 确保动画节点是指定棋盘的子节点，避免重复添加
        if (animationNode.node.parent !== parentBoard) {
            // 如果节点已经有父节点，先移除
            if (animationNode.node.parent) {
                animationNode.node.removeFromParent();
            }
            parentBoard.addChild(animationNode.node);
        }

        // 设置动画节点不阻挡触摸事件
        animationNode.node.getComponent(cc.Button)?.destroy();

        // 清除之前的完成监听器，避免冲突
        animationNode.setCompleteListener(null);

        // 设置动画节点的图层优先级
        if (highPriority) {
            // 高优先级动画（如沉船动画）设置在Hit预制体之上
            animationNode.node.zIndex = 1500;
        } else if (animationName === 'zhadan') {
            // zhadan动画应该在命中和未命中预制体之上
            animationNode.node.zIndex = 1200;
        } else {
            // 其他普通动画使用默认图层
            animationNode.node.zIndex = 100;
        }

        animationNode.node.active = true;

        // 播放动画（不循环）
        try {
            const trackEntry = animationNode.setAnimation(0, animationName, false);

            // 加快动画播放速度到2倍
            if (trackEntry) {
                trackEntry.timeScale = 2.0;
            }

            // 设置动画完成监听器
            animationNode.setCompleteListener((_trackEntry: any) => {
                // 动画播放完成后隐藏节点
                animationNode.node.active = false;
                // 清除完成监听器
                animationNode.setCompleteListener(null);
                // 执行回调
                if (onComplete) {
                    onComplete();
                }
            });
        } catch (error) {

            animationNode.node.active = false;
            // 即使动画失败也要执行回调
            if (onComplete) {
                onComplete();
            }
        }
    }

    // ==================== 回合倒计时动画相关方法 ====================

    // 启动我方大回合（根据倒计时时间显示对应动画，对方动画停止）
    private startMyMajorTurn(): void {
        // 显示我方jishi节点，隐藏对方jishi节点
        this.showMyJishi();
        this.hideOpponentJishi();

        // 停止对方所有动画
        this.stopOpponentAnimations();

        // 根据当前倒计时时间设置正确的蓝色闹钟动画
        if (this.gameState.currentTime <= 5 && this.gameState.currentTime > 0) {
            // 倒计时5秒内，显示警告动画
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.timeScale = 1;
                this.blueClockSkeleton.setToSetupPose();
                this.blueClockSkeleton.setAnimation(0, 'lantixing', true);
            }
        } else {
            // 倒计时大于5秒或为0，显示正常闹钟动画
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.timeScale = 0.05;
                this.blueClockSkeleton.setToSetupPose();
                this.blueClockSkeleton.setAnimation(0, 'lannaozhong', true);
            }
        }
    }

    // 启动对方大回合（根据倒计时时间显示对应动画，我方动画停止）
    private startOpponentMajorTurn(): void {
        // 隐藏我方jishi，显示对方jishi
        this.hideMyJishi();
        this.showOpponentJishi();

        // 停止我方所有动画
        this.stopMyAnimations();

        // 根据当前倒计时时间设置正确的红色闹钟动画
        if (this.gameState.currentTime <= 5 && this.gameState.currentTime > 0) {
            // 倒计时5秒内，显示警告动画
            if (this.redClockSkeleton) {
                this.redClockSkeleton.timeScale = 1;
                this.redClockSkeleton.setToSetupPose();
                this.redClockSkeleton.setAnimation(0, 'hongtixing', true);
            }
        } else {
            // 倒计时大于5秒或为0，显示正常闹钟动画
            if (this.redClockSkeleton) {
                this.redClockSkeleton.timeScale = 0.05;
                this.redClockSkeleton.setToSetupPose();
                this.redClockSkeleton.setAnimation(0, 'hongnaozhong', true);
            }
        }
    }

    // 停止我方动画
    private stopMyAnimations(): void {
        if (this.blueTimer) {
            this.blueTimer.clearTracks();
            this.blueTimer.timeScale = 0;

        }
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.clearTracks();
            this.blueClockSkeleton.timeScale = 0;

        }
    }

    // 停止对方动画
    private stopOpponentAnimations(): void {
        if (this.redTimer) {
            this.redTimer.clearTracks();
            this.redTimer.timeScale = 0;

        }
        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
            this.redClockSkeleton.timeScale = 0;

        }
    }

    // 显示我方jishi节点
    private showMyJishi(): void {
        if (this.blueTimer && this.blueTimer.node) {
            this.blueTimer.node.active = true;

        }
    }

    // 隐藏我方jishi节点
    private hideMyJishi(): void {
        if (this.blueTimer && this.blueTimer.node) {
            this.blueTimer.node.active = false;

        }
    }

    // 显示对方jishi节点
    private showOpponentJishi(): void {
        if (this.redTimer && this.redTimer.node) {
            this.redTimer.node.active = true;

        }
    }

    // 隐藏对方jishi节点
    private hideOpponentJishi(): void {
        if (this.redTimer && this.redTimer.node) {
            this.redTimer.node.active = false;

        }
    }

    // 启动jishi头像圈动画（小回合开始时）
    private startJishiAnimations(): void {
        if (this.gameState.isPlayerTurn) {
            // 我的回合：启动蓝色jishi动画
            if (this.blueTimer) {
                this.blueTimer.timeScale = 0.1;
                this.blueTimer.setToSetupPose();
                this.blueTimer.setAnimation(0, 'jishi', true);

            }
        } else {
            // 对手回合：启动红色jishi动画
            if (this.redTimer) {
                this.redTimer.timeScale = 0.1;
                this.redTimer.setToSetupPose();
                this.redTimer.setAnimation(0, 'jishi', true);

            }
        }
    }







    // 切换到闹钟动画（大于5秒时）
    private switchToClockAnimations(): void {
        if (this.gameState.isPlayerTurn) {
            // 我的回合：切换蓝色闹钟到正常闹钟动画
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.timeScale = 0.05;
                this.blueClockSkeleton.setAnimation(0, 'lannaozhong', true);
            }
        } else {
            // 对手回合：切换红色闹钟到正常闹钟动画
            if (this.redClockSkeleton) {
                this.redClockSkeleton.timeScale = 0.05;
                this.redClockSkeleton.setAnimation(0, 'hongnaozhong', true);
            }
        }
    }

    // 切换到警告动画（剩余5秒到0秒时）
    private switchToWarningAnimations(): void {
        if (this.gameState.isPlayerTurn) {
            // 我的回合：切换蓝色闹钟到警告动画
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.timeScale = 1;
                this.blueClockSkeleton.setAnimation(0, 'lantixing', true);

            }
        } else {
            // 对手回合：切换红色闹钟到警告动画
            if (this.redClockSkeleton) {
                this.redClockSkeleton.timeScale = 1;
                this.redClockSkeleton.setAnimation(0, 'hongtixing', true);

            }
        }
    }

    // 只重置当前回合的jishi动画状态（不重置闹钟动画）
    private resetJishiAnimations(): void {
        if (this.gameState.isPlayerTurn) {
            // 我的回合：只重置蓝色jishi动画
            if (this.blueTimer) {
                this.blueTimer.clearTracks();
                this.blueTimer.setToSetupPose();
                this.blueTimer.timeScale = 0; // 暂停动画

            }
        } else {
            // 对手回合：只重置红色jishi动画
            if (this.redTimer) {
                this.redTimer.clearTracks();
                this.redTimer.setToSetupPose();
                this.redTimer.timeScale = 0; // 暂停动画

            }
        }
        // 闹钟动画保持当前状态，不重置

    }

    // 重置所有倒计时动画状态
    private resetAllTimerAnimations(): void {
        // 强制停止所有动画并重置到初始状态
        if (this.blueTimer) {
            this.blueTimer.clearTracks();
            this.blueTimer.setToSetupPose();
            this.blueTimer.timeScale = 0; // 暂停动画

        }
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.clearTracks();
            this.blueClockSkeleton.setToSetupPose();
            this.blueClockSkeleton.timeScale = 0; // 暂停动画

        }
        if (this.redTimer) {
            this.redTimer.clearTracks();
            this.redTimer.setToSetupPose();
            this.redTimer.timeScale = 0; // 暂停动画

        }
        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
            this.redClockSkeleton.setToSetupPose();
            this.redClockSkeleton.timeScale = 0; // 暂停动画

        }
    }

    // 处理SetSkin响应
    private onSetSkinResponse(data: any) {
        

        // 根据api.md，SetSkin响应参数包含：userId, type, id
        if (data.userId && data.type === 1 && data.id) {
            const skinManager = SkinManager.getInstance();

            // 检查是否是自己的皮肤设置
            if (data.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                // 自己的皮肤设置成功
              
                skinManager.setCurrentSkin(data.id);
            } else {
                // 对手的皮肤设置
               
                skinManager.setOpponentSkin(data.id);
            }

            // 刷新所有船只的皮肤（包括双方棋盘）
            ShipController.refreshAllShips();

            // 专门刷新对手沉船皮肤
            ShipController.refreshOpponentSunkShips();
        } else {
            console.error("MainBattle: SetSkin响应格式不正确:", data);
        }
    }

    // 处理皮肤变更广播
    private onNoticeSkinChange(data: any) {
        

        // 检查数据格式：{skinId: number, userId: string}
        if (data.skinId && data.userId) {
            const skinManager = SkinManager.getInstance();
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;

            // 检查是否是对手的皮肤变更
            if (data.userId !== myUserId) {
                // 对手的皮肤变更 - 设置对手沉船皮肤
                
                skinManager.setOpponentSkin(data.skinId);
                

                // 刷新所有船只的皮肤（特别是已生成的沉船）
                
                ShipController.refreshAllShips();

                // 专门刷新对手沉船皮肤
                
                ShipController.refreshOpponentSunkShips();
            } else {
                // 自己的皮肤变更确认
                
                skinManager.setCurrentSkin(data.skinId);
                ShipController.refreshAllShips();
            }
        }
    }

    // 根据船只ID获取船只大小（格子数）
    private getShipSizeByShipId(shipId: number): number {
        switch (shipId) {
            case 1: // 2*5船
                return 10;
            case 2: // 1*5船
                return 5;
            case 3: // 1*4船
                return 4;
            case 4: // 1*3船
                return 3;
            case 5: // 1*2船
            case 6: // 1*2船
                return 2;
            default:
                console.warn(`未知的船只ID: ${shipId}`);
                return 2;
        }
    }

    // 根据船只ID获取船只索引（用于区分同类型的多个船只）
    private getShipIndexByShipId(shipId: number): number {
        switch (shipId) {
            case 5: // 第一艘1*2船
                return 0;
            case 6: // 第二艘1*2船
                return 1;
            default:
                return 0; // 其他船只类型只有1艘，索引为0
        }
    }

    // 清理指定的预制体：Hit、Miss标记和敌人棋盘上的沉船
    private cleanSpecificMarkers(): void {


        // 清理玩家棋盘上的Hit和Miss标记
        if (this.playerGridImage) {
            const playerChildren = this.playerGridImage.children.slice();


            let playerCleanedCount = 0;
            for (let child of playerChildren) {
                const childName = child.name || '';

                // 只清理Hit和Miss预制体
                if (childName === 'Hit' || childName === 'Miss') {

                    child.removeFromParent();
                    playerCleanedCount++;
                } else {
                    
                }
            }

        }

        // 清理对手棋盘上的Hit、Miss标记和沉船预制体
        if (this.opponentGridImage) {
            const opponentChildren = this.opponentGridImage.children.slice();


            let opponentCleanedCount = 0;
            for (let child of opponentChildren) {
                const childName = child.name || '';

                // 清理Hit、Miss标记和沉船预制体
                if (childName === 'Hit' || childName === 'Miss' ||
                    childName.startsWith('SunkShip_')) {

                    child.removeFromParent();
                    opponentCleanedCount++;
                } else {
                }
            }

        }

        // 清空标记字典中的相关标记

        const markersToRemove = [];
        for (const markerId in this.markers) {
            if (markerId.includes('Hit') || markerId.includes('Miss') ||
                markerId.includes('SunkShip_')) {
                markersToRemove.push(markerId);
            }
        }

        for (const markerId of markersToRemove) {
            delete this.markers[markerId];
        }


        // 重置网格状态数组，清除所有攻击记录

        for (let y = 0; y < this.config.gridSize; y++) {
            for (let x = 0; x < this.config.gridSize; x++) {
                // 重置为未探索状态，允许下一把游戏重新攻击这些位置
                this.gameState.playerGrid[y][x] = 0;
                this.gameState.opponentGrid[y][x] = 0;
            }
        }


        // 重置游戏状态

        this.gameState.gameStarted = false;
        this.gameState.isPlayerTurn = false;
        this.gameState.playerAmmo = 0;
        this.gameState.opponentAmmo = 0;
        this.gameState.isAnimationPlaying = false;
        this.isAttackLocked = false;
        this.gameState.isAmmoAnimationPlaying = false;
        this.gameState.isSunkShipGenerating = false;
        this.gameState.isInWarningState = false;

        // 清空NoticeAttack队列
        this.noticeAttackQueue = [];
        this.isProcessingNoticeAttack = false;

        // 停止所有动画和定时器

        this.stopAllAnimations();
        this.stopCountdown();

        // 重置所有计时器动画

        this.resetAllTimerAnimations();

        // 重置所有动画节点状态

        this.initAnimationNodes();

        // 确保事件监听器正常工作

        this.opponentGridImage.off(cc.Node.EventType.TOUCH_END);
        this.opponentGridImage.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            this.onOpponentGridClick(event);
        });


    }

    // 根据攻击历史计算我方剩余弹药数量
    private calculatePlayerAmmoFromHistory(attackHistory?: AttackHistoryItem[]): number {
        if (!attackHistory || attackHistory.length === 0) {
            return this.config.initialAmmo;
        }

        const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        // 只有未命中的攻击才消耗弹药
        const myMissedAttacks = attackHistory.filter(attack => attack.attackerId === myUserId && !attack.hit);
        const remainingAmmo = this.config.initialAmmo - myMissedAttacks.length;



        return Math.max(0, remainingAmmo);
    }

    // 根据攻击历史计算对手剩余弹药数量
    private calculateOpponentAmmoFromHistory(attackHistory?: AttackHistoryItem[]): number {
        if (!attackHistory || attackHistory.length === 0) {
            return this.config.initialAmmo;
        }

        const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        // 只有未命中的攻击才消耗弹药
        const opponentMissedAttacks = attackHistory.filter(attack => attack.attackerId !== myUserId && !attack.hit);
        const remainingAmmo = this.config.initialAmmo - opponentMissedAttacks.length;



        return Math.max(0, remainingAmmo);
    }

    /**
     * 启动jishi动画（断线重连时使用，根据剩余时间调整播放位置）
     * @param remainingTime 剩余时间（秒）
     */
    private startJishiAnimationsWithReconnectTime(remainingTime: number): void {
        if (this.gameState.isPlayerTurn) {
            // 我的回合：启动蓝色jishi动画
            if (this.blueTimer) {
                this.startJishiAnimationWithTimeScale(this.blueTimer, remainingTime);
            }
        } else {
            // 对手回合：启动红色jishi动画
            if (this.redTimer) {
                this.startJishiAnimationWithTimeScale(this.redTimer, remainingTime);
            }
        }
    }

    // ==================== jishi动画时间缩放相关方法 ====================

    /**
     * 启动jishi动画并设置正确的timeScale和播放位置（战斗阶段）
     * @param skeleton 骨骼动画组件
     * @param remainingTime 剩余时间（秒）
     */
    private startJishiAnimationWithTimeScale(skeleton: sp.Skeleton, remainingTime: number): void {
        if (!skeleton) return;

        // 战斗阶段总时间20秒，jishi动画原始长度是2秒
        const totalTime = 20;
        const animationDuration = 2.0;

        // 计算timeScale：让2秒动画播放20秒
        const timeScale = animationDuration / totalTime; // 0.1

        // 计算动画应该从哪个时间点开始播放
        const elapsedTime = totalTime - remainingTime;
        const animationStartTime = (elapsedTime / totalTime) * animationDuration;


        // 设置动画
        skeleton.timeScale = timeScale;
        skeleton.setToSetupPose();
        skeleton.setAnimation(0, 'jishi', true);

        // 设置动画播放位置
        if (animationStartTime > 0) {
            // 如果需要从中间开始播放，使用scheduleOnce在下一帧设置动画时间
            this.scheduleOnce(() => {
                if (skeleton && skeleton.isValid) {
                    // 通过设置动画状态的时间来跳到指定位置
                    const trackEntry = skeleton.getCurrent(0);
                    if (trackEntry) {
                        trackEntry.trackTime = animationStartTime;
                    }
                }
            }, 0);
        }
    }

}