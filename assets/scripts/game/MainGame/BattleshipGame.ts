import { GlobalBean } from "../../bean/GlobalBean";
import { EventType } from "../../common/EventCenter";
import { GameMgr } from "../../common/GameMgr";
import ShipController from "../../common/ShipController";
import SkinManager from "../../common/SkinManager";
import { AutoMessageBean, AutoMessageId } from "../../net/MessageBaseBean";
import { MessageId } from "../../net/MessageId";
import { WebSocketManager } from "../../net/WebSocketManager";
import { ReconnectGameData, ShipWithAnchor } from "../../bean/GameBean";

// 触摸移动阈值常量（像素）- 只有移动距离超过此值才认为是拖拽操作
const TOUCH_MOVE_THRESHOLD = 20;

// 船只类型定义
enum ShipType {
    CARRIER = 1,      // 航母
    BATTLESHIP = 2,   // 战列舰
    CRUISER = 3,      // 重巡
    LIGHT_CRUISER = 4,// 轻巡
    DESTROYER = 5     // 驱逐舰
}

// 船只方向枚举
enum Direction {
    UP = 0,       // 向上
    RIGHT = 1,    // 向右
    DOWN = 2,     // 向下
    LEFT = 3      // 向左
}

// 船只配置接口
interface ShipConfig {
    id: ShipType;          // 船只类型ID
    name: string;          // 船只名称
    size: { width: number; height: number }; // 船只尺寸（格子数）
    count: number;         // 该类型船只数量
    prefabIndex: number;   // 对应预制体在数组中的索引
}

// 船只数据接口
interface ShipData {
    id: number;            // 船只唯一ID
    type: ShipConfig;      // 船只类型配置
    direction: Direction;  // 船只方向
    gridPosition: cc.Vec2; // 网格坐标
    isDragging: boolean;   // 是否正在拖拽
    originalPosition: cc.Vec2; // 原始位置
    ghostNode?: cc.Node;   // 虚影节点引用
    originalRotation: number; // 原始旋转角度
}

// 游戏主类
const { ccclass, property } = cc._decorator;

@ccclass
export default class BattleshipGame extends cc.Component {
    [x: string]: any;

    public isMyTurn: boolean = null;

    // 棋盘节点
    @property(cc.Node)
    private boardNode: cc.Node = null;

    // 随机按钮
    @property(cc.Button)
    private randomBtn: cc.Button = null;

    // 准备按钮
    @property(cc.Button)
    private readyBtn: cc.Button = null;

    // 倒计时标签
    @property(cc.Label)
    private countdownLabel: cc.Label = null;

    // 引导文案标签
    @property(cc.Label)
    private guideLabel: cc.Label = null;

    // 船只预制体数组（6个不同预制体）
    @property([cc.Prefab])
    private shipPrefabs: cc.Prefab[] = [];

    //拖动音效
    @property(cc.AudioClip)
    draggingSound: cc.AudioClip = null;

    // 放置音效
    @property(cc.AudioClip)
    putDownSound: cc.AudioClip = null;

    // 旋转音效
    @property(cc.AudioClip)
    rotateSound: cc.AudioClip = null;

    // 倒计时相关动画节点
    @property(sp.Skeleton)
    blueClockSkeleton: sp.Skeleton = null;  // 蓝色闹钟骨骼动画节点（包含lannaozhong和lantixing动画）

    @property(sp.Skeleton)
    redClockSkeleton: sp.Skeleton = null;   // 红色闹钟骨骼动画节点（包含hongnaozhong和hongtixing动画）

    @property(sp.Skeleton)
    blueTimer: sp.Skeleton = null;  // 蓝计时（自己的头像圈）

    @property(sp.Skeleton)
    redTimer: sp.Skeleton = null;   // 红计时（对面的头像圈）

    @property(cc.Node)
    GameBoard: cc.Node = null;


    @property(cc.Node)
    MainGameUI: cc.Node = null;

    @property(cc.Node)
    ShipBattle: cc.Node = null;

    @property(cc.Node)
    Aralm: cc.Node = null;



    // 准备倒计时时间（秒）
    @property(cc.Integer)
    private countdownTime: number = 30;

    // 棋盘大小（格子数）
    @property(cc.Integer)
    private boardSize: number = 10;

    // 每个格子的大小（像素）
    @property(cc.Integer)
    private cellSize: number = 64;

    public userid:string = "";
    public isanemyReady:boolean = false;



    // 船只配置
    private shipConfigs: { [key in ShipType]: ShipConfig } = {
        [ShipType.CARRIER]: {
            id: ShipType.CARRIER,
            name: 'carrier',
            size: { width: 2, height: 5 },
            count: 1,
            prefabIndex: 0
        },
        [ShipType.BATTLESHIP]: {
            id: ShipType.BATTLESHIP,
            name: 'battleship',
            size: { width: 1, height: 5 },
            count: 1,
            prefabIndex: 1
        },
        [ShipType.CRUISER]: {
            id: ShipType.CRUISER,
            name: 'cruiser',
            size: { width: 1, height: 4 },
            count: 1,
            prefabIndex: 2
        },
        [ShipType.LIGHT_CRUISER]: {
            id: ShipType.LIGHT_CRUISER,
            name: 'lightCruiser',
            size: { width: 1, height: 3 },
            count: 1,
            prefabIndex: 3
        },
        [ShipType.DESTROYER]: {
            id: ShipType.DESTROYER,
            name: 'destroyer',
            size: { width: 1, height: 2 },
            count: 2,
            prefabIndex: 4
        }
    };

    // 游戏状态
    private ships: cc.Node[] = [];             // 所有船只节点
    private isReady: boolean = false;          // 是否已准备好
    private isCountdownFinished: boolean = false; // 倒计时是否结束
    private currentPlayer: number = 1;         // 当前玩家（1表示本地玩家）
    private opponentReady: boolean = false;    // 对手是否准备好
    private currentCountdown: number = 0;      // 当前倒计时值
    private selectedShip: cc.Node = null;      // 当前选中的船只（用于键盘旋转）
    private lastSentShipPositions: string = ""; // 上次发送的船只位置数据（用于防重复发送）
    private isUserInteracting: boolean = false; // 用户是否正在交互（拖拽或点击船只）

    // 布置战舰倒计时相关
    private setupCountdownTimer: any = null;   // 布置倒计时定时器
    private setupTimeLeft: number = 30;        // 布置剩余时间
    private isSetupCountdownActive: boolean = false; // 布置倒计时是否激活

    // 游戏状态控制
    private hasReceivedGameStart: boolean = false; // 是否已收到GameStart消息
    private isInputLocked: boolean = false;    // 是否锁定用户输入（最后1秒后锁定）



    onLoad() {
       

        // 初始化变量
        this.ships = [];
        this.isReady = false;
        this.isCountdownFinished = false;
        this.currentPlayer = 1;
        this.opponentReady = false;
        this.selectedShip = null;

        // 初始化棋盘
        this.initBoard();

        // 创建所有船只（使用6个不同预制体）
        this.createShips();

        // 随机放置所有船只
        this.randomPlaceShips();

        // 注释掉：不在初始化时发送DeployShips消息，等收到GameStart后再允许发送
        // this.saveShipPositions();

        // 更新引导文案
        this.updateGuideText();

        //这里监听程序内消息
        GameMgr.Event.AddEventListener(EventType.AutoMessage, this.onAutoMessage, this);

        // 注册按钮事件
        this.randomBtn.node.on(cc.Node.EventType.TOUCH_END, this.onRandomBtnClick, this);
        this.readyBtn.node.on(cc.Node.EventType.TOUCH_END, this.onReadyBtnClick, this);

        // 初始化时禁用准备按钮，直到收到GameStart消息
        if (this.readyBtn) {
            this.readyBtn.interactable = false;
        }

        // 注册键盘事件（用于旋转）
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);

        // 等待收到 GameStart 消息时才启动倒计时
        // this.startCountdown(); // 移除自动启动

        // 临时解决方案：如果5秒内没有收到GameStart消息，自动启动倒计时
        this.scheduleOnce(() => {
            if (!this.isSetupCountdownActive && this.currentCountdown === 0 && !this.hasReceivedGameStart) {
               
                this.onGameStart({});
            }
        }, 5);

        
    }

    start() {
     

        // 在start中也尝试启动倒计时，作为备用方案
        this.scheduleOnce(() => {
            if (!this.isSetupCountdownActive && this.currentCountdown === 0 && !this.hasReceivedGameStart) {
              
                this.onGameStart({});
            }
        }, 1);
    }

    onDestroy() {
        // 取消注册键盘事件
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        GameMgr.Event.RemoveEventListener(EventType.AutoMessage, this.onAutoMessage, this);
    }

    onAutoMessage(autoMessageBean: AutoMessageBean) {
        

        switch (autoMessageBean.msgId) {

            case AutoMessageId.isReady:
                this.isAnemyReady(autoMessageBean.data);
            break;
            case AutoMessageId.GameStart:
              
                this.onGameStart(autoMessageBean.data);
            break;
            case AutoMessageId.BattleStart:
                this.startGame(autoMessageBean.data);
            break;
            case 'HideBattleshipAndPrepareCreatShip':
             
                this.hideBattleshipAndPrepareCreatShip();
            break;
            case 'ResetForReconnect':
             
                this.resetForReconnect();
            break;
            case 'RestoreWaitingState':
            
                this.restoreWaitingState(autoMessageBean.data);
            break;
            case 'RestoreDeploymentState':
               
                this.restoreDeploymentState(autoMessageBean.data);
            break;
            case 'RestoreBattleStateForBattleship':
                
                this.restoreBattleStateForBattleship(autoMessageBean.data);
            break;
            case 'OpponentTimerStart':
              
                this.startOpponentCountdown();
            break;
            case 'OpponentTimerWarning':
                
                this.switchOpponentToWarningAnimation();
            break;
            case 'OpponentTimerStop':
               
                this.stopOpponentCountdown();
            break;
            case 'SetSkin':
               
                this.onSetSkinResponse(autoMessageBean.data);
            break;
            case AutoMessageId.NoticeSkinChange:
                this.onNoticeSkinChange(autoMessageBean.data);
            break;
            case 'ResetGameFromLogin':
                // 处理登录时的游戏重置
                this.resetGameFromLogin();
            break;

        }
    }

    // 处理 GameStart 消息
    private onGameStart(data: any) {
        // 防重复调用保护
        if (this.hasReceivedGameStart) {

            return;
        }



        // 设置已收到GameStart标志
        this.hasReceivedGameStart = true;

        // 启用准备按钮（收到GameStart消息后才允许点击准备）
        if (this.readyBtn) {
            this.readyBtn.interactable = true;
        }

        // 只启动布置倒计时（不启动常规倒计时，避免冲突）
        // 注意：不要同时启动两个倒计时，这会导致状态冲突
        this.startSetupCountdown();

        // 收到GameStart后立即发送当前船只位置
        this.saveShipPositions();
    }

    // 处理SetSkin响应
    private onSetSkinResponse(data: any) {
      

        // 根据api.md，SetSkin响应参数包含：userId, type, id
        if (data.userId && data.type === 1 && data.id) {
            const skinManager = SkinManager.getInstance();

            // 检查是否是自己的皮肤设置
            if (data.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                // 自己的皮肤设置成功
               
                skinManager.setCurrentSkin(data.id);
            } else {
                // 对手的皮肤设置
        
                skinManager.setOpponentSkin(data.id);
            }

            // 刷新所有船只的皮肤（包括双方棋盘）
            ShipController.refreshAllShips();
        } else {
            console.error("SetSkin响应格式不正确:", data);
        }
    }

    // 处理皮肤变更广播
    private onNoticeSkinChange(data: any) {
       

        // 检查数据格式：{skinId: number, userId: string}
        if (data.skinId && data.userId) {
            const skinManager = SkinManager.getInstance();
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;

            // 检查是否是对手的皮肤变更
            if (data.userId !== myUserId) {
                // 对手的皮肤变更 - 设置对手沉船皮肤
                
                skinManager.setOpponentSkin(data.skinId);

                // 刷新所有船只的皮肤（包括已生成的沉船）
                ShipController.refreshAllShips();

                // 专门刷新对手沉船皮肤
                ShipController.refreshOpponentSunkShips();
            } else {
                // 自己的皮肤变更确认
               
                skinManager.setCurrentSkin(data.skinId);
                ShipController.refreshAllShips();
            }
        }
    }

    public isAnemyReady(data: any){
        this.userid =data.userId;
        this.isanemyReady = data.ready;

        if(this.userid != GlobalBean.GetInstance().loginData.userInfo.userId){
            if(this.isanemyReady==true)
            {
                this.opponentReady = true;
                this.updateGuideText();
                // 更新对手动画状态（只停止对手动画，不影响自己的动画）
                this.updateOpponentTimerAnimations();
            }
        }
    }

    // 键盘按下事件
    private onKeyDown(event: cc.Event.EventKeyboard): void {
        if (this.isReady) return; // 已经准备好，不能再操作
        if (this.isInputLocked) {
           
            return; // 输入已锁定，不能再操作
        }

        // Q键旋转当前选中的船只
        // if (event.keyCode === cc.macro.KEY.q && this.selectedShip) {
        //     this.rotateShip(this.selectedShip);
        // }
    }

    // 初始化棋盘
    private initBoard(): void {
        // 设置棋盘大小（644*644像素，10*10格子）
        this.boardNode.width = this.boardSize * this.cellSize;
        this.boardNode.height = this.boardSize * this.cellSize;

        // 可选：如果棋盘图片原点在左上角，需要调整棋盘位置
        // this.boardNode.y = this.boardNode.height / 2;
    }

    // 创建所有船只（使用不同预制体）
    private createShips(): void {
        let shipId = 1;

        // 遍历每种船只类型
        for (const type in this.shipConfigs) {
            const shipType = this.shipConfigs[type];

            // 根据数量创建船只
            for (let i = 0; i < shipType.count; i++) {
                const ship = this.createShip(shipId++, shipType);
                this.ships.push(ship);
            }
        }
    }

    // 创建单个船只（使用对应预制体）
    private createShip(id: number, shipType: ShipConfig): cc.Node {
        // 从预制体数组中获取对应预制体
        const prefabIndex = shipType.prefabIndex;
        if (prefabIndex >= this.shipPrefabs.length) {
            cc.error(`预制体索引 ${prefabIndex} 超出数组范围`);
            return null;
        }

        // 实例化对应预制体
        const shipNode = cc.instantiate(this.shipPrefabs[prefabIndex]);
        shipNode.parent = this.boardNode;
        shipNode.name = `ship_${id}`;



        // 设置船只属性
        shipNode['shipData'] = {
            id: id,
            type: shipType,
            direction: Direction.UP,
            gridPosition: cc.v2(0, 0),
            isDragging: false,
            originalPosition: cc.v2(0, 0),
            ghostNode: null,
            originalRotation: 0,
            hasMoved: false  // 添加标志位跟踪是否发生了移动
        };

        // 添加船只控制器组件，支持皮肤系统
        const shipController = ShipController.addToNode(shipNode, this.getShipSizeByType(shipType), this.getShipIndexByType(shipType, id));

        // 添加碰撞检测组件
        const collision = shipNode.addComponent(cc.BoxCollider);
        collision.size.width = shipNode.width;
        collision.size.height = shipNode.height;

        // 注册触摸事件（拖拽和旋转）
        shipNode.on(cc.Node.EventType.TOUCH_START, this.onShipTouchStart, this);
        shipNode.on(cc.Node.EventType.TOUCH_MOVE, this.onShipTouchMove, this);
        shipNode.on(cc.Node.EventType.TOUCH_END, this.onShipTouchEnd, this);
        shipNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onShipTouchCancel, this);

        return shipNode;
    }

    // 根据船只类型获取船只大小（格子数）
    private getShipSizeByType(shipType: ShipConfig): number {
        return shipType.size.width * shipType.size.height;
    }

    // 根据船只类型和ID获取船只索引（用于区分同类型的多个船只）
    private getShipIndexByType(shipType: ShipConfig, shipId: number): number {
        // 对于驱逐舰（2格船，有2艘），需要区分索引
        if (shipType.id === ShipType.DESTROYER) {
            // 假设驱逐舰的ID是连续的，第一艘索引为0，第二艘索引为1
            return (shipId - 1) % shipType.count;
        }
        // 其他船只类型只有1艘，索引为0
        return 0;
    }

    // 随机放置所有船只（确保无重叠）
    private randomPlaceShips(): void {
        // 先清除所有船只位置
        this.clearShipPositions();

        // 创建一个二维数组来跟踪已占用的格子
        const occupiedGrid: boolean[][] = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(false));

        // 随机放置每艘船只
        for (let i = 0; i < this.ships.length; i++) {
            let placed = false;

            // 尝试放置最多1000次，防止无限循环
            for (let attempt = 0; attempt < 1000 && !placed; attempt++) {
                // 随机方向
                const direction = Math.floor(Math.random() * 4);

                // 随机生成位置（使用整个棋盘范围）
                const gridX = Math.floor(Math.random() * this.boardSize);
                const gridY = Math.floor(Math.random() * this.boardSize);

                // 先设置方向，然后测试位置是否有效
                this.setShipDirection(this.ships[i], direction);

                // 使用现有的验证方法检查位置是否有效
                if (this.isPositionValid(this.ships[i], gridX, gridY) &&
                    !this.testPositionCollision(this.ships[i], gridX, gridY, direction, occupiedGrid)) {

                    // 位置有效，设置船只位置
                    this.setShipPosition(this.ships[i], gridX, gridY, true);

                    // 更新已占用格子（使用设置后的实际位置）
                    this.updateOccupiedGrid(this.ships[i], occupiedGrid);

                    placed = true;
                    
                }
            }

            if (!placed) {
                console.warn(`BattleshipGame: 无法放置船只 ${this.ships[i]['shipData'].id}，尝试了1000次`);
            }
        }
    }

    // 检查与已占用格子的碰撞
    private checkCollisionWithGrid(ship: cc.Node, gridX: number, gridY: number, direction: Direction, occupiedGrid: boolean[][]): boolean {
        let width = ship['shipData'].type.size.width;
        let height = ship['shipData'].type.size.height;
        //根据方向调整宽高

        if (!ship || !ship['shipData']) {
            cc.error("shipData 未定义", ship.name);
            return;
        }
        //把锚点中心坐标转换为左上角坐标
        if (ship['shipData'].id === 1) {
            if (direction === Direction.RIGHT || direction === Direction.LEFT) {
                gridX = gridX - 2;
                gridY = gridY - 1;
            }
            else if (direction === Direction.UP || direction === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 2) {
            if (direction === Direction.RIGHT || direction === Direction.LEFT) {
                gridX = gridX - 2;
            }
            else if (direction === Direction.UP || direction === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 3) {
            if (direction === Direction.RIGHT) {
                gridX = gridX - 1;
            }
            else if (direction === Direction.LEFT) {
                gridX = gridX - 1;
            }
            else if (direction === Direction.UP) {
                gridY = gridY - 2;
            }
            else if (direction === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 4) {
            if (direction === Direction.RIGHT || direction === Direction.LEFT) {
                gridX = gridX - 1;
            }
            else if (direction === Direction.UP || direction === Direction.DOWN) {
                gridY = gridY - 1;
            }
        }
        else if (ship['shipData'].id === 5) {
            if (direction === Direction.DOWN) {
                gridY = gridY - 1;
            }
            else if (direction === Direction.UP) {
                gridY = gridY - 1;
            }
        }
        else if (ship['shipData'].id === 6) {
            if (direction === Direction.DOWN) {
                gridY = gridY - 1;
            }
            else if (direction === Direction.UP) {
                gridY = gridY - 1;
            }
        }

        if (direction === Direction.RIGHT || direction === Direction.LEFT) {
            [width, height] = [height, width];
        }
        if (gridX < 0 || gridY < 0 || gridX + width > this.boardSize || gridY + height > this.boardSize) {
            return true;
        }

        for (let x = gridX; x < gridX + width; x++) {
            for (let y = gridY; y < gridY + height; y++) {
                if (occupiedGrid[y][x]) {
                    return true; // 有重叠
                }
            }
        }

        
        return false; // 没有重叠

    }

    // 专门用于随机生成的碰撞测试（不依赖船只当前位置）
    private testPositionCollision(ship: cc.Node, testGridX: number, testGridY: number, testDirection: Direction, occupiedGrid: boolean[][]): boolean {
        let width = ship['shipData'].type.size.width;
        let height = ship['shipData'].type.size.height;
        
        let gridX = testGridX;
        let gridY = testGridY;

        // 把锚点中心坐标转换为左上角坐标（与checkCollisionWithGrid相同的逻辑）
        if (ship['shipData'].id === 1) {
            if (testDirection === Direction.RIGHT || testDirection === Direction.LEFT) {
                gridX = gridX - 2;
                gridY = gridY - 1;
            }
            else if (testDirection === Direction.UP || testDirection === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 2) {
            if (testDirection === Direction.RIGHT || testDirection === Direction.LEFT) {
                gridX = gridX - 2;
            }
            else if (testDirection === Direction.UP || testDirection === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 3) {
            if (testDirection === Direction.RIGHT) {
                gridX = gridX - 1;
            }
            else if (testDirection === Direction.LEFT) {
                gridX = gridX - 1;
            }
            else if (testDirection === Direction.UP) {
                gridY = gridY - 2;
            }
            else if (testDirection === Direction.DOWN) {
                gridY = gridY - 2;
            }
        }
        else if (ship['shipData'].id === 4) {
            if (testDirection === Direction.RIGHT || testDirection === Direction.LEFT) {
                gridX = gridX - 1;
            }
            else if (testDirection === Direction.UP || testDirection === Direction.DOWN) {
                gridY = gridY - 1;
            }
        }
        else if (ship['shipData'].id === 5) {
            if (testDirection === Direction.DOWN) {
                gridY = gridY - 1;
            }
            else if (testDirection === Direction.UP) {
                gridY = gridY - 1;
            }
        }
        else if (ship['shipData'].id === 6) {
            if (testDirection === Direction.DOWN) {
                gridY = gridY - 1;
            }
            else if (testDirection === Direction.UP) {
                gridY = gridY - 1;
            }
        }

        if (testDirection === Direction.RIGHT || testDirection === Direction.LEFT) {
            [width, height] = [height, width];
        }

        if (gridX < 0 || gridY < 0 || gridX + width > this.boardSize || gridY + height > this.boardSize) {
            return true;
        }

        for (let x = gridX; x < gridX + width; x++) {
            for (let y = gridY; y < gridY + height; y++) {
                if (occupiedGrid[y][x]) {
                    return true; // 有重叠
                }
            }
        }

        return false; // 没有重叠
    }

    // 更新已占用格子
    private updateOccupiedGrid(ship: cc.Node, occupiedGrid: boolean[][]): void {
        const gridX = ship['shipData'].gridPosition.x;
        const gridY = ship['shipData'].gridPosition.y;
        let width = ship['shipData'].type.size.width;
        let height = ship['shipData'].type.size.height;

        let gridx: number;
        let gridy: number;

        gridx = gridX;
        gridy = gridY;

        //把锚点中心坐标转换为左上角坐标
        if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                gridx = gridx - 2;
                gridy = gridy - 1;
            }
            else if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 2;
            }
        }
        else if (ship['shipData'].id === 2) {
            if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                gridx = gridx - 2;
            }
            else if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 2;
            }
        }
        else if (ship['shipData'].id === 3) {
            if (ship['shipData'].direction === Direction.RIGHT) {
                gridx = gridx - 1;
            }
            else if (ship['shipData'].direction === Direction.LEFT) {
                gridx = gridx - 1;
            }
            else if (ship['shipData'].direction === Direction.UP) {
                gridy = gridy - 2;
            }
            else if (ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 2;
            }
        }
        else if (ship['shipData'].id === 4) {
            if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                gridx = gridx - 1;
            }
            else if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 1;
            }
        }
        else if (ship['shipData'].id === 5) {
            if (ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 1;
            }
            else if (ship['shipData'].direction === Direction.UP) {
                gridy = gridy - 1;
            }
        }
        else if (ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.DOWN) {
                gridy = gridy - 1;
            }
            else if (ship['shipData'].direction === Direction.UP) {
                gridy = gridy - 1;
            }
        }

        // 根据方向调整宽高
        if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
            [width, height] = [height, width];
        }

        for (let x = gridx; x < gridx + width; x++) {
            for (let y = gridy; y < gridy + height; y++) {
                occupiedGrid[y][x] = true;
            }
        }
    }

    // 强制放置船只到第一个空位
    private forcePlaceShip(ship: cc.Node, occupiedGrid: boolean[][]): boolean {
        // 尝试所有可能的位置和方向
        for (let direction = 0; direction < 4; direction++) {
            this.setShipDirection(ship, direction);

            for (let y = 0; y < this.boardSize; y++) {
                for (let x = 0; x < this.boardSize; x++) {
                    if (!this.checkCollisionWithGrid(ship, x, y, direction, occupiedGrid)) {
                        this.setShipPosition(ship, x, y, true);
                        return true;
                    }
                }
            }
        }

        // 如果还是不行，就放在原点（覆盖其他船只）
        //this.setShipDirection(ship, Direction.UP);
        //this.setShipPosition(ship, 0, 0, true);

        //更新已占用格子（覆盖其他船只）
        for (let x = 0; x < this.boardSize; x++) {
            for (let y = 0; y < this.boardSize; y++) {
                occupiedGrid[x][y] = false;
            }
        }
        this.updateOccupiedGrid(ship, occupiedGrid);

        return true;
    }

    // 清除所有船只位置
    private clearShipPositions(): void {
        // 创建一个空的已占用格子数组
        const occupiedGrid: boolean[][] = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(false));

        for (let i = 0; i < this.ships.length; i++) {
            this.ships[i]['shipData'].gridPosition = cc.v2(0, 0);
            this.updateShipNodePosition(this.ships[i]);
        }
    }

    // 彻底清理棋盘上的所有预制体和子节点
    private clearAllBoardElements(): void {
        

        if (!this.boardNode) {
            console.warn("BattleshipGame: boardNode 不存在，跳过清理");
            return;
        }

        // 获取所有子节点
        const children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组

        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 保留船只节点，清理其他所有节点（攻击标记、爆炸效果等）
            if (child.name && child.name.startsWith('ship_')) {
                // 这是船只节点，保留但清理其虚影
                const shipData = child['shipData'];
                if (shipData && shipData.ghostNode) {
                    shipData.ghostNode.removeFromParent();
                    shipData.ghostNode = null;
                }
                continue;
            }

            // 清理所有非船只节点
            
            child.removeFromParent();
        }

        
    }

    // 设置船只方向（顺时针旋转）
    private setShipDirection(ship: cc.Node, direction: Direction): void {
        ship['shipData'].direction = direction;

        // 根据方向调整船只旋转
        ship.rotation = direction * 90;

        // 更新碰撞器大小和偏移
        const collision = ship.getComponent(cc.BoxCollider);
        collision.size.width = ship.width;
        collision.size.height = ship.height;

        // 根据方向调整碰撞器偏移
        if (direction === Direction.RIGHT || direction === Direction.LEFT) {
            collision.offset.x = (ship['shipData'].type.size.height - 1) * this.cellSize / 2;
            collision.offset.y = (ship['shipData'].type.size.width - 1) * this.cellSize / 2;
        } else {
            collision.offset.x = (ship['shipData'].type.size.width - 1) * this.cellSize / 2;
            collision.offset.y = (ship['shipData'].type.size.height - 1) * this.cellSize / 2;
        }
    }

    // 旋转船只（顺时针90度）
    private rotateShip(ship: cc.Node): void {
        if (this.isReady) return; // 已经准备好，不能再旋转

        // 记录旋转前的状态
        const originalDirection = ship['shipData'].direction;
        const originalGridPos = cc.v2(ship['shipData'].gridPosition);

        // 顺时针旋转90度
        const newDirection = (ship['shipData'].direction + 1) % 4;
        this.setShipDirection(ship, newDirection);

        // 检查旋转后的位置是否有效
        if (!this.isPositionValid(ship, originalGridPos.x, originalGridPos.y)) {
            // 位置无效，尝试寻找有效位置
            const validPos = this.findValidPositionAfterRotation(ship, originalGridPos);
            if (validPos) {
                this.setShipPosition(ship, validPos.x, validPos.y);
            } else {
                // 找不到有效位置，恢复原状
                this.setShipDirection(ship, originalDirection);
                return;
            }
        } else if (this.checkCollision(ship, originalGridPos.x, originalGridPos.y)) {
            // 位置有效但有碰撞，尝试寻找有效位置
            const validPos = this.findValidPositionAfterRotation(ship, originalGridPos);
            if (validPos) {
                this.setShipPosition(ship, validPos.x, validPos.y);
            } else {
                // 找不到有效位置，恢复原状
                this.setShipDirection(ship, originalDirection);
                return;
            }
        } else {
            // 位置有效且无碰撞，直接更新
            this.setShipPosition(ship, originalGridPos.x, originalGridPos.y);
        }
    }

    // 寻找旋转后可能的有效位置
    private findValidPositionAfterRotation(ship: cc.Node, originalPos: cc.Vec2): cc.Vec2 | null {
        // 尝试在原位置附近寻找有效位置
        const offsets = [
            cc.v2(0, 0), cc.v2(1, 0), cc.v2(-1, 0), cc.v2(0, 1), cc.v2(0, -1),
            cc.v2(1, 1), cc.v2(1, -1), cc.v2(-1, 1), cc.v2(-1, -1),
            cc.v2(2, 0), cc.v2(-2, 0), cc.v2(0, 2), cc.v2(0, -2)
        ];

        for (const offset of offsets) {
            const newPos = cc.v2(originalPos.x + offset.x, originalPos.y + offset.y);
            if (this.isPositionValid(ship, newPos.x, newPos.y) && !this.checkCollision(ship, newPos.x, newPos.y)) {
                return newPos;
            }
        }

        return null; // 找不到有效位置
    }

    // 设置船只位置
    private setShipPosition(ship: cc.Node, gridX: number, gridY: number, ignoreCollision: boolean = false, playSound: boolean = false): boolean {
        // 检查是否超出边界
        if (!this.isPositionValid(ship, gridX, gridY)) {
            return false;
        }

        // 检查碰撞
        if (!ignoreCollision && this.checkCollision(ship, gridX, gridY)) {
            // 碰撞了，显示红色虚影
            this.showGhostShip(ship, gridX, gridY, true);
            return false;
        }

        // 位置有效，更新船只位置
        ship['shipData'].gridPosition = cc.v2(gridX, gridY);
        this.updateShipNodePosition(ship, playSound);

        // 隐藏虚影
        this.hideGhostShip(ship);

        return true;
    }

    // 检查是否应该在指定位置显示虚影（仿照船只放置的边界判定）
    private shouldShowGhostAtPosition(ship: cc.Node, gridX: number, gridY: number): boolean {
        // 基本边界检查：确保锚点位置在棋盘范围内
        if (gridX < 0 || gridY < 0 || gridX >= this.boardSize || gridY >= this.boardSize) {
            return false;
        }

        // 使用与isPositionValid相同的逻辑来检查船只是否会完全出界
        // 如果船只的任何部分会超出棋盘边界，则不显示虚影
        let width = ship['shipData'].type.size.width;
        let height = ship['shipData'].type.size.height;

        // 根据方向调整宽高
        if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
            [width, height] = [height, width];
        }

        // 检查船只的所有部分是否都在有效范围内
        // 这里使用与isPositionValid相同的边界检查逻辑
        if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                return gridX >= 2 && gridX <= 7 && gridY >= 1 && gridY <= 9;
            } else {
                return gridX >= 0 && gridX <= 8 && gridY >= 2 && gridY <= 7;
            }
        } else if (ship['shipData'].id === 2) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                return gridX >= 2 && gridX <= 7 && gridY >= 0 && gridY <= 9;
            } else {
                return gridX >= 0 && gridX <= 9 && gridY >= 2 && gridY <= 7;
            }
        } else if (ship['shipData'].id === 3) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                return gridX >= 1 && gridX <= 7 && gridY >= 0 && gridY <= 9;
            } else {
                return gridX >= 0 && gridX <= 9 && gridY >= 2 && gridY <= 8;
            }
        } else if (ship['shipData'].id === 4) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                return gridX >= 1 && gridX <= 8 && gridY >= 0 && gridY <= 9;
            } else {
                return gridX >= 0 && gridX <= 9 && gridY >= 1 && gridY <= 8;
            }
        } else if (ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                return gridX >= 0 && gridX <= 8 && gridY >= 0 && gridY <= 9;
            } else {
                return gridX >= 0 && gridX <= 9 && gridY >= 1 && gridY <= 9;
            }
        }

        return false;
    }

    // 检查位置是否有效（是否超出边界）
    private isPositionValid(ship: cc.Node, gridX: number, gridY: number): boolean {
        let width = ship['shipData'].type.size.width;

        let height = ship['shipData'].type.size.height;


        // 根据方向调整宽高
        if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
            [width, height] = [height, width];
        }


        // 检查是否超出边界
        if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                if (gridX >= 2 && gridX <= 7 && gridY >= 1 && gridY <= 9)
                    return true;
            }
            else {
                if (gridX >= 0 && gridX <= 8 && gridY >= 2 && gridY <= 7)
                    return true;
            }
        }
        else if (ship['shipData'].id === 2) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                if (gridX >= 2 && gridX <= 7 && gridY >= 0 && gridY <= 9)
                    return true;
            }
            else {
                if (gridX >= 0 && gridX <= 9 && gridY >= 2 && gridY <= 7)
                    return true;
            }

        }
        else if (ship['shipData'].id === 3) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                if (gridX >= 1 && gridX <= 7 && gridY >= 0 && gridY <= 9)
                    return true;
            }
            else {
                if (gridX >= 0 && gridX <= 9 && gridY >= 2 && gridY <= 8)
                    return true;
            }

        }
        else if (ship['shipData'].id === 4) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                if (gridX >= 1 && gridX <= 8 && gridY >= 0 && gridY <= 9)
                    return true;
            }
            else {
                if (gridX >= 0 && gridX <= 9 && gridY >= 1 && gridY <= 8)
                    return true;
            }

        }
        else if (ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.LEFT || ship['shipData'].direction === Direction.RIGHT) {
                if (gridX >= 0 && gridX <= 8 && gridY >= 0 && gridY <= 9)
                    return true;
            }
            else {
                if (gridX >= 0 && gridX <= 9 && gridY >= 1 && gridY <= 9)
                    return true;
            }
        }
        //return gridX >= 0 && gridY >= 2 && gridX + width <= this.boardSize && gridY + height <= this.boardSize;
    }

    // 检查碰撞
    private checkCollision(ship: cc.Node, gridX: number, gridY: number): boolean {
        // 获取当前船只的占用格子
        const occupiedCells = this.getShipOccupiedCells(ship, gridX, gridY);

        // 检查与其他船只的碰撞
        for (let i = 0; i < this.ships.length; i++) {
            const otherShip = this.ships[i];

            // 跳过自己
            if (otherShip === ship) continue;

            // 获取其他船只的占用格子
            const otherOccupiedCells = this.getShipOccupiedCells(otherShip, otherShip['shipData'].gridPosition.x, otherShip['shipData'].gridPosition.y);

            // 检查是否有重叠
            for (let j = 0; j < occupiedCells.length; j++) {
                for (let k = 0; k < otherOccupiedCells.length; k++) {
                    if (occupiedCells[j].x === otherOccupiedCells[k].x && occupiedCells[j].y === otherOccupiedCells[k].y) {
                        return true; // 有重叠
                    }
                }
            }
        }

        return false; // 没有重叠
    }

    // 获取船只占用的格子
    private getShipOccupiedCells(ship: cc.Node, gridX: number, gridY: number): cc.Vec2[] {
        let width = ship['shipData'].type.size.width;
        let height = ship['shipData'].type.size.height;

        // 根据方向调整宽高
        if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
            [width, height] = [height, width];
        }

        // 生成占用的格子列表
        const cells = [];
        for (let x = 0; x < width; x++) {
            for (let y = 0; y < height; y++) {
                if (ship['shipData'].id === 1)//2*5
                {
                    if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                        cells.push(cc.v2(gridX + x - 2, gridY + y - 1));
                    }
                    else {
                        cells.push(cc.v2(gridX + x, gridY + y - 2));
                    }
                }
                else if (ship['shipData'].id === 2)//1*5
                {
                    if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                        cells.push(cc.v2(gridX + x - 2, gridY + y));
                    }
                    else {
                        cells.push(cc.v2(gridX + x, gridY + y - 2));
                    }
                }
                else if (ship['shipData'].id === 3)//1*4
                {
                    if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                        cells.push(cc.v2(gridX + x - 1, gridY + y));
                    }
                    else {
                        cells.push(cc.v2(gridX + x, gridY + y - 2));
                    }
                }
                else if (ship['shipData'].id === 4)//1*3
                {
                    if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                        cells.push(cc.v2(gridX + x - 1, gridY + y));
                    }
                    else {
                        cells.push(cc.v2(gridX + x, gridY + y - 1));
                    }
                }
                else {
                    if (ship['shipData'].direction === Direction.RIGHT || ship['shipData'].direction === Direction.LEFT) {
                        cells.push(cc.v2(gridX + x, gridY + y));
                    }
                    else {
                        cells.push(cc.v2(gridX + x, gridY + y - 1));
                    }
                }

                //cells.push(cc.v2(gridX + x, gridY + y));
            }
        }

        return cells;
    }

    // 更新船只节点的位置（精确对齐格子）
    private updateShipNodePosition(ship: cc.Node, playSound: boolean = false): void {
        // 将网格坐标转换为世界坐标（精确对齐格子中心）
        const worldX = (ship['shipData'].gridPosition.x + 0.5) * this.cellSize;
        const worldY = (this.boardSize - ship['shipData'].gridPosition.y - 0.5) * this.cellSize; // 关键修改：反转y轴并调整偏移

        // 只在需要时播放音效
        if (playSound) {
            cc.audioEngine.playEffect(this.putDownSound, false);
        }


        if (ship['shipData'].id === 3 || ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
            }
            // 设置船只位置（锚点在中心）
            else {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
            }
        }
        else if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
            }

            else {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
            }
        }
        else {
            ship.x = worldX - this.boardNode.width / 2;
            ship.y = worldY - this.boardNode.height / 2;
        }





    }

    // 显示虚影船只（与预制体大小一致）
    private showGhostShip(ship: cc.Node, gridX: number, gridY: number, isCollision: boolean = false): void {
        // 如果已经有虚影，先移除
        this.hideGhostShip(ship);

        // 创建虚影节点
        const ghostNode = new cc.Node();
        ghostNode.parent = this.boardNode;
        ghostNode.name = `ghost_${ship.name}`;

        // 设置锚点为中心
        ghostNode.anchorX = ship.anchorX;
        ghostNode.anchorY = ship.anchorY;

        // 设置虚影属性（使用相同的预制体）
        const sprite = ghostNode.addComponent(cc.Sprite);
        sprite.spriteFrame = ship.getComponent(cc.Sprite).spriteFrame;

        // 设置虚影颜色（红色表示碰撞或超出边界，灰色表示正常）
        ghostNode.color = isCollision ? cc.Color.RED : cc.Color.GRAY;

        // 设置虚影大小和旋转（与预制体一致）
        ghostNode.width = ship.width;
        ghostNode.height = ship.height;
        ghostNode.rotation = ship.rotation;

        // 设置虚影位置（精确对齐格子中心）
        const worldX = (gridX + 0.5) * this.cellSize;
        const worldY = (this.boardSize - gridY - 0.5) * this.cellSize; // 关键修改：反转y轴并调整偏移

        if (ship['shipData'].id === 3 || ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ghostNode.x = worldX - this.boardNode.width / 2;
                ghostNode.y = worldY - this.boardNode.height / 2 + 32;
            }
            // 设置船只位置（锚点在中心）
            else {
                ghostNode.x = worldX - this.boardNode.width / 2 + 32;
                ghostNode.y = worldY - this.boardNode.height / 2;
            }
        }
        else if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ghostNode.x = worldX - this.boardNode.width / 2 + 32;
                ghostNode.y = worldY - this.boardNode.height / 2;
            }

            else {
                ghostNode.x = worldX - this.boardNode.width / 2;
                ghostNode.y = worldY - this.boardNode.height / 2 + 32;
            }
        }
        else {
            ghostNode.x = worldX - this.boardNode.width / 2;
            ghostNode.y = worldY - this.boardNode.height / 2;
        }




        // 设置透明度
        ghostNode.opacity = 128;

        // 保存虚影节点引用
        ship['shipData'].ghostNode = ghostNode;
    }

    // 隐藏虚影船只
    private hideGhostShip(ship: cc.Node): void {
        if (ship['shipData'].ghostNode) {
            ship['shipData'].ghostNode.destroy();
            ship['shipData'].ghostNode = null;
        }
    }

    // 船只触摸开始事件
    private onShipTouchStart(event: cc.Event.EventTouch): void {
        const ship = event.currentTarget as cc.Node;

        if (this.isReady) return; // 已经准备好，不能再拖拽
        if (this.isInputLocked) {
           
            return; // 输入已锁定，不能再操作
        }

        // 检查倒计时是否正在重置中
        if (!this.isSetupCountdownActive && !this.isCountdownFinished) {
            
            return;
        }

        // 设置用户交互标志，防止倒计时重置
        this.isUserInteracting = true;

        // 记录原始位置和旋转角度
        ship['shipData'].originalPosition = cc.v2(ship['shipData'].gridPosition);
        ship['shipData'].isDragging = true;
        ship['shipData'].hasMoved = false;  // 重置移动标志

        // 记录初始触摸位置，用于计算移动距离
        ship['shipData'].startTouchPos = event.getLocation();
        ship['shipData'].totalMoveDistance = 0; // 重置总移动距离

        // 播放拖动音效
        cc.audioEngine.playEffect(this.draggingSound, false);

        // 设置为当前选中的船只
        this.selectedShip = ship;

        // 显示虚影（原始位置）
        this.showGhostShip(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y, false);

        // 提升船只层级
        ship.zIndex = 100;
    }

    // 船只触摸移动事件
    private onShipTouchMove(event: cc.Event.EventTouch): void {
        const ship = event.currentTarget as cc.Node;

        if (this.isInputLocked) return; // 输入已锁定，不能再操作
        if (!ship['shipData'].isDragging) return;

        // 计算从开始触摸到当前位置的距离
        const currentTouchPos = event.getLocation();
        const startTouchPos = ship['shipData'].startTouchPos;
        const moveDistance = currentTouchPos.sub(startTouchPos).mag();

        // 累计总移动距离
        ship['shipData'].totalMoveDistance = moveDistance;

        // 只有移动距离超过阈值才标记为已移动
        if (moveDistance > TOUCH_MOVE_THRESHOLD) {
            ship['shipData'].hasMoved = true;
        }

        // 获取触摸位置
        const touchPos = this.boardNode.convertToNodeSpaceAR(event.getLocation());

        // 计算网格位置（精确到最近的格子中心）
        const gridX = Math.round((touchPos.x + this.boardNode.width / 2) / this.cellSize - 0.5);
        const gridY = this.boardSize - Math.round((touchPos.y + this.boardNode.height / 2) / this.cellSize - 0.5) - 1; // 关键修改：反转y轴计算

        // 只有在真正移动时才显示虚影和移动船只
        if (ship['shipData'].hasMoved) {
            // 检查是否超出边界或碰撞
            const isOutOfBounds = !this.isPositionValid(ship, gridX, gridY);
            const hasCollision = !isOutOfBounds && this.checkCollision(ship, gridX, gridY);

            // 更新虚影（超出边界显示红色，碰撞显示红色，正常显示灰色）
            // 仿照船只放置的边界判定，只在船只不会完全出界时显示虚影
            // 使用isPositionValid方法来检查是否应该显示虚影，避免虚影出界
            const shouldShowGhost = this.shouldShowGhostAtPosition(ship, gridX, gridY);
            if (shouldShowGhost) {
                this.showGhostShip(ship, gridX, gridY, isOutOfBounds || hasCollision);
            }

            // 临时移动船只到触摸位置（不更新网格位置）
            ship.x = touchPos.x;
            ship.y = touchPos.y;
        }
    }

    // 船只触摸结束事件
    private onShipTouchEnd(event: cc.Event.EventTouch): void {
        const ship = event.currentTarget as cc.Node;

        if (this.isInputLocked) {
            // 输入已锁定，强制停止拖拽状态并恢复位置
            ship['shipData'].isDragging = false;
            this.updateShipNodePosition(ship, false);
            ship.zIndex = 10;
            this.hideGhostShip(ship);
            return;
        }

        if (!ship['shipData'].isDragging) return;

        ship['shipData'].isDragging = false;

        // 清除用户交互标志
        this.isUserInteracting = false;

        // 双重检查：既检查hasMoved标志，也检查实际移动距离
        const totalMoveDistance = ship['shipData'].totalMoveDistance || 0;
        const isReallyMoved = ship['shipData'].hasMoved && totalMoveDistance > TOUCH_MOVE_THRESHOLD;

        // 检查是否是点击（没有真正移动）
        if (!isReallyMoved) {
            // 这是一个点击事件，执行旋转
           
            this.selectedShip = ship;

            // 记录旋转前的方向，用于判断是否旋转成功
            const originalDirection = ship['shipData'].direction;
            this.rotateShip(ship);

            // 只有旋转成功时才播放音效并发送DeployShips消息
            if (ship['shipData'].direction !== originalDirection) {
                cc.audioEngine.playEffect(this.rotateSound, false);
                // 旋转成功后发送DeployShips消息
                this.saveShipPositions();
            }

            // 恢复船只层级
            ship.zIndex = 10;
            return;
        }

        // 这是拖拽事件，处理位置更新
       

        // 获取触摸位置
        const touchPos = this.boardNode.convertToNodeSpaceAR(event.getLocation());

        // 计算网格位置（精确到最近的格子中心）
        const gridX = Math.round((touchPos.x + this.boardNode.width / 2) / this.cellSize - 0.5);
        const gridY = this.boardSize - Math.round((touchPos.y + this.boardNode.height / 2) / this.cellSize - 0.5) - 1; // 关键修改：反转y轴计算

        // 检查是否超出边界
        if (!this.isPositionValid(ship, gridX, gridY)) {
            // 超出边界，恢复到原始位置
            this.setShipPosition(ship, ship['shipData'].originalPosition.x, ship['shipData'].originalPosition.y);
            return;
        }

        // 检查碰撞
        if (this.checkCollision(ship, gridX, gridY)) {
            // 有碰撞，恢复到原始位置
            this.setShipPosition(ship, ship['shipData'].originalPosition.x, ship['shipData'].originalPosition.y);
            return;
        }

        // 位置有效，更新船只位置（用户拖拽成功，播放音效）
        this.setShipPosition(ship, gridX, gridY, false, true);

        // 成功放置船只后发送DeployShips消息
        this.saveShipPositions();

        // 恢复船只层级
        ship.zIndex = 10;
    }

    // 船只触摸取消事件
    private onShipTouchCancel(event: cc.Event.EventTouch): void {
        const ship = event.currentTarget as cc.Node;

        if (!ship['shipData'].isDragging) return;

        ship['shipData'].isDragging = false;

        // 清除用户交互标志
        this.isUserInteracting = false;

        // 恢复到原始位置
        this.setShipPosition(ship, ship['shipData'].originalPosition.x, ship['shipData'].originalPosition.y);

        // 恢复船只层级
        ship.zIndex = 10;
    }

    // 随机按钮点击事件
    private onRandomBtnClick(): void {
        if (this.isReady) return; // 已经准备好，不能再随机

        // 首先处理正在拖拽的船只，确保它们回到合法位置
        this.handleDraggingShipsBeforeDeployment();

        // 然后进行随机放置
        this.randomPlaceShips();

        // 随机放置后发送DeployShips消息
        this.saveShipPositions();
    }

    // 准备按钮点击事件
    private onReadyBtnClick(): void {
        if (this.isReady) return; // 已经准备好，不能再点击

        // 检查是否已收到GameStart消息，如果没有则禁止点击准备
        if (!this.hasReceivedGameStart) {
          
            return;
        }

        // 停止布置倒计时
        this.stopSetupCountdown();

        // 首先处理正在拖拽的船只，确保它们回到合法位置
        this.handleDraggingShipsBeforeDeployment();

        // 检查是否有碰撞
        let hasCollision = false;
        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                hasCollision = true;
                break;
            }
        }

        // 如果有碰撞，随机放置有碰撞的船只
        if (hasCollision) {
            for (let i = 0; i < this.ships.length; i++) {
                const ship = this.ships[i];
                if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                    this.randomPlaceShip(ship);
                }
            }
        }

        // 先发送DeployShips消息，然后完成准备
        this.saveShipPositions();

        // 延迟完成准备，确保DeployShips消息先发送
        this.scheduleOnce(() => {
            this.finishReady();
        }, 0.1);
    }

    // 随机放置单艘船只
    private randomPlaceShip(ship: cc.Node): void {
        let placed = false;

        // 创建一个二维数组来跟踪已占用的格子
        const occupiedGrid: boolean[][] = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(false));

        // 初始化已占用格子
        for (let i = 0; i < this.ships.length; i++) {
            if (this.ships[i] !== ship) {
                this.updateOccupiedGrid(this.ships[i], occupiedGrid);
            }
        }

        // 尝试放置最多1000次，防止无限循环
        for (let attempt = 0; attempt < 1000 && !placed; attempt++) {
            // 随机方向
            const direction = Math.floor(Math.random() * 4);

            // 随机位置
            const maxX = this.boardSize - (direction === Direction.UP || direction === Direction.DOWN ?
                ship['shipData'].type.size.width :
                ship['shipData'].type.size.height);

            const maxY = this.boardSize - (direction === Direction.UP || direction === Direction.DOWN ?
                ship['shipData'].type.size.height :
                ship['shipData'].type.size.width);

            const gridX = Math.floor(Math.random() * maxX);
            const gridY = Math.floor(Math.random() * maxY);

            // 检查是否与已放置的船只冲突
            if (!this.checkCollisionWithGrid(ship, gridX, gridY, direction, occupiedGrid)) {
                // 设置方向和位置
                this.setShipDirection(ship, direction);
                this.setShipPosition(ship, gridX, gridY, true);

                // 更新已占用格子
                this.updateOccupiedGrid(ship, occupiedGrid);

                placed = true;
            }
        }

        // 如果尝试了1000次还是无法放置，就强制放置在第一个空位
        if (!placed) {
            this.forcePlaceShip(ship, occupiedGrid);
            this.updateOccupiedGrid(ship, occupiedGrid);
        }
    }
    // 开始倒计时
    private startCountdown(): void {
        

        this.currentCountdown = this.countdownTime;
        this.countdownLabel.string = this.currentCountdown.toString();

      

        // 启动蓝色闹钟动画（开始时播放lannaozhong）
        if (this.blueClockSkeleton && !this.isReady) {
            this.blueClockSkeleton.setAnimation(0, 'lannaozhong', true);

        }

        // 创建倒计时定时器
        this.schedule(this.updateCountdown, 1);
    
    }

    // 更新倒计时
    private updateCountdown(): void {
        this.currentCountdown--;
        this.countdownLabel.string = this.currentCountdown.toString();
       

        // 根据剩余时间切换动画：剩余5秒时切换到警告动画
        if (this.currentCountdown === 5) {
            // 蓝色动画切换（玩家自己）
            if (this.blueClockSkeleton && !this.isReady) {
                // 确保动画以正常速度播放
                this.blueClockSkeleton.timeScale = 1;
                this.blueClockSkeleton.setAnimation(0, 'lantixing', true);

            }

            // 红色动画切换（对手）
            if (this.redClockSkeleton && !this.opponentReady) {
                // 确保动画以正常速度播放
                this.redClockSkeleton.timeScale = 1;
                this.redClockSkeleton.setAnimation(0, 'hongtixing', true);

            }
        }

        // 在最后2秒处理船只位置，但不发送DeployShips消息（等BattleStart后再发送）
        if (this.currentCountdown === 2 && !this.isReady) {


            // 处理正在拖拽的船只，确保它们回到合法位置
            this.handleDraggingShipsBeforeDeployment();

            // 自动处理碰撞的船只
            for (let i = 0; i < this.ships.length; i++) {
                const ship = this.ships[i];
                if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                    this.randomPlaceShip(ship);
                }
            }

            // 注释掉：不在这里发送DeployShips消息，等收到BattleStart后再发送
            // this.saveShipPositions();

        }

        // 在最后1秒禁用用户输入并强制修正船只位置
        if (this.currentCountdown === 1 && !this.isReady) {
           

            // 锁定用户输入，防止后续操作
            this.isInputLocked = true;

            // 禁用所有船只的拖动功能
            this.disableAllShipDragging();

            // 强制处理所有船只，确保它们在正确位置
            this.forceAllShipsToValidPositions();

            // 最终检查并修正所有船只位置
            this.finalizeShipPositionsBeforeBattle();

          
        }

        // 倒计时结束
        if (this.currentCountdown <= 0) {
            this.unschedule(this.updateCountdown);
            this.isCountdownFinished = true;

            // 如果还没有准备好，自动准备
            if (!this.isReady) {


                // 随机放置有碰撞的船只
                let hasCollision = false;
                for (let i = 0; i < this.ships.length; i++) {
                    const ship = this.ships[i];
                    if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                        this.randomPlaceShip(ship);
                        hasCollision = true;
                    }
                }

                // 自动准备时必须发送DeployShips消息（无论是否有碰撞修正）
                this.saveShipPositions();

                // 延迟完成准备，确保DeployShips消息先发送
                this.scheduleOnce(() => {
                    this.finishReady();
                }, 0.1);
            }
        }
    }

    // 完成准备
    private finishReady(): void {
        this.isReady = true;

        // 隐藏按钮
        this.randomBtn.node.active = false;
        this.readyBtn.node.active = false;

        // 在发送DeployShips之前，确保所有拖拽状态的船只都回到合法位置
        this.handleDraggingShipsBeforeDeployment();

        // 最终检查并修正所有船只位置
        this.finalizeShipPositionsBeforeBattle();

        // 发送当前皮肤设置到后端
        this.sendCurrentSkinToServer();

        // 注意：不在这里调用saveShipPositions()，避免重复发送DeployShips消息
        // 在手动点击准备按钮时，已经提前发送了DeployShips消息
        // 只有在自动准备（倒计时结束）时才需要发送

        // 更新引导文案
        this.updateGuideText();

        // 更新动画状态
        this.updateTimerAnimations();

        // 通知服务器或对手（在实际游戏中实现）
        this.notifyReadyToOpponent();
    }

    // 发送当前皮肤设置到后端
    private sendCurrentSkinToServer(): void {
        const skinManager = SkinManager.getInstance();
        const currentSkinId = skinManager.getCurrentSkinId();

        

        // 发送SetSkin消息到后端
        const setSkinRequest = {
            id: currentSkinId  // 要设置的皮肤ID (2100-2102)
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.SetSkin, setSkinRequest);
    }

    // 保存船只位置信息
    private saveShipPositions(): void {
        // 检查是否已收到GameStart消息，如果没有则不发送DeployShips
        if (!this.hasReceivedGameStart) {
        
            return;
        }

        // 检查输入是否被锁定（倒计时重置期间或最后1秒）
        if (this.isInputLocked) {
          
            return;
        }

        // 检查是否已经准备好
        if (this.isReady) {
       
            return;
        }

        const shipPositions = [];

        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (!ship || !ship['shipData']) {
                continue;
            }

            shipPositions.push({
                id: ship['shipData'].id,
                type: ship['shipData'].type.name,
                gridX: ship['shipData'].gridPosition.x,
                gridY: ship['shipData'].gridPosition.y,
                direction: ship['shipData'].direction
            });
        }

        // 检查是否与上次发送的数据相同，避免重复发送
        const currentPositionsStr = JSON.stringify(shipPositions);
        if (this.lastSentShipPositions === currentPositionsStr) {
            
            return;
        }

    

        try {
            WebSocketManager.GetInstance().sendMsg(MessageId.DeployShips, shipPositions);
            // 发送成功后更新上次发送的数据
            this.lastSentShipPositions = currentPositionsStr;
        } catch (error) {
            console.error("BattleshipGame: DeployShips消息发送失败", error);
        }

        cc.sys.localStorage.setItem('shipPositions', JSON.stringify(shipPositions));
    }

    // 通知对手已准备好 - 发送NoticePlayerReady消息
    private notifyReadyToOpponent(): void {
       
        const readyRequest = {
            ready: true  // 根据API文档，固定为true
        };

        try {
            WebSocketManager.GetInstance().sendMsg(MessageId.NoticePlayerReady, readyRequest);
           
        } catch (error) {
            console.error("BattleshipGame: NoticePlayerReady消息发送失败", error);
        }
    }

    // 更新引导文案
    private updateGuideText(): void {
        if (!this.isReady && !this.opponentReady) {
            // 双方都未部署
            let str1 = window.getLocalizedStr("twonotready")
            this.guideLabel.string = str1;
        } else if (this.isReady && !this.opponentReady) {
            // 我部署了，对方没部署
            let str2 = window.getLocalizedStr("uarenotready")
            this.guideLabel.string = str2;
        } else if (!this.isReady && this.opponentReady) {
            // 对方部署了，我没部署
            let str3 = window.getLocalizedStr("imnotready")
            this.guideLabel.string = str3;
        } else {
            // 双方都部署了
            let str4 = window.getLocalizedStr("weareallready")
            this.guideLabel.string = str4;
        }
    }

    // 动画状态跟踪
    private currentBlueTimerAnimation: string = '';
    private currentBlueClockAnimation: string = '';
    private currentRedTimerAnimation: string = '';
    private currentRedClockAnimation: string = '';

    // 更新倒计时动画状态（参考updateGuideText的逻辑）
    private updateTimerAnimations(): void {
      

        // 处理自己的动画（蓝色）- 只有自己的状态变化才影响自己的动画
        if (!this.isReady && this.isSetupCountdownActive) {
            // 我还没准备好且倒计时激活 - 播放倒计时动画
            if (this.blueTimer && this.currentBlueTimerAnimation !== 'jishi') {
                this.startJishiAnimationWithTimeScale(this.blueTimer, this.setupTimeLeft, 30);
                this.currentBlueTimerAnimation = 'jishi';

            }
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.node.active = true;
                // 根据剩余时间决定播放哪个动画
                if (this.setupTimeLeft > 5) {
                    if (this.currentBlueClockAnimation !== 'lannaozhong') {
                        this.blueClockSkeleton.setAnimation(0, 'lannaozhong', true);
                        this.currentBlueClockAnimation = 'lannaozhong';
                     
                    }
                } else {
                    if (this.currentBlueClockAnimation !== 'lantixing') {
                        this.blueClockSkeleton.setAnimation(0, 'lantixing', true);
                        this.currentBlueClockAnimation = 'lantixing';
                  
                    }
                }
            }
        } else if (this.isReady) {
            // 只有当我准备好时才停止我的动画
            if (this.blueTimer) {
                this.blueTimer.clearTracks();
                this.currentBlueTimerAnimation = '';

            }
            if (this.blueClockSkeleton) {
                this.blueClockSkeleton.clearTracks();
                this.blueClockSkeleton.node.active = true; // 保持可见，不隐藏
                this.currentBlueClockAnimation = '';

            }
        }

        // 同时更新对手动画
        this.updateOpponentTimerAnimations();
    }

    // 单独处理对手动画状态
    private updateOpponentTimerAnimations(): void {
        

        // 处理对手的动画（红色）- 只有对手状态变化才影响对手动画
        if (!this.opponentReady && this.isSetupCountdownActive) {
            // 对手还没准备好且倒计时激活 - 播放对手倒计时动画
            if (this.redTimer && this.currentRedTimerAnimation !== 'jishi') {
      
                this.startJishiAnimationWithTimeScale(this.redTimer, this.setupTimeLeft, 30);
                this.currentRedTimerAnimation = 'jishi';

            }
            if (this.redClockSkeleton) {
                this.redClockSkeleton.node.active = true;
                // 根据剩余时间决定播放哪个动画
                if (this.setupTimeLeft > 5) {
                    if (this.currentRedClockAnimation !== 'hongnaozhong') {
                        this.redClockSkeleton.setAnimation(0, 'hongnaozhong', true);
                        this.currentRedClockAnimation = 'hongnaozhong';
                       
                    }
                } else {
                    if (this.currentRedClockAnimation !== 'hongtixing') {
                        this.redClockSkeleton.setAnimation(0, 'hongtixing', true);
                        this.currentRedClockAnimation = 'hongtixing';
                       
                    }
                }
            }
        } else if (this.opponentReady) {
            // 对手已经准备好 - 停止对手动画但保持节点可见
            if (this.redTimer) {
    
                this.redTimer.clearTracks();
                this.currentRedTimerAnimation = '';

            }
            if (this.redClockSkeleton) {
                this.redClockSkeleton.clearTracks();
                this.redClockSkeleton.node.active = true; // 保持可见，不隐藏
                this.currentRedClockAnimation = '';

            }
        }
    }

    // 隐藏battleship页面并在后台准备creatship页面
    public hideBattleshipAndPrepareCreatShip(): void {
        

        // 重置游戏状态
        this.resetGameState();
      

        // 隐藏battleship相关UI，显示creatship相关UI
        this.switchUIToCreatShip();
      

        // 重新随机放置船只
        this.randomPlaceShips();

        // 新游戏随机放置后发送DeployShips消息
        this.saveShipPositions();

        // 启用船只拖动功能（新游戏开始时允许拖动）
        this.enableAllShipDragging();
    }

    // ==================== 断线重连相关方法 ====================

    // 为断线重连重置场景
    private resetForReconnect(): void {
        // 重置游戏状态（但保留船只位置）
        this.resetGameStateForReconnect();

        // 隐藏所有UI，准备根据重连数据恢复
        this.hideAllUI();

    }

    // 为断线重连重置游戏状态（保留船只位置）
    private resetGameStateForReconnect(): void {
        this.isReady = false;
        this.opponentReady = false;
        this.isMyTurn = false;
        this.isCountdownFinished = false;
        this.currentCountdown = this.countdownTime;

        // 重置输入锁定状态
        this.isInputLocked = false;

        // 重置上次发送的船只位置数据
        this.lastSentShipPositions = "";

        // 重置用户交互标志
        this.isUserInteracting = false;

        // 停止倒计时
        this.unschedule(this.updateCountdown);

        // 停止布置倒计时
        this.stopSetupCountdown();

        // 停止对手倒计时动画
        this.stopOpponentCountdown();

        // 重置所有动画到初始状态
        this.resetAnimationsToInitialState();

        // 清理棋盘上的攻击标记等，但保留船只
        this.clearBoardElementsButKeepShips();

        // 注意：不调用clearShipPositions()，保留船只当前位置
        
    }

    // 清理棋盘元素但保留船只位置（重连专用）
    private clearBoardElementsButKeepShips(): void {
        

        if (!this.boardNode) {
            
            return;
        }

        // 获取所有子节点
        const children = this.boardNode.children.slice();

        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 保留船只节点及其位置，清理其他所有节点
            if (child.name && child.name.startsWith('ship_')) {
                // 这是船只节点，保留但清理其虚影
                const shipData = child['shipData'];
                if (shipData && shipData.ghostNode) {
                    shipData.ghostNode.removeFromParent();
                    shipData.ghostNode = null;
                }
                // 保留船只的位置信息，不重置gridPosition
                continue;
            }

            // 清理所有非船只节点（攻击标记、爆炸效果等）
            
            child.removeFromParent();
        }

    
    }

    // 确保船只存在且正确初始化（战斗状态专用）
    private ensureShipsExistForBattle(): void {
        
        // 检查船只数组是否为空或船只节点是否被销毁
        let needRecreate = false;

        if (!this.ships || this.ships.length === 0) {
           
            needRecreate = true;
        } else {
            // 检查船只节点是否有效
            for (let i = 0; i < this.ships.length; i++) {
                if (!this.ships[i] || !cc.isValid(this.ships[i])) {
                    
                    needRecreate = true;
                    break;
                }
            }
        }

        if (needRecreate) {
           
            // 清空船只数组
            this.ships = [];
            // 重新创建所有船只（与onLoad中的逻辑完全一致）
            this.createShips();
        } else {
            
        }
    }

    // 隐藏所有UI
    private hideAllUI(): void {
        this.GameBoard.active = false;
        this.MainGameUI.active = false;
        this.ShipBattle.active = false;
        this.Aralm.active = false;
        this.guideLabel.node.active = false;
    }

    // 恢复等待状态
    private restoreWaitingState(reconnectData: ReconnectGameData): void {
       

        // 显示等待界面
        this.GameBoard.active = false;
        this.MainGameUI.active = false;
        this.ShipBattle.active = false;
        this.Aralm.active = true;
        this.guideLabel.node.active = true;

        // 设置棋盘位置和缩放为等待状态（与Creatship页面保持一致）
        this.boardNode.setPosition(0, 241);
        this.boardNode.setScale(1, 1);
        this.boardNode.width = 644;
        this.boardNode.height = 644;

        // 显示准备和随机按钮
        this.randomBtn.node.active = true;
        this.readyBtn.node.active = true;

        // 等待状态不应该随机分布船只，保持当前船只位置
        

        // 启用船只拖动功能（等待状态允许拖动）
        this.enableAllShipDragging();

        // 更新引导文案
        this.updateGuideText();

        // 根据倒计时恢复状态和jishi动画
        if (reconnectData.countDown > 0) {
           

            // 先停止现有倒计时，避免冲突
            this.stopSetupCountdown();

            this.currentCountdown = reconnectData.countDown;
            this.setupTimeLeft = reconnectData.countDown; // 设置布置倒计时剩余时间
            this.countdownLabel.string = this.currentCountdown.toString();

            // 先同步对手动画状态（更新opponentReady状态）
            this.syncOpponentAnimationState(reconnectData);

            // 启动布置倒计时（使用恢复的时间）
            this.startSetupCountdownWithTime(this.setupTimeLeft);

            // 同步jishi动画状态（此时opponentReady状态已正确）
            this.updateTimerAnimations();
        }
    }

    // 恢复部署战舰状态（实际上是等待状态的一种，使用相同的棋盘位置）
    private restoreDeploymentState(reconnectData: ReconnectGameData): void {
       

        // 显示等待/部署界面
        this.GameBoard.active = false;
        this.MainGameUI.active = false;
        this.ShipBattle.active = false;
        this.Aralm.active = true;
        this.guideLabel.node.active = true;

        // 设置棋盘位置和缩放为等待状态（与Creatship页面保持一致）
        this.boardNode.setPosition(0, 241);
        this.boardNode.setScale(1, 1);
        this.boardNode.width = 644;
        this.boardNode.height = 644;

        // 显示准备和随机按钮
        this.randomBtn.node.active = true;
        this.readyBtn.node.active = true;

        // 恢复船只部署状态 - 优先使用服务器数据，保持断线前的位置
        if (reconnectData.playerShips && reconnectData.playerShips.length > 0) {
            // 有服务器数据，使用服务器数据恢复船只位置
            this.restorePlayerShips(reconnectData.playerShips);
        } else {
            // 没有服务器数据，保持当前船只位置（断线前的位置）
           
            // 不调用randomPlaceShips()，保持船只当前位置不变
        }

        // 启用船只拖动功能（部署状态允许拖动）
        this.enableAllShipDragging();

        // 恢复部署状态
        if (reconnectData.deploymentStatus) {
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
            const isMyDeploymentComplete = reconnectData.deploymentStatus[myUserId];

            if (isMyDeploymentComplete) {
                this.isReady = true;
                this.readyBtn.interactable = false;
                this.readyBtn.node.getChildByName("Background").getComponent(cc.Sprite).spriteFrame = this.readyBtnPressedSprite;
                this.readyBtn.node.getChildByName("Label").getComponent(cc.Label).string = "已准备";

                // 隐藏按钮
                this.randomBtn.node.active = false;
                this.readyBtn.node.active = false;
            }
        }

        // 恢复倒计时和jishi动画
        if (reconnectData.countDown > 0) {
           

            // 先停止现有倒计时，避免冲突
            this.stopSetupCountdown();

            this.setupTimeLeft = reconnectData.countDown;

            // 先同步对手动画状态（更新opponentReady状态）
            this.syncOpponentAnimationState(reconnectData);

            // 启动布置倒计时（使用恢复的时间）
            this.startSetupCountdownWithTime(this.setupTimeLeft);

            // 同步jishi动画状态（此时opponentReady状态已正确）
            this.updateTimerAnimations();
        } else if (reconnectData.countDown === 0) {
            // 倒计时为0，需要自动发送DeployShips确保前后端数据一致
            

            // 检查是否已经准备好
            if (!this.isReady) {
                // 自动处理碰撞的船只
                for (let i = 0; i < this.ships.length; i++) {
                    const ship = this.ships[i];
                    if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                        this.randomPlaceShip(ship);
                    }
                }

                // 自动完成准备并发送DeployShips
                this.saveShipPositions();

                // 延迟完成准备，确保DeployShips消息先发送
                this.scheduleOnce(() => {
                    this.finishReady();
                }, 0.1);
                
            } else {
                // 已经准备好，但为了确保数据一致性，重新发送DeployShips
                this.saveShipPositions();
                
            }
        }

        // 更新引导文案
        this.updateGuideText();
    }

    // 恢复战斗状态（为BattleshipGame设置正确的棋盘位置）
    private restoreBattleStateForBattleship(reconnectData: ReconnectGameData): void {
        

        // 设置界面状态为战斗状态
        this.GameBoard.active = true;
        this.MainGameUI.active = true;
        this.ShipBattle.active = true;
        this.Aralm.active = false;
        this.guideLabel.node.active = false;

        // 设置棋盘位置和缩放为战斗状态（与startGame方法保持一致）
        this.boardNode.setPosition(0, -469);
        this.boardNode.setScale(0.598, 0.598);

        // 隐藏布置阶段的按钮（gameStatus==3时必须隐藏）
        this.randomBtn.node.active = false;
        this.readyBtn.node.active = false;
        

        // 恢复船只部署状态（战斗阶段船只位置固定，不能拖动）
        if (reconnectData.playerShips && reconnectData.playerShips.length > 0) {
            // 有服务器数据，使用服务器数据恢复船只位置
           
            this.restoreBattleShips(reconnectData.playerShips);
        } else {
            // 没有服务器数据（玩家在部署阶段断线且未点击准备），重新生成船只布局
          

            // 确保船只存在
            this.ensureShipsExistForBattle();

            // 重新随机生成船只位置（参考初始生成逻辑）
            this.randomPlaceShips();

            // 断线重连随机放置后发送DeployShips消息
            this.saveShipPositions();

            // 禁用所有船只的拖动功能（战斗阶段）
            this.disableAllShipDragging();

            
        }

        // 设置回合状态
        if (reconnectData.currentAttacker) {
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
            this.isMyTurn = (reconnectData.currentAttacker === myUserId);
            
        }

        // 设置战斗状态标志
        this.isReady = true;
        this.opponentReady = true;

        // 禁用所有船只的拖动功能（战斗阶段）
        this.disableAllShipDragging();

        // 停止布置阶段的倒计时
        this.stopSetupCountdown();

       
    }

    // 恢复玩家船只部署（布置阶段使用）
    private restorePlayerShips(playerShips: ShipWithAnchor[]): void {
       

        // 注意：不调用clearShipPositions()，直接更新船只位置避免重复刷新

        // 根据服务器数据恢复船只位置
        this.generateShipsFromBackendData(playerShips);
    }

    // 恢复战斗状态的船只（战斗阶段使用，禁用拖动）
    private restoreBattleShips(playerShips: ShipWithAnchor[]): void {
        

        // 清空当前船只
        this.clearShipPositions();

        // 根据服务器数据恢复船只位置
        this.generateShipsFromBackendData(playerShips);

        // 禁用所有船只拖动（战斗阶段船只位置固定）
        this.disableAllShipDragging();
    }

    // 根据后端数据生成船只位置（应用自定义偏移量）
    private generateShipsFromBackendData(playerShips: ShipWithAnchor[]): void {
        

        // 根据服务器数据设置船只位置
        for (const shipData of playerShips) {
            const ship = this.findShipById(shipData.id);
            if (ship) {
                // 检查后端数据是否包含锚点坐标
                const anchorX = (shipData as any).anchorX;
                const anchorY = (shipData as any).anchorY;

                if (anchorX !== undefined && anchorY !== undefined) {
                    // 后端返回的是锚点坐标
                   

                    // 直接设置船只数据（锚点坐标就是网格坐标）
                    ship['shipData'].gridPosition = cc.v2(anchorX, anchorY);
                    ship['shipData'].direction = shipData.direction;

                    // 直接设置船只旋转角度（避免调用setShipDirection的副作用）
                    ship.rotation = shipData.direction * 90;

                    // 直接计算并设置船只位置（使用锚点坐标）
                    this.setShipPositionDirectly(ship, anchorX, anchorY, shipData.direction);

                    
                } else {
                    // 后端返回的是网格坐标（兼容旧版本）
                  
                    // 直接设置船只数据（不进行边界检查和碰撞检测）
                    ship['shipData'].gridPosition = cc.v2(shipData.gridX, shipData.gridY);
                    ship['shipData'].direction = shipData.direction;

                    // 直接设置船只旋转角度（避免调用setShipDirection的副作用）
                    ship.rotation = shipData.direction * 90;

                    // 直接计算并设置船只位置（使用与updateShipNodePosition相同的逻辑）
                    this.setShipPositionDirectly(ship, shipData.gridX, shipData.gridY, shipData.direction);

                   
                }
            } else {
                console.warn(`BattleshipGame: 找不到船只 ID ${shipData.id}`);
            }
        }
    }

    // 直接设置船只位置（用于断线重连，跳过所有验证）
    private setShipPositionDirectly(ship: cc.Node, gridX: number, gridY: number, direction: number): void {
        // 将网格坐标转换为世界坐标（精确对齐格子中心）
        const worldX = (gridX + 0.5) * this.cellSize;
        const worldY = (this.boardSize - gridY - 0.5) * this.cellSize;

    

        // 应用自定义偏移量逻辑（与updateShipNodePosition完全一致）
        if (ship['shipData'].id === 3 || ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (direction === Direction.UP || direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
              
            } else {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
             
            }
        } else if (ship['shipData'].id === 1) {
            if (direction === Direction.UP || direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
             
            } else {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
               
            }
        } else {
            ship.x = worldX - this.boardNode.width / 2;
            ship.y = worldY - this.boardNode.height / 2;
           
        }
    }

    // 应用自定义偏移量位置（基于updateShipNodePosition的偏移逻辑）
    private applyCustomOffsetPosition(ship: cc.Node): void {
        // 将网格坐标转换为世界坐标（精确对齐格子中心）
        const worldX = (ship['shipData'].gridPosition.x + 0.5) * this.cellSize;
        const worldY = (this.boardSize - ship['shipData'].gridPosition.y - 0.5) * this.cellSize;

        // 应用您的自定义偏移量逻辑
        if (ship['shipData'].id === 3 || ship['shipData'].id === 5 || ship['shipData'].id === 6) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
            } else {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
            }
        } else if (ship['shipData'].id === 1) {
            if (ship['shipData'].direction === Direction.UP || ship['shipData'].direction === Direction.DOWN) {
                ship.x = worldX - this.boardNode.width / 2 + 32;
                ship.y = worldY - this.boardNode.height / 2;
            } else {
                ship.x = worldX - this.boardNode.width / 2;
                ship.y = worldY - this.boardNode.height / 2 + 32;
            }
        } else {
            ship.x = worldX - this.boardNode.width / 2;
            ship.y = worldY - this.boardNode.height / 2;
        }
    }

    // 根据ID查找船只
    private findShipById(shipId: number): cc.Node | null {
        for (const ship of this.ships) {
            if (ship['shipData'].id === shipId) {
                return ship;
            }
        }
        return null;
    }

    // 将网格坐标转换为世界坐标（与updateShipNodePosition保持一致）
    private gridToWorldPosition(gridX: number, gridY: number): cc.Vec2 {
        const worldX = (gridX + 0.5) * this.cellSize;
        const worldY = (this.boardSize - gridY - 0.5) * this.cellSize;
        return cc.v2(worldX - this.boardNode.width / 2, worldY - this.boardNode.height / 2);
    }

    // 同步对手动画状态（部署阶段使用）
    private syncOpponentAnimationState(reconnectData: ReconnectGameData): void {


        // 检查对手准备状态
        if (reconnectData.deploymentStatus) {
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;

            // 遍历部署状态，找到对手的状态
            for (const userId in reconnectData.deploymentStatus) {
                if (userId !== myUserId) {
                    const opponentReady = reconnectData.deploymentStatus[userId];
                    this.opponentReady = opponentReady;


                    // 如果对手还没准备好且有倒计时，启动对手闹钟动画（jishi动画由updateTimerAnimations处理）
                    if (!opponentReady && reconnectData.countDown > 0) {
            
                        this.startOpponentCountdown();
                    } else if (opponentReady) {
                        // 对手已准备好，停止对手闹钟动画（jishi动画也会在updateTimerAnimations中停止）
   
                        this.stopOpponentCountdown();
                    }
                    break;
                }
            }
        }
    }

    // 禁用船只拖动（战斗阶段使用）
    private disableShipDragging(ship: cc.Node): void {
        if (!ship) return;

        // 移除所有触摸事件监听器
        ship.off(cc.Node.EventType.TOUCH_START);
        ship.off(cc.Node.EventType.TOUCH_MOVE);
        ship.off(cc.Node.EventType.TOUCH_END);
        ship.off(cc.Node.EventType.TOUCH_CANCEL);

        // 标记为不可拖动
        if (ship['shipData']) {
            ship['shipData'].isDragging = false;
        }


    }

    // 禁用所有船只的拖动功能（战斗阶段使用）
    private disableAllShipDragging(): void {
       

        for (let i = 0; i < this.ships.length; i++) {
            this.disableShipDragging(this.ships[i]);

            // 额外确保船只状态正确
            const ship = this.ships[i];
            if (ship && ship['shipData']) {
                ship['shipData'].isDragging = false;
                ship.zIndex = 10; // 恢复正常层级
                this.hideGhostShip(ship); // 清理虚影
            }
        }
    }

    // 启用船只拖动（部署阶段使用）
    private enableShipDragging(ship: cc.Node): void {
        if (!ship) return;

        // 重新注册触摸事件（拖拽和旋转）
        ship.on(cc.Node.EventType.TOUCH_START, this.onShipTouchStart, this);
        ship.on(cc.Node.EventType.TOUCH_MOVE, this.onShipTouchMove, this);
        ship.on(cc.Node.EventType.TOUCH_END, this.onShipTouchEnd, this);
        ship.on(cc.Node.EventType.TOUCH_CANCEL, this.onShipTouchCancel, this);

    }

    // 启用所有船只的拖动功能（部署阶段使用）
    private enableAllShipDragging(): void {

        for (let i = 0; i < this.ships.length; i++) {
            this.enableShipDragging(this.ships[i]);
        }
    }

    // 重置游戏状态
    private resetGameState(): void {
        this.isReady = false;
        this.opponentReady = false;
        this.isMyTurn = false;
        this.isCountdownFinished = false;
        this.currentCountdown = this.countdownTime;

        // 重置GameStart标志
        this.hasReceivedGameStart = false;

        // 重置输入锁定状态
        this.isInputLocked = false;

        // 重置上次发送的船只位置数据
        this.lastSentShipPositions = "";

        // 重置用户交互标志
        this.isUserInteracting = false;

        // 停止倒计时
        this.unschedule(this.updateCountdown);

        // 停止布置倒计时
        this.stopSetupCountdown();

        // 停止对手倒计时动画
        this.stopOpponentCountdown();

        // 重置所有动画到初始状态
        this.resetAnimationsToInitialState();

        // 彻底清理棋盘上的所有预制体
        this.clearAllBoardElements();

        // 重置船只状态
        this.clearShipPositions();
    }

    // 登录时的游戏重置 - 用于处理断网后收不到大结算通知的情况
    private resetGameFromLogin(): void {
        

        // 重置游戏状态
        this.isReady = false;
        this.opponentReady = false;
        this.isMyTurn = false;
        this.isCountdownFinished = false;
        this.currentCountdown = this.countdownTime;

        // 重置GameStart标志
        this.hasReceivedGameStart = false;

        // 重置输入锁定状态
        this.isInputLocked = false;

        // 重置上次发送的船只位置数据
        this.lastSentShipPositions = "";

        // 重置用户交互标志
        this.isUserInteracting = false;

        // 停止倒计时
        this.unschedule(this.updateCountdown);

        // 停止布置倒计时
        this.stopSetupCountdown();

        // 停止对手倒计时动画
        this.stopOpponentCountdown();

        // 重置所有动画到初始状态
        this.resetAnimationsToInitialState();

        // 彻底清理棋盘上的所有预制体
        this.clearAllBoardElements();

        // 重置船只状态
        this.clearShipPositions();

        // 隐藏battleship相关UI，显示creatship相关UI
        this.switchUIToCreatShip();

        // 重新随机放置船只
        this.randomPlaceShips();

        // 启用船只拖动功能
        this.enableAllShipDragging();

       
    }

    // 重置所有动画到初始状态
    private resetAnimationsToInitialState(): void {
       

        // 重置动画状态跟踪
        this.currentBlueTimerAnimation = '';
        this.currentBlueClockAnimation = '';
        this.currentRedTimerAnimation = '';
        this.currentRedClockAnimation = '';

        // 重置蓝色头像圈动画（玩家）
        if (this.blueTimer) {
            this.blueTimer.clearTracks();
            this.blueTimer.node.active = true;
        }

        // 重置红色头像圈动画（对手）
        if (this.redTimer) {
            this.redTimer.clearTracks();
            this.redTimer.node.active = true;
        }

        // 重置蓝色闹钟动画 - 保持可见但不播放动画
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.clearTracks();
            this.blueClockSkeleton.node.active = true; // 改为可见
        }

        // 重置红色闹钟动画 - 保持可见但不播放动画
        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
            this.redClockSkeleton.node.active = true; // 改为可见
        }

        // 显示 CreatShip 页面的 Alarm 节点
        if (this.Aralm) {
            this.Aralm.active = true;
           
        }

        
    }

    // 切换UI到船只布置界面
    private switchUIToCreatShip(): void {
        // 恢复船只布置界面的UI状态
        this.GameBoard.active = false;
        this.guideLabel.node.active = true;
        this.MainGameUI.active = false;
        this.ShipBattle.active = false;

        // 恢复棋盘到creatship页面的原始位置和大小
        this.boardNode.setPosition(0, 241);
        this.boardNode.setScale(1, 1);
        this.boardNode.width = 644;
        this.boardNode.height = 644;

        // 重新显示准备和随机按钮
        this.randomBtn.node.active = true;
        this.readyBtn.node.active = true;

        // 更新指导文本
        this.updateGuideText();

        // 等待收到 GameStart 消息时才启动倒计时
        
    }

    // 开始游戏（在实际游戏中实现）
    private startGame(data: any): void {
        let firstPlayer = data.firstPlayer;
        if(firstPlayer==GlobalBean.GetInstance().loginData.userInfo.userId)
        {
             this.isMyTurn = true;
        }
        else
        {
            this.isMyTurn = false;
        }

        // 处理正在拖拽的船只，确保它们回到合法位置
        this.handleDraggingShipsBeforeBattle();

        // 在实际游戏中，这里应该切换到游戏场景
        this.boardNode.setPosition(0, -469);
        this.boardNode.setScale(0.598, 0.598);
        this.GameBoard.active = true;
        this.guideLabel.node.active = false;
        this.MainGameUI.active = true;
        this.ShipBattle.active = true;
        this.Aralm.active = false;

        // 隐藏摆船页面的随机和确定按钮
        this.randomBtn.node.active = false;
        this.readyBtn.node.active = false;

        // 进入战斗阶段，禁用所有船只的拖动功能
        this.disableAllShipDragging();


    }

    // 处理正在拖拽的船只，确保它们回到合法位置
    private handleDraggingShipsBeforeBattle(): void {
        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (ship && ship['shipData']) {
                // 如果船只正在拖拽状态
                if (ship['shipData'].isDragging) {
                    // 停止拖拽状态
                    ship['shipData'].isDragging = false;

                    // 恢复到最后一次合法位置（originalPosition）
                    this.setShipPosition(ship, ship['shipData'].originalPosition.x, ship['shipData'].originalPosition.y);

                    // 恢复船只层级
                    ship.zIndex = 10;
                }

                // 清理虚影（无论是否在拖拽状态）
                this.hideGhostShip(ship);
            }
        }
    }

    // 处理正在拖拽的船只，在部署阶段倒计时时使用
    private handleDraggingShipsBeforeDeployment(): void {

        let draggingCount = 0;

        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (ship && ship['shipData']) {
                // 如果船只正在拖拽状态
                if (ship['shipData'].isDragging) {
                    draggingCount++;


                    // 停止拖拽状态
                    ship['shipData'].isDragging = false;

                    // 恢复到最后一次合法位置（originalPosition）
                    this.setShipPosition(ship, ship['shipData'].originalPosition.x, ship['shipData'].originalPosition.y);

                    // 恢复船只层级
                    ship.zIndex = 10;
                } else {
                    // 即使不在拖拽状态，也要确保船只位置正确对应到网格位置
                    // 这是为了防止船只的实际显示位置与gridPosition不一致
                    this.updateShipNodePosition(ship, false);
                }

                // 清理虚影（无论是否在拖拽状态）
                this.hideGhostShip(ship);
            }
        }


    }

    // 强制所有船只回到有效位置（最后1秒使用）
    private forceAllShipsToValidPositions(): void {
       
        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (ship && ship['shipData']) {
                // 强制停止拖拽状态
                ship['shipData'].isDragging = false;

                // 恢复船只层级
                ship.zIndex = 10;

                // 清理虚影
                this.hideGhostShip(ship);

                // 强制将船只位置设置为其gridPosition对应的正确显示位置
                this.updateShipNodePosition(ship, false);

                
            }
        }
    }

    // 最终确认所有船只位置合法，在战斗开始前使用
    private finalizeShipPositionsBeforeBattle(): void {
        for (let i = 0; i < this.ships.length; i++) {
            const ship = this.ships[i];
            if (ship && ship['shipData']) {
                // 检查船只位置是否合法
                const isValidPosition = this.isPositionValid(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y);
                const hasCollision = this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y);

                if (!isValidPosition || hasCollision) {
                    
                    this.randomPlaceShip(ship);
                }
            }
        }
    }

    // ==================== 布置战舰倒计时相关方法 ====================

    // 启动布置倒计时
    private startSetupCountdown(): void {
        // 检查是否已经在倒计时中，避免重复启动
        if (this.isSetupCountdownActive) {
            
            return;
        }

        
        this.startSetupCountdownWithTime(30);
    }

    // 启动布置倒计时（指定时间）
    private startSetupCountdownWithTime(timeLeft: number): void {
       

        // 如果用户正在交互，延迟重置倒计时
        if (this.isUserInteracting) {
           
            this.scheduleOnce(() => {
                this.startSetupCountdownWithTime(timeLeft);
            }, 0.5);
            return;
        }

        if (this.isSetupCountdownActive) {
            // 如果已经在倒计时，先停止
          

            // 临时锁定用户输入，防止重置期间的操作导致游戏卡住
            this.isInputLocked = true;

            this.stopSetupCountdown();

            // 使用异步方式延迟解锁，确保停止操作完成
            this.scheduleOnce(() => {
                this.isInputLocked = false;
           
            }, 0.1);
        }

        this.isSetupCountdownActive = true;
        this.setupTimeLeft = timeLeft;

        // 更新动画状态
        this.updateTimerAnimations();

        // 启动倒计时定时器

        this.setupCountdownTimer = setInterval(() => {
            this.setupTimeLeft--;
           

            // 更新动画状态（包括5秒警告切换）
            this.updateTimerAnimations();

            // 在最后2秒发送DeployShips消息
            if (this.setupTimeLeft === 2 && !this.isReady) {
              

                // 处理正在拖拽的船只，确保它们回到合法位置
                this.handleDraggingShipsBeforeDeployment();

                // 自动处理碰撞的船只
                for (let i = 0; i < this.ships.length; i++) {
                    const ship = this.ships[i];
                    if (this.checkCollision(ship, ship['shipData'].gridPosition.x, ship['shipData'].gridPosition.y)) {
                        this.randomPlaceShip(ship);
                    }
                }

                // 发送DeployShips消息
                this.saveShipPositions();
       
            }

            // 在最后1秒禁用用户输入并强制修正船只位置
            if (this.setupTimeLeft === 1 && !this.isReady) {
              

                // 锁定用户输入，防止后续操作
                this.isInputLocked = true;

                // 禁用所有船只的拖动功能
                this.disableAllShipDragging();

                // 强制处理所有船只，确保它们在正确位置
                this.forceAllShipsToValidPositions();

                // 最终检查并修正所有船只位置
                this.finalizeShipPositionsBeforeBattle();

               
            }

            // 倒计时结束
            if (this.setupTimeLeft <= 0) {

                this.onSetupCountdownFinished();
            }
        }, 1000);

    }

    // 停止布置倒计时
    private stopSetupCountdown(): void {
        if (!this.isSetupCountdownActive) return;

       
        this.isSetupCountdownActive = false;

        // 清除定时器
        if (this.setupCountdownTimer) {
            clearInterval(this.setupCountdownTimer);
            this.setupCountdownTimer = null;
        }

        // 更新动画状态（停止动画）
        this.updateTimerAnimations();

       
    }

    // 启动玩家倒计时动画
    private startPlayerTimerAnimation(): void {
        // 启动蓝色头像圈倒计时动画（使用当前剩余时间）
        if (this.blueTimer) {
            this.startJishiAnimationWithTimeScale(this.blueTimer, this.setupTimeLeft, 30);
        }

        // 启动蓝色闹钟动画
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.setAnimation(0, 'lannaozhong', true);
        }
    }

    // 停止玩家倒计时动画
    private stopPlayerTimerAnimation(): void {
        // 重置蓝色头像圈动画
        if (this.blueTimer) {
            this.blueTimer.clearTracks();
        }

        // 停止蓝色闹钟动画
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.clearTracks();
        }
    }

    // 启动对手倒计时动画
    private startOpponentTimerAnimation(): void {
        // 启动红色头像圈倒计时动画（使用当前剩余时间）
        if (this.redTimer) {
            this.startJishiAnimationWithTimeScale(this.redTimer, this.setupTimeLeft, 30);
        }

        // 启动红色闹钟动画
        if (this.redClockSkeleton) {
            this.redClockSkeleton.setAnimation(0, 'hongnaozhong', true);
        }
    }

    // 启动对手倒计时动画
    private startOpponentCountdown(): void {
        // 启动红色闹钟动画（开始时播放hongnaozhong）
        if (this.redClockSkeleton && !this.opponentReady) {
            this.redClockSkeleton.setAnimation(0, 'hongnaozhong', true);
          
        }
    }

    // 停止对手倒计时动画
    private stopOpponentCountdown(): void {
        // 停止红色闹钟动画
        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
        
        }
    }

    // 停止对手倒计时动画
    private stopOpponentTimerAnimation(): void {
        // 重置红色头像圈动画
        if (this.redTimer) {
            this.redTimer.clearTracks();
        }

        // 停止红色闹钟动画
        if (this.redClockSkeleton) {
            this.redClockSkeleton.clearTracks();
        }
    }

    // 切换到警告动画（剩余5秒时）
    private switchToWarningAnimation(): void {
        // 切换蓝色闹钟到提醒动画
        if (this.blueClockSkeleton) {
            this.blueClockSkeleton.setAnimation(0, 'lantixing', true);
        }
    }

    // 布置倒计时结束处理
    private onSetupCountdownFinished(): void {
        // 停止倒计时
        this.stopSetupCountdown();

        // 如果玩家还没准备好，自动准备
        if (!this.isReady) {
            // 自动准备时必须发送DeployShips消息
            this.saveShipPositions();

            // 延迟完成准备，确保DeployShips消息先发送
            this.scheduleOnce(() => {
                this.finishReady();
            }, 0.1);
        }
    }

    // 切换对手到警告动画
    private switchOpponentToWarningAnimation(): void {
        if (this.redClockSkeleton) {
            this.redClockSkeleton.setAnimation(0, 'hongtixing', true);
        }
    }

    // 对手准备完成时调用
    public onOpponentReady(): void {
        this.isanemyReady = true;
        // 停止对手的倒计时动画
        this.stopOpponentTimerAnimation();
    }

    // ==================== jishi动画时间缩放相关方法 ====================

    /**
     * 启动jishi动画并设置正确的timeScale和播放位置
     * @param skeleton 骨骼动画组件
     * @param remainingTime 剩余时间（秒）
     * @param totalTime 总时间（秒）- 摆船阶段30s，战斗阶段20s
     */
    private startJishiAnimationWithTimeScale(skeleton: sp.Skeleton, remainingTime: number, totalTime: number): void {
        if (!skeleton) return;

        // jishi动画原始长度是2秒
        const animationDuration = 2.0;

        // 计算timeScale：让2秒动画播放totalTime秒
        const timeScale = animationDuration / totalTime;

        // 计算动画应该从哪个时间点开始播放
        const elapsedTime = totalTime - remainingTime;
        const animationStartTime = (elapsedTime / totalTime) * animationDuration;

        

        // 设置动画
        skeleton.timeScale = timeScale;
        skeleton.setAnimation(0, 'jishi', true);

        // 设置动画播放位置
        if (animationStartTime > 0) {
            // 如果需要从中间开始播放，使用scheduleOnce在下一帧设置动画时间
            this.scheduleOnce(() => {
                if (skeleton && skeleton.isValid) {
                    // 通过设置动画状态的时间来跳到指定位置
                    const trackEntry = skeleton.getCurrent(0);
                    if (trackEntry) {
                        trackEntry.trackTime = animationStartTime;
                    }
                }
            }, 0);
        }
    }
}



