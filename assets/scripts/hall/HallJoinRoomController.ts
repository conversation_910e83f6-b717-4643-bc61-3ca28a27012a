// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { Config } from "../util/Config";
import { Tools } from "../util/Tools";


const { ccclass, property } = cc._decorator;

@ccclass
export default class HallJoinRoomController extends cc.Component {

    @property(cc.Node)
    errorLabel: cc.Node = null;
    @property(cc.EditBox)
    editBox: cc.EditBox = null   //文本输入框
    @property(cc.Node)
    buttonNoClick: cc.Node = null; //join 按钮（不可点击）
    @property(cc.Node)
    buttonReady: cc.Node = null; //join 按钮（可点击）



    newText: string = ''

    skillsClick: Function = null
    callback: Function;


    // onLoad () {}

    start() {

        Tools.yellowButton(this.buttonReady, () => {
            //点击加入房间
            if (this.callback) {
                this.callback(this.newText);
            }
        })

        Tools.grayButton(this.buttonNoClick, () => { })

    }

    protected onEnable(): void {
        this.destroyEditBox()
        this.editBox.node.on('text-changed', this.onTextChanged, this);
        this.buttonStyle(false);

    }
    protected onDisable(): void {
        this.destroyEditBox()
        this.editBox.string = ''
        this.errorLabel.active = false
    }

    onTextChanged() {
        // 当 EditBox 的文本发生变化时执行这里的代码
        this.newText = this.editBox.string;
        this.errorLabel.active = false
        if (this.newText.length > 0) {
            this.buttonStyle(true);
        } else {
            this.buttonStyle(false);
        }
    }
    destroyEditBox() {
        this.editBox.node.off('text-changed', this.onTextChanged, this);
    }

    private buttonStyle(isEnter: boolean) {
        if (isEnter) {
            this.buttonReady.active = true
            this.buttonNoClick.active = false
        } else {
            this.buttonReady.active = false
            this.buttonNoClick.active = true

        }

    }

    //加入房间失败
    joinError() {
        this.errorLabel.active = true
    }

    //设置点击道具 item 按钮的回调
    setButtonClick(callback: Function) {

        this.callback = callback; //点击加入房间的按钮
    }


    // update (dt) {}
}
