// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { GameMgr } from "../common/GameMgr";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TopUpDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Node)
    cancelBtn: cc.Node = null
    @property(cc.Node)
    confirmBtn: cc.Node = null
    @property(cc.Label)
    tipContent: cc.Label = null

    backCallback: Function = null //隐藏弹窗的回调


    // onLoad () {}

    start() {

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });

        //cancel 按钮点击事件
        Tools.yellowButton(this.cancelBtn, () => {
            this.hide()
        })
        //Confirm 按钮点击事件
        Tools.greenButton(this.confirmBtn, () => {
            GameMgr.H5SDK.ShowAppShop()
            this.hide()
        })
    }


    show(backCallback: Function) {

        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // update (dt) {}
}
