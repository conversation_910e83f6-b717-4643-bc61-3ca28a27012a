// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { Publish } from "../../meshTools/tools/Publish";
import { RoomUser, UserInfo } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { EventType } from "../common/EventCenter";
import { GameMgr } from "../common/GameMgr";
import { AutoMessageBean, AutoMessageId } from "../net/MessageBaseBean";
import MatchItemController from "../pfb/MatchItemController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

@ccclass
export default class MatchParentController extends cc.Component {

    @property(cc.Node)
    backBtn: cc.Node = null;
    @property(cc.Node)
    matchUserLay: cc.Node = null;
    @property(cc.Node)
    matchUserLay2: cc.Node = null;
    @property(cc.Prefab)
    matchItem: cc.Prefab = null;

    backClick: Function

    private _userListCol: MatchItemController[] = []; //显示用户布局的controller

    normalTime = () => { }

    protected onLoad(): void {
        if (cc.sys.os === cc.sys.OS_IOS) {
            // 在iOS设备上执行的代码
            if (Publish.GetInstance().gameMode != '2') {//非半屏的话
                this.backBtn.getComponent(cc.Widget).top = 146
            }
        } else {
            // 在其他设备上执行的代码
        }
    }



    start() {

        //设置返回键的点击事件
        Tools.imageButtonClick(this.backBtn, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {
            if (this.backClick) {
                this.backClick()
            }
        });

    }

    createMatchView() {
        // 重置匹配界面状态
        this.resetMatchState();

        let user_s: RoomUser[] = [];

        //判断是几人游戏
        let peopleNumber: number = GlobalBean.GetInstance().players;
        let userInfo: UserInfo = GlobalBean.GetInstance().loginData.userInfo;
        for (let i = 0; i < peopleNumber; i++) {
            if (i == 0) {

                let user: RoomUser = {
                    userId: userInfo.userId,
                    nickName: userInfo.nickname,
                    avatar: userInfo.avatar,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    score: 0,
                    rank: 0

                }
                user_s.push(user);

            } else {
                user_s.push(null);
            }
        }
        //这里是添加一个等待的页面匹配的占位
        this.matchUserLay.removeAllChildren();
        this.matchUserLay2.removeAllChildren();
        this._userListCol = [];

        if (user_s.length <= 4) {
            for (let i = 0; i < user_s.length; i++) {
                const item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                let matchingItemController = item.getComponent(MatchItemController)
                this._userListCol.push(matchingItemController)
                matchingItemController.setData(user_s[i]);
            }
        } else {
            let arrayData = Tools.chunkArray(user_s, 3)//根据人数进行分块
            for (let i = 0; i < arrayData[0].length; i++) {
                const item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                let matchingItemController = item.getComponent(MatchItemController)
                this._userListCol.push(matchingItemController)
                matchingItemController.setData(user_s[i]);
            }
            for (let i = 0; i < arrayData[1].length; i++) {
                const item = cc.instantiate(this.matchItem);
                this.matchUserLay2.addChild(item);
                let matchingItemController = item.getComponent(MatchItemController)
                this._userListCol.push(matchingItemController)
                matchingItemController.setData(user_s[3+i]);
            }
        }



    }

    //设置游戏数据
    setGameData() {
        // 先取消之前的调度，避免重复调度
        this.unschedule(this.normalTime);

        this.backBtn.active = false //匹配成功之后 隐藏掉返回键
        let user: RoomUser[] = GlobalBean.GetInstance().adjustUserData();

        // 检查用户数据是否有效
        if (!user || user.length === 0) {
            console.warn("setGameData: 用户数据为空，跳过数据设置但继续页面跳转流程");
            // 不要提前返回，继续执行页面跳转逻辑，保持匹配界面当前显示的数据
        } else {
            // 只有在有有效数据时才更新金币和设置真实数据
            const index = user.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索
            if (index !== -1) {
                GlobalBean.GetInstance().loginData.userInfo.coin = user[index].coin //更新自己的最新金币
            }

            //这里是匹配成功之后的真实数据，延迟设置以确保动画完整播放
            this.scheduleOnce(() => {
                for (let i = 0; i < user.length; i++) {
                    if (i < this._userListCol.length) {
                        this._userListCol[i].setData(user[i]);
                    }
                }
            }, 0.2); // 给滚动头像动画一些时间
        }

        // 无论是否有有效数据，都要设置页面跳转逻辑
        this.normalTime = () => {
            let autoMessageBean: AutoMessageBean = {
                'msgId': AutoMessageId.SwitchGameSceneMsg,//匹配成功 通知进入游戏页面
                'data': {}
            }
            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);//进入游戏的消息
        }

        // 按原始动画时间设置延迟
        // 滚动头像动画约0.67秒 + 数据设置延迟0.2秒 = 约0.87秒
        this.scheduleOnce(this.normalTime, 0.87)

    }

    // 重置匹配界面状态
    public resetMatchState(): void {
        // 取消所有调度的任务
        this.unschedule(this.normalTime);

        // 重置normalTime函数为空函数
        this.normalTime = () => { };

        // 清理用户列表控制器数组
        this._userListCol = [];

        // 确保返回按钮可见
        this.backBtn.active = true;

        // 清理子节点
        this.matchUserLay.removeAllChildren();
        this.matchUserLay2.removeAllChildren();
    }

    protected onEnable(): void {
        this.backBtn.active = true;

        // 重置页面中所有按钮到普通状态，解决长按按钮后页面切换导致的按钮卡住问题
        Tools.resetAllButtonsInNode(this.node);

        // 注意：不在onEnable中重置状态，避免干扰正在进行的匹配流程
        // resetMatchState只在createMatchView和onDisable中调用
    }

    protected onDisable(): void {
        this.unschedule(this.normalTime);
        // 重置状态，为下次使用做准备
        this.resetMatchState();
    }

    setClick(backClick: Function) {
        this.backClick = backClick
    }

    // update (dt) {}

    
}
