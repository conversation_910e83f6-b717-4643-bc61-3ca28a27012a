// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus, PairRequest } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { WebSocketToolState } from "../net/WebSocketTool";
import ToastController from "../ToastController";
import { AudioManager } from "../util/AudioManager";
import GlobalManagerController from "../GlobalManagerController";
import { Tools } from "../util/Tools";
import HallParentController from "./HallParentController";
import InfoDialogController from "./InfoDialogController";
import ItemController from "./Item Controller";
import KickOutDialogController from "./KickOutDialogController";
import LeaveDialogController from "./LeaveDialogController";
import MatchParentController from "./MatchParentController";
import SettingDialogController from "./SettingDialogController";

const { ccclass, property } = cc._decorator;


export enum HallOrMatch {
    HALL_PARENT,    //大厅页面
    MATCH_PARENT,   //匹配页面
}

@ccclass
export default class HallPageController extends cc.Component {

    @property(cc.Node)
    hallParentNode: cc.Node = null;
    @property(cc.Node)
    matchParentNode: cc.Node = null;
    @property(InfoDialogController)
    infoDialogController: InfoDialogController = null //道具简介弹窗
    @property(ItemController)
    itemController: ItemController = null //道具介绍页面
    @property(LeaveDialogController)
    leaveDialogController: LeaveDialogController = null // 退出游戏弹窗
    @property(SettingDialogController)
    settingDialogController: SettingDialogController = null //设置弹窗
    @property(KickOutDialogController)
    kickOutDialogController: KickOutDialogController = null //踢出用户的 dialog
    @property(ToastController)
    toastController: ToastController = null  //toast 的布局

    hallOrMatch: HallOrMatch = null

    hallParentController: HallParentController
    matchParentController: MatchParentController

    onLoad() {
        this.hallParentController = this.hallParentNode.getComponent(HallParentController)
        this.matchParentController = this.matchParentNode.getComponent(MatchParentController)

    }

    protected onEnable(): void {
        AudioManager.playBgm()

        // 重置页面中所有按钮到普通状态，解决长按按钮后页面切换导致的按钮卡住问题
        Tools.resetAllButtonsInNode(this.node);

        // 获取GlobalManagerController实例来检查结算重置标志
        const globalMgr = cc.find("Canvas").getComponent("GlobalManagerController");
        const isGameResetFromSettlement = globalMgr && globalMgr.isGameResetFromSettlement;

        // 检查是否有游戏数据或临时备份数据
        const hasGameData = GlobalBean.GetInstance().noticeStartGame || (window as any).tempGameStartData;

        // 只有在没有游戏数据且不是结算重置流程时才强制切换到大厅
        if (!hasGameData && !isGameResetFromSettlement) {
            this.setHallOrMatch(HallOrMatch.HALL_PARENT)
        }

    }

    start() {

        this.hallParentController.setClick(() => {
            //返回键的回调
            this.leaveDialogController.show(0,()=>{})
        }, () => {
            //info 的回调
            this.infoDialogController.show(()=>{})
        }, () => {
            //item 道具介绍的回调
            this.itemController.show(()=>{})
        }, () => {
            //设置键的回调
            this.settingDialogController.show(()=>{})
        }, () => {
            //start 按钮点击
            this.startOrCreate(MessageId.MsgTypePairRequest)

        }, () => {
            //create 点击创建房间
            this.startOrCreate(MessageId.MsgTypeCreateInvite)

        }, (userId: string, nickname: string) => {
            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗
            this.kickOutDialogController.show(userId, nickname)
        })

        this.matchParentController.setClick(() => {
            //匹配页面的返回键的回调
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelPair, {});

        })

    }
    updateGold() {
        this.hallParentController.updateGold()
    }

    //开始匹配 或者创建房间
    startOrCreate(msgId: MessageId) {
        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作
        if (WebSocketManager.GetInstance().webState != WebSocketToolState.Connected || GlobalBean.GetInstance().loginData == null) {
            return;
        }

        //点击 快速开始游戏 start 的回调
        let pairRequest: PairRequest = {
            playerNum: GlobalBean.GetInstance().players,
            fee: GlobalBean.GetInstance().ticketsNum,
            gridNum: 100
        }
        //发送请求开始游戏的消息
        WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);
    }

    //设置是大厅 还是匹配页面
    setHallOrMatch(hallOrMatch: HallOrMatch) {

        if (this.hallOrMatch === hallOrMatch) {
            return
        }
        this.hallOrMatch = hallOrMatch
        this.hallParentNode.active = false
        this.matchParentNode.active = false
        switch (hallOrMatch) {
            case HallOrMatch.HALL_PARENT:
                this.hallParentNode.active = true
                break;
            case HallOrMatch.MATCH_PARENT:
                // 进入匹配界面时，关闭已经打开的设置和游戏介绍页面
                if (this.settingDialogController && this.settingDialogController.node.active) {
                    this.settingDialogController.hide();
                }
                if (this.infoDialogController && this.infoDialogController.node.active) {
                    this.infoDialogController.hide();
                }
                if (this.itemController && this.itemController.node.active) {
                    this.itemController.hide();
                }
                this.matchParentNode.active = true
                break;
        }
    }


    LoginSuccess() {
        //登录成功后 执行的操作
        let loginData = GlobalBean.GetInstance().loginData
        if (loginData) {
            if (loginData.roomId > 0) { //正在游戏中
                // 检查GlobalManagerController是否已经发送了重连请求
                const globalController = GlobalManagerController.getInstance();
                if (globalController && !globalController.isCurrentlyReconnecting()) {
                    // 设置重连标志，防止GlobalManagerController重复发送
                    globalController['isReconnecting'] = true;
                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {
                        lastRoomID: loginData.roomId
                    });//重连进来的 玩家请求进入房间
                }
            }
            if (loginData.inviteCode > 0) {//正在私人房间
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) })
            } else {
                //房间已经解散了  但是我还留在私人房间
                this.hallParentController.exitTheRoom()
            }
            //重连的时候 被遗留在匹配页面的话 就回到大厅
            // 但是要确保不是正在进行的匹配流程（通过检查是否有游戏数据）
            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {
                // 只有在没有游戏数据的情况下才切换回大厅，避免干扰正常的匹配流程
                if (!GlobalBean.GetInstance().noticeStartGame) {
                    this.setHallOrMatch(HallOrMatch.HALL_PARENT)
                }
            }
        }

        // 登录成功后请求用户皮肤信息，确保皮肤状态正确
        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});

        this.setFees()
    }

    //设置接受邀请成功
    setAcceptInvite(acceptInvite: AcceptInvite) {
        this.hallParentController.setAcceptInvite(acceptInvite)
    }
    //离开房间
    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {
        this.hallParentController.leaveRoom(noticeLeaveInvite)
    }


    //设置门票
    setFees() {
        this.hallParentController.setFees()
    }

    //初始化 match 页面
    createMatchView() {
        this.matchParentController.createMatchView()
    }
    //设置匹配数据
    setGameData() {
        this.matchParentController.setGameData()
    }

    //进入私人房间
    joinCreateRoom(){
        this.hallParentController.joinCreateRoom()
    }
    //房间号无效
    joinError() {
        this.hallParentController.joinError()
    }
    //准备 取消准备
    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {
        if (this.hallParentController) {
            this.hallParentController.setReadyState(noticeUserInviteStatus)
        }

    }

    // update (dt) {}
}
