// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import ToastController from "../ToastController";
import HallAutoController from "./HallAutoController";
import HallCreateRoomController from "./HallCreateRoomController";
import HallJoinRoomController from "./HallJoinRoomController";

const { ccclass, property } = cc._decorator;


export enum CenterLaytouType {
    HALL_AUTO_VIEW,
    HALL_CREAT_ROOM_VIEW,
    HALL_JOIN_ROOM_VIEW,
}

@ccclass
export default class HallCenterLayController extends cc.Component {

    @property(cc.Node)
    hallAutoView: cc.Node = null;  //快速开始view
    @property(cc.Node)
    hallCreateRoomView: cc.Node = null; //创建房间 view
    @property(cc.Node)
    hallJoinRoomView: cc.Node = null; //加入房间的 view
    @property(ToastController)
    toastController: ToastController = null  //toast 的布局

    hallAutoController: HallAutoController = null
    hallCreateRoomController: HallCreateRoomController = null
    hallJoinRoomController: HallJoinRoomController = null

    centerLaytouType: CenterLaytouType = null //当前展示的哪一个 view
    startClick: Function
    createClick: Function
    seatCallback: Function

    onLoad() {
        this.hallAutoController = this.hallAutoView.getComponent(HallAutoController)
        this.hallCreateRoomController = this.hallCreateRoomView.getComponent(HallCreateRoomController)
        this.hallJoinRoomController = this.hallJoinRoomView.getComponent(HallJoinRoomController)
    
    }
    protected onEnable(): void {
        this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
    }

    start() {
        this.hallAutoController.setButtonClick(() => {
            //start 按钮的点击回调
            if (this.startClick) {
                this.startClick()
            }
        }, () => {
            //create 按钮的点击回调
            if (this.createClick) {
                this.createClick()
            }

        }, () => {
            //join 按钮的点击回调
            this.setCenterLaytouType(CenterLaytouType.HALL_JOIN_ROOM_VIEW)
        })


        this.hallCreateRoomController.setClick((userId: string, nickname: string) => {
            //房间内点击玩家头像
            if (this.seatCallback) {
                this.seatCallback(userId, nickname)
            }
        }, () => {
            //点击 start 的回调

            //发送开始游戏的消息
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteStart, {});
        }, () => {
            //ready的回调
            //发送游戏准备的消息
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteReady, { 'ready': true });
        }, () => {
            //cancel 的回调
            //发送取消游戏准备的消息
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteReady, { 'ready': false });
        },)

        this.hallJoinRoomController.setButtonClick((inviteCode: string) => {
            //加入房间的按钮
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(inviteCode)})
        })
    }

    //设置显示哪一个 view
    setCenterLaytouType(centerLaytouType: CenterLaytouType) {
        if (this.centerLaytouType === centerLaytouType) {
            return
        }
        this.centerLaytouType = centerLaytouType
        this.hallAutoView.active = false
        this.hallCreateRoomView.active = false
        this.hallJoinRoomView.active = false

        switch (centerLaytouType) {
            case CenterLaytouType.HALL_AUTO_VIEW:
                this.hallAutoView.active = true
                break
            case CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                this.hallCreateRoomView.active = true
                break
            case CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                this.hallJoinRoomView.active = true
                break

        }
    }

    //获取当前正在显示的那个页面
    getCenterLaytouType() {
        return this.centerLaytouType
    }

    //设置按钮的点击回调
    setClick(startClick: Function, createClick: Function, seatCallback: Function) {
        this.startClick = startClick
        this.createClick = createClick
        this.seatCallback = seatCallback
    }

    //设置接受邀请成功
    setAcceptInvite(acceptInvite: AcceptInvite) {

        //如果正在，接受邀请页面的话
        if (this.centerLaytouType == CenterLaytouType.HALL_JOIN_ROOM_VIEW) {
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)
        } else if (this.centerLaytouType == CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            //如果已经在房间页面，就刷新数据
            this.hallCreateRoomController.refreshPlayer(acceptInvite.inviteInfo)
        } else {
            //还在大厅收到重链接的消息
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)
        }
    }
    //离开房间
    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {
        //判断是不是自己离开了房间，或者是房主离开（房主离开就是解散房间了）
        if (noticeLeaveInvite.userId === GlobalBean.GetInstance().loginData.userInfo.userId || noticeLeaveInvite.isCreator) {

            if (noticeLeaveInvite.userId != GlobalBean.GetInstance().loginData.userInfo.userId) {
                this.toastController.showContent(window.getLocalizedStr('LeaveRoom'))
            }
            //当前展示的是加入房间 或者创建房间的话  返回键返回大厅
            this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
        } else {
            //刷新数据
            this.hallCreateRoomController.leavePlayer(noticeLeaveInvite)
        }
    }

    //设置门票
    setFees() {
        this.hallAutoController.setFees()
    }

    //进入私人房间
    joinCreateRoom() {
        this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)
    }
    joinError() {
        this.hallJoinRoomController.joinError()
    }
    //准备 取消准备
    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {
        this.hallCreateRoomController.setReadyState(noticeUserInviteStatus)
    }

    // update (dt) {}
}
