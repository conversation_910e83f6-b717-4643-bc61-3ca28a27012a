// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html


import InfoItemController from "../pfb/InfoItemController";
import InfoItemOneController from "../pfb/InfoItemOneController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;


//游戏规则页面
@ccclass
export default class InfoDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Prefab)
    infoItem: cc.Prefab = null;
    @property(cc.Prefab)
    infoItem1: cc.Prefab = null;

    // 图片预制体
    @property(cc.Prefab)
    hengxianPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    bg1Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    bg2Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    bg3Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    bg4Prefab: cc.Prefab = null;



    // 游戏规则相关属性
    titleList: string[] = [] // 游戏规则标题列表
    gameIntroductionList: string[] = [] // 游戏介绍
    chessboardPatternList: string[] = [] // 棋盘模式规则
    battleshipLayoutList: string[] = [] // 战舰布局规则
    gameStartList: string[] = [] // 游戏开始规则
    battleshipAreaList: string[] = [] // 战舰区域
    hitTargetList: string[] = [] // 命中目标
    winGameList: string[] = [] // 获胜条件

    backCallback: Function = null //隐藏弹窗的回调

    onLoad() {

    }

    /**
     * 初始化游戏规则多语言文本
     */
    private initializeGameRulesTexts() {
        // 游戏规则标题列表
        this.titleList = [
            window.getLocalizedStr('GameRules_GameIntroduction'),
            window.getLocalizedStr('GameRules_ChessboardPattern'),
            window.getLocalizedStr('GameRules_BattleshipLayout'),
            window.getLocalizedStr('GameRules_GameStart'),
            window.getLocalizedStr('GameRules_BattleshipArea'),
            window.getLocalizedStr('GameRules_HitTarget'),
            window.getLocalizedStr('GameRules_WinGame'),
        ];

        // 游戏介绍
        this.gameIntroductionList = [
            window.getLocalizedStr('GameRules_IntroText'),
        ];

        // 棋盘模式规则
        this.chessboardPatternList = [
            window.getLocalizedStr('GameRules_ChessboardSize'),
            window.getLocalizedStr('GameRules_WarshipSpecs'),
        ];

        // 战舰布局规则
        this.battleshipLayoutList = [
            window.getLocalizedStr('GameRules_LayoutTime'),
            window.getLocalizedStr('GameRules_RotateShip'),
        ];

        // 游戏开始规则
        this.gameStartList = [
            window.getLocalizedStr('GameRules_RandomFirstPlayer'),
        ];

        // 战舰区域
        this.battleshipAreaList = [
            window.getLocalizedStr('GameRules_BattleshipAreaText'),
        ];

        // 命中目标
        this.hitTargetList = [
            window.getLocalizedStr('GameRules_HitTargetText'),
            window.getLocalizedStr('GameRules_SmallGridText'),
        ];

        // 获胜条件
        this.winGameList = [
            window.getLocalizedStr('GameRules_WinCondition'),
            window.getLocalizedStr('GameRules_FirstToWin'),
        ];
    }

    start() {

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });
    }

    /**
     * 根据预制体名称获取对应的预制体
     */
    private getPrefabByName(prefabName: string): cc.Prefab {
        switch (prefabName) {
            case 'hengxian':
                return this.hengxianPrefab;
            case 'bg1':
                return this.bg1Prefab;
            case 'bg2':
                return this.bg2Prefab;
            case 'bg3':
                return this.bg3Prefab;
            case 'bg4':
                return this.bg4Prefab;
            default:
                return null;
        }
    }

    /**
     * 添加图片节点
     */
    private addImageNode(prefabName: string) {
        const prefab = this.getPrefabByName(prefabName);
        if (prefab) {
            const imageNode = cc.instantiate(prefab);

            // 如果是横线图片，需要添加上下间距
            if (prefabName === 'hengxian') {
                // 添加上间距
                this.addSpacerNode(25);

                // 添加图片
                this.contentLay.addChild(imageNode);

                // 添加下间距
                this.addSpacerNode(25);
            } else {
                // 其他图片直接添加
                this.contentLay.addChild(imageNode);
            }
        }
    }

    /**
     * 添加间距节点
     */
    private addSpacerNode(height: number) {
        const spacerNode = new cc.Node('spacer');
        spacerNode.addComponent(cc.Widget);
        spacerNode.width = this.contentLay.width;
        spacerNode.height = height;
        this.contentLay.addChild(spacerNode);
    }

    /**
     * 构建游戏规则内容
     */
    private buildGameRulesContent() {
        // 1. 游戏介绍
        this.getTitleNode(this.titleList[0]);
        this.addContentList(this.gameIntroductionList);
        this.addImageNode('hengxian');

        // 2. 棋盘模式
        this.getTitleNode(this.titleList[1]);
        this.addContentList(this.chessboardPatternList);
        this.addImageNode('bg1');
        this.addImageNode('hengxian');

        // 3. 战舰布局
        this.getTitleNode(this.titleList[2]);
        this.addContentList(this.battleshipLayoutList);
        this.addImageNode('bg2');
        this.addImageNode('hengxian');

        // 4. 游戏开始
        this.getTitleNode(this.titleList[3]);
        this.addContentList(this.gameStartList);
        this.addImageNode('hengxian');

        // 5. 战舰区域
        this.getTitleNode(this.titleList[4]);
        this.addContentList(this.battleshipAreaList);
        this.addImageNode('hengxian');

        // 6. 命中目标
        this.getTitleNode(this.titleList[5]);
        this.addContentList([this.hitTargetList[0]]);
        this.addImageNode('bg3');
        this.addContentList([this.hitTargetList[1]]);
        this.addImageNode('bg4');
        this.addImageNode('hengxian');

        // 7. 获胜条件
        this.getTitleNode(this.titleList[6]);
        this.addContentList(this.winGameList);
    }

    /**
     * 添加内容列表（只有文字，没有图片）
     */
    private addContentList(contentList: string[]) {
        contentList.forEach((content) => {
            let infoItem = cc.instantiate(this.infoItem1);
            let infoItemOneController = infoItem.getComponent(InfoItemOneController);
            infoItemOneController.setData(content);
            this.contentLay.addChild(infoItem);
        });
    }


    getTitleNode(title: string) {
        let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        let infoItemController = infoItem.getComponent(InfoItemController)
        infoItemController.setContent(title)
        this.contentLay.addChild(infoItem)
    }

    show(backCallback: Function) {
        this.backCallback = backCallback

        // 清空内容并重新构建（确保多语言适配）
        this.contentLay.removeAllChildren()

        // 初始化游戏规则多语言文本（确保获取到最新的语言设置）
        this.initializeGameRulesTexts();

        // 构建游戏规则内容
        this.buildGameRulesContent();

        this.node.active = true
        this.boardBg.scale = 0
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // update (dt) {}
}
