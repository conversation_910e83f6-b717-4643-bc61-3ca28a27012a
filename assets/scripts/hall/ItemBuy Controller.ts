// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { EventType } from "../common/EventCenter";
import { GameMgr } from "../common/GameMgr";
import SkinManager from "../common/SkinManager";
import ShipController from "../common/ShipController";
import { GlobalBean } from "../bean/GlobalBean";

import { ReceivedMessageBean } from "../net/MessageBaseBean";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";
import GlobalManagerController from "../GlobalManagerController";

const { ccclass, property } = cc._decorator;

// 皮肤数据接口
interface SkinData {
    id: number;          // 商品ID (2100-2102)
    type: number;        // 商品类型 (1=战舰皮肤)
    price: number;       // 商品价格 (金币)
    isHave: boolean;     // 玩家是否已拥有该商品
}

@ccclass
export default class ItemBuyController extends cc.Component {
    [x: string]: any  ;

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    cancelBtn: cc.Node = null
    @property(cc.Node)
    confirmBtn: cc.Node = null
    @property(cc.Node)
    priceNode2101: cc.Node = null // 皮肤2101的价格显示节点
    @property(cc.Node)
    priceNode2102: cc.Node = null // 皮肤2102的价格显示节点
    @property(cc.Label)
    spendLabel: cc.Label = null // 购买提示文字标签
    @property(cc.Node)
    beanIcon: cc.Node = null // 金豆图标节点

    // 选择节点
    @property(cc.Node)
    choose001: cc.Node = null // 第一个皮肤的选择节点
    @property(cc.Node)
    choose002: cc.Node = null // 第二个皮肤的选择节点
    @property(cc.Node)
    choose003: cc.Node = null // 第三个皮肤的选择节点

    backCallback: Function = null //隐藏弹窗的回调
    currentSkinData: SkinData = null // 当前显示的皮肤数据
    private isProcessingPurchase: boolean = false // 是否正在处理购买请求
    private itemController: any = null // Item Controller 的引用，用于更新选择节点

    onLoad() {
        // 监听网络消息
        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);
    }

    onDestroy() {
        // 移除网络消息监听
        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);
    }

    start() {
        // 设置按钮事件
        this.setupButtonEvents();

        // 初始化所有皮肤的价格节点显示状态
        this.initializeAllPriceNodes();

        // 初始化所有选择节点状态
        this.initializeAllChooseNodes();

        // 初始化金豆图标位置（基于默认价格300）
        this.adjustBeanIconPosition(300);
    }

    // 设置按钮事件
    private setupButtonEvents() {
        // 关闭按钮
        if (this.boardBtnClose) {
            Tools.imageButtonClick(this.boardBtnClose,
                Config.buttonRes + 'board_btn_close_normal',
                Config.buttonRes + 'board_btn_close_pressed',
                () => {
                    this.hide();
                });
        }

        // 取消按钮
        if (this.cancelBtn) {
            Tools.imageButtonClick(this.cancelBtn,
                Config.buttonRes + 'btn_yellow_normal',
                Config.buttonRes + 'btn_yellow_pressed',
                () => {
                    this.hide();
                });
        }

        // 确认按钮
        if (this.confirmBtn) {
            Tools.imageButtonClick(this.confirmBtn,
                Config.buttonRes + 'btn_green_normal',
                Config.buttonRes + 'btn_green_pressed',
                () => {
                    this.onConfirmClick();
                });
        }
    }

    // 显示皮肤购买界面（供 Item Controller 调用）
    showSkinPurchase(skinData: SkinData, backCallback: Function, itemController?: any) {
        this.currentSkinData = skinData;
        this.backCallback = backCallback;
        this.itemController = itemController; // 保存 Item Controller 的引用

        // 初始化皮肤数据
        this.initializeSkinData();

        // 更新界面显示
        this.updateDisplay();

        // 显示界面
        this.show(backCallback);
    }

    // 初始化皮肤数据
    private initializeSkinData() {
        if (!this.currentSkinData) {
            return;
        }

        // 设置皮肤的默认价格（如果价格未设置）
        if (this.currentSkinData.price === undefined || this.currentSkinData.price === null) {
            switch (this.currentSkinData.id) {
                case 2100:
                    // 默认皮肤，免费且已拥有
                    this.currentSkinData.price = 0;
                    break;
                case 2101:
                case 2102:
                    // 高级皮肤，300金币
                    this.currentSkinData.price = 300;
                    break;
                default:
                    // 未知皮肤，默认300金币
                    this.currentSkinData.price = 300;
                    console.warn(`未知的皮肤ID: ${this.currentSkinData.id}，使用默认价格300`);
                    break;
            }
        }

        // 设置拥有状态（如果未设置）
        if (this.currentSkinData.isHave === undefined) {
            switch (this.currentSkinData.id) {
                case 2100:
                    // 默认皮肤，已拥有
                    this.currentSkinData.isHave = true;
                    break;
                case 2101:
                case 2102:
                default:
                    // 其他皮肤，默认未拥有
                    this.currentSkinData.isHave = false;
                    break;
            }
        }

       
    }

    // 更新界面显示
    private updateDisplay() {
        if (!this.currentSkinData) {
            return;
        }

        // 根据是否拥有皮肤来控制价格显示和按钮文字
        if (this.currentSkinData.isHave) {
            // 已拥有：隐藏价格，按钮显示"选择"
            this.hidePriceNode();
            this.updateConfirmButtonText("选择");
        } else {
            // 未拥有：显示价格，按钮显示"购买"
            this.showPriceNode();
            this.updateConfirmButtonText("购买");
            this.updateSpendLabelText("购买", this.currentSkinData.price);
        }
    }

    // 隐藏指定皮肤的价格节点
    private hidePriceNode() {
        if (!this.currentSkinData) {
            return;
        }

        const priceNode = this.getPriceNodeBySkinId(this.currentSkinData.id);
        if (priceNode) {
            priceNode.active = false;
           
        }
    }

    // 显示指定皮肤的价格节点
    private showPriceNode() {
        if (!this.currentSkinData) {
            return;
        }

        const priceNode = this.getPriceNodeBySkinId(this.currentSkinData.id);
        if (priceNode) {
            priceNode.active = true;
           
        }
    }

    // 根据皮肤ID获取对应的价格节点
    private getPriceNodeBySkinId(skinId: number): cc.Node | null {
        switch (skinId) {
            case 2101:
                return this.priceNode2101;
            case 2102:
                return this.priceNode2102;
            case 2100:
                // 默认皮肤不需要价格节点
                return null;
            default:
                console.warn(`未知的皮肤ID: ${skinId}`);
                return null;
        }
    }

    // 根据皮肤ID获取对应的选择节点
    private getChooseNodeBySkinId(skinId: number): cc.Node | null {
        switch (skinId) {
            case 2100:
                return this.choose001; // 第一个皮肤
            case 2101:
                return this.choose002; // 第二个皮肤
            case 2102:
                return this.choose003; // 第三个皮肤
            default:
                console.warn(`未知的皮肤ID: ${skinId}`);
                return null;
        }
    }

    // 显示指定皮肤的选择节点，隐藏其他选择节点
    private showChooseNode(skinId: number) {
      

        // 隐藏所有选择节点
        this.hideAllChooseNodes();

        // 显示指定皮肤的选择节点
        const chooseNode = this.getChooseNodeBySkinId(skinId);
        if (chooseNode) {
            chooseNode.active = true;
          
        } else {
            console.warn(`未找到皮肤${skinId}的选择节点`);
        }
    }

    // 隐藏所有选择节点
    private hideAllChooseNodes() {
        if (this.choose001) {
            this.choose001.active = false;
        }
        if (this.choose002) {
            this.choose002.active = false;
        }
        if (this.choose003) {
            this.choose003.active = false;
        }
     
    }

    // 更新确认按钮文字
    private updateConfirmButtonText(text: string) {
        if (this.confirmBtn) {
            const labelNode = this.confirmBtn.getChildByName('button_label');
            if (labelNode) {
                const label = labelNode.getComponent(cc.Label);
                if (label) {
                    label.string = text;
                }
            }
        }
    }

    // 更新购买提示文字
    private updateSpendLabelText(action: string, price?: number) {
        if (!this.spendLabel) {
            return;
        }

        let finalPrice = 300; // 默认价格
        // 使用自适应的金豆图标位置方案
        this.setupAdaptiveBeanIconPosition(action, price);

        // 如果是购买操作，更新最终价格
        if (action === "购买" && price !== undefined) {
            finalPrice = price;
        }

       
    }

    // 自适应设置金豆图标和文字位置
    private setupAdaptiveBeanIconPosition(action: string, price?: number) {
        if (!this.spendLabel || !this.beanIcon) {
            return;
        }

        let finalPrice = 300; // 默认价格
        if (action === "购买" && price !== undefined) {
            finalPrice = price;
        }

        // 获取本地化文字模板
        let textTemplate = window.getLocalizedStr("morenbuy");

        // 简单方案：直接替换数字并在前面加空格
        const priceStr = finalPrice.toString();
        let finalText = textTemplate.replace("300", "    " + priceStr); // 4个空格为金豆图标留位置

        // 设置文字
        this.spendLabel.string = finalText;

        // 延迟计算金豆图标位置
        this.scheduleOnce(() => {
            this.positionBeanIconBeforeNumber(textTemplate, priceStr);
        }, 0.1);
    }

    // 根据语言设置金豆图标的精确位置
    private positionBeanIconBeforeNumber(originalText: string, priceStr: string) {
        if (!this.spendLabel || !this.beanIcon) {
            return;
        }

        // 根据当前语言设置金豆图标的精确位置
        const currentLang = window.languageName;

        switch (currentLang) {
            case "zh_CN":
            case "zh_HK":
                // 中文和繁体中文
                this.beanIcon.x = -4;
                this.beanIcon.y = 26;
                break;
            case "en":
                // 英文
                this.beanIcon.x = 28;
                this.beanIcon.y = 26;
                break;
            default:
                // 默认使用中文位置
                this.beanIcon.x = -4;
                this.beanIcon.y = 26;
                break;
        }


    }

    // 保留原方法用于兼容性
    private adjustBeanIconPosition(price: number) {
        // 现在由setupAdaptiveBeanIconPosition处理
    }

    // 公共方法：根据皮肤数据更新购买提示文字
    public updateSpendLabelForSkin(skinData: any) {
        if (!skinData) {
            return;
        }

        if (!skinData.isHave) {
            this.updateSpendLabelText("购买", skinData.price);
        }
    }

    // 确认按钮点击处理
    private onConfirmClick() {
        if (!this.currentSkinData) {
            console.error("没有当前皮肤数据");
            return;
        }

        if (this.currentSkinData.isHave) {
            // 已拥有皮肤，直接更换皮肤（不再触发购买界面）
           
            this.setSkinAsCurrent();
        } else {
            // 未拥有皮肤，发起购买请求
            this.buyCurrentSkin();
        }
    }

    // 设置皮肤为当前使用的皮肤
    private setSkinAsCurrent() {
        if (!this.currentSkinData) {
            return;
        }

      

        // 应用皮肤（不关闭界面）
        this.applySkinWithoutClosing();

        // 关闭购买界面
        this.hide();
    }

    // 应用皮肤但不关闭界面（用于购买成功后自动应用）
    private applySkinWithoutClosing() {
        if (!this.currentSkinData) {
            return;
        }

     

        // 立即更新本地皮肤状态（确保即时生效）
        const skinManager = SkinManager.getInstance();
        skinManager.setCurrentSkin(this.currentSkinData.id);

        // 立即刷新所有船只的皮肤
        ShipController.refreshAllShips();

        // 发送设置皮肤请求到后端（用于同步到服务器和其他玩家）
        const setSkinRequest = {
            id: this.currentSkinData.id
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.SetSkin, setSkinRequest);
    }

    // 购买当前皮肤
    private buyCurrentSkin() {
        if (this.isProcessingPurchase) {
            return;
        }

        if (!this.currentSkinData) {
            return;
        }

        this.isProcessingPurchase = true;

        // 发送购买请求到后端 - 严格按照api.md格式
        const buyRequest = {
            id: this.currentSkinData.id  // 要购买的商品ID (2100-2102)
        };

      

        // 检查 WebSocket 连接状态
        const wsManager = WebSocketManager.GetInstance();
        if (!wsManager || !this.isWebSocketConnected()) {
            console.error("ItemBuyController: WebSocket 未连接");
            this.isProcessingPurchase = false;

            // 提供重连选项
            if (confirm("网络连接已断开，是否重新连接？")) {
                this.attemptReconnection();
            }
            return;
        }

        try {
            wsManager.sendMsg(MessageId.BuyProduct, buyRequest);
           
        } catch (error) {
            console.error("ItemBuyController: 发送购买请求失败", error);
            this.isProcessingPurchase = false;
            alert("发送请求失败，请检查网络连接后重试");
            return;
        }

        // 设置购买超时处理
        this.scheduleOnce(() => {
            if (this.isProcessingPurchase) {
                this.isProcessingPurchase = false;
              
                alert("购买请求超时，请检查网络连接");
            }
        }, 10); // 10秒超时
    }

    // 检查 WebSocket 连接状态
    private isWebSocketConnected(): boolean {
        try {
            // 简单的连接检查 - 如果能创建 WebSocketManager 实例说明连接正常
            const wsManager = WebSocketManager.GetInstance();
            if (!wsManager) {
               
                return false;
            }

            // 这里可以添加更详细的连接状态检查
            // 暂时返回 true，依赖发送时的错误处理
            return true;
        } catch (error) {
            console.error("ItemBuyController: 检查 WebSocket 状态失败", error);
            return false;
        }
    }

    // 尝试重新连接
    private attemptReconnection(): void {
       

        try {
            // 这里可以调用重连逻辑
            // 暂时提示用户刷新页面
            if (confirm("请刷新页面重新连接，是否立即刷新？")) {
                window.location.reload();
            }
        } catch (error) {
            console.error("ItemBuyController: 重连失败", error);
            alert("重连失败，请手动刷新页面");
        }
    }

    // 网络消息处理
    private onReceiveMessage(receivedMessageBean: ReceivedMessageBean) {
        switch (receivedMessageBean.msgId) {
            case MessageId.BuyProduct:
                this.onBuyProductResponse(receivedMessageBean.data);
                break;
            case MessageId.SetSkin:
                this.onSetSkinResponse(receivedMessageBean.data);
                break;
            case MessageId.GetUserSkin:
                this.onGetUserSkinResponse(receivedMessageBean.data);
                break;
            case MessageId.ProductConfigs:
                this.onProductConfigsResponse(receivedMessageBean.data);
                break;
            case MessageId.NoticeUserCoin:
                this.onUserCoinChange(receivedMessageBean.data);
                break;
        }
    }

    // 获取用户皮肤信息响应处理
    public onGetUserSkinResponse(data: any): void {
        if (!data) {
            return;
        }

        // 通知 Item Controller 处理皮肤信息（避免重复调用）
        if (this.itemController && this.itemController.onGetUserSkinResponse && !data._fromItemController) {
            this.itemController.onGetUserSkinResponse(data);
        }

        // 立即更新所有价格节点状态
        this.updateAllPriceNodesBasedOnOwnership();

        // 延迟再次更新，确保状态正确
        this.scheduleOnce(() => {
            this.updateAllPriceNodesBasedOnOwnership();
        }, 0.1);
    }

    // 购买商品响应处理
    private onBuyProductResponse(data: any) {
       
        this.isProcessingPurchase = false;

        if (data === null || data === undefined) {
            console.error("ItemBuyController: 购买响应数据为空");
            alert("购买失败：服务器响应异常");
            return;
        }

        // 检查是否有错误信息
        if (data.code && data.code !== 0) {
            console.error("ItemBuyController: 购买失败，错误码:", data.code, "错误信息:", data.msg);

            // 检查是否是"皮肤已购买"的情况
            if (data.msg && (data.msg.includes("已购买") || data.msg.includes("already owned") || data.msg.includes("already purchased"))) {
               
                this.handleAlreadyOwnedSkin(this.currentSkinData?.id);
                return;
            }

            alert(`购买失败：${data.msg || "未知错误"}`);
            return;
        }

        // 根据api.md，BuyProduct响应参数只有id字段
        // 如果返回了id，说明购买成功
        if (data.id && data.id === this.currentSkinData?.id) {
            // 购买成功
            // 更新当前皮肤数据
            if (this.currentSkinData) {
                this.currentSkinData.isHave = true;
            }

            // 添加到 SkinManager 的拥有列表
            // 临时注释掉，避免编译缓存问题
            // const skinManager = SkinManager.getInstance();
            // const currentOwnedSkins = skinManager.getOwnedSkins();
            // if (!currentOwnedSkins.includes(data.id)) {
            //     currentOwnedSkins.push(data.id);
            //     skinManager.setOwnedSkins(currentOwnedSkins);
            // }
         

            // 隐藏对应皮肤的价格节点
            this.hideSkinPrice(data.id);

            // 更新确认按钮文字为"选择"
            this.updateConfirmButtonText("选择");

            // 通知 Item Controller 更新皮肤拥有状态
            if (this.itemController && this.itemController.updateSkinOwnership) {
                this.itemController.updateSkinOwnership(data.id, true);
            }

            // 购买成功后自动应用皮肤并关闭购买页面
            this.scheduleOnce(() => {
                // 自动应用刚购买的皮肤
                this.applySkinWithoutClosing();

                // 关闭购买界面
                this.hide();

                // 通知ItemController更新choose节点显示
                if (this.itemController && this.itemController.showChooseNode) {
                    this.itemController.showChooseNode(data.id);
                }
            }, 0.5);

        } else {
            // 购买失败
            alert("购买失败，请检查金币余额或稍后重试");
        }
    }

    // 设置皮肤响应处理
    private onSetSkinResponse(data: any) {
        // 根据api.md，SetSkin响应参数包含：userId, type, id
        if (data.userId && data.type === 1 && data.id) {
            const myUserId = GlobalBean.GetInstance().loginData.userInfo.userId;

            // 检查是否是自己的皮肤设置
            if (data.userId === myUserId) {
               

                // 更新SkinManager的当前皮肤
                const skinManager = SkinManager.getInstance();
                skinManager.setCurrentSkin(data.id);

                // 刷新所有船只的皮肤
                ShipController.refreshAllShips();

                // 显示对应的选择节点，隐藏其他选择节点（在购买页面）
                this.showChooseNode(data.id);

                // 同时更新 Item Controller 的选择节点
                if (this.itemController && this.itemController.showChooseNode) {
                    this.itemController.showChooseNode(data.id);
                }
            }
        }
    }

    // 商品列表响应处理
    private onProductConfigsResponse(data: any) {
        // 根据api.md，ProductConfigs响应参数包含：productList数组
        if (data.productList && Array.isArray(data.productList)) {
            // 更新皮肤拥有状态
            this.updateSkinOwnershipStatus(data.productList);
        }
    }

    // 更新皮肤拥有状态
    private updateSkinOwnershipStatus(productList: any[]) {
        // 遍历商品列表，更新皮肤拥有状态
        productList.forEach(product => {
            if (product.type === 1) { // 1=战舰皮肤
                // 更新对应皮肤的价格节点显示状态
                this.updatePriceNodeBySkinData(product);

                // 如果当前显示的皮肤状态发生变化，更新UI
                if (this.currentSkinData && this.currentSkinData.id === product.id) {
                    const wasOwned = this.currentSkinData.isHave;
                    this.currentSkinData.isHave = product.isHave;
                    this.currentSkinData.price = product.price;

                    // 如果拥有状态发生变化，更新显示
                    if (wasOwned !== product.isHave) {
                        this.updateDisplay();
                    }
                }
            }
        });
    }

    // 用户金币变化通知处理
    private onUserCoinChange(data: any) {
        // 根据api.md，NoticeUserCoin响应参数包含：coin字段（Number类型）
        // 直接使用服务器返回的最终金币数量
        if (data.coin !== undefined && data.coin !== null && typeof data.coin === 'number') {
            // 更新全局账户余额为服务器返回的最终值
            GlobalBean.GetInstance().loginData.userInfo.coin = data.coin;

            // 通知大厅页面更新金币显示
            this.updateHallGoldDisplay();
        }
    }

    // 更新大厅页面的金币显示
    private updateHallGoldDisplay() {
        try {
            // 查找HallPageController并更新金币显示
            const canvasNode = cc.find("Canvas");
            if (canvasNode) {
                const hallPageController = canvasNode.getComponent("HallPageController");
                if (hallPageController && hallPageController.updateGold) {
                    hallPageController.updateGold();
                   
                }
            }
        } catch (error) {
            console.warn("ItemBuyController: 更新大厅金币显示失败", error);
        }
    }

    // 隐藏指定皮肤的价格显示节点
    public hideSkinPrice(skinId?: number) {
        const targetSkinId = skinId || this.currentSkinData?.id;
        if (!targetSkinId) {
            return;
        }

        const priceNode = this.getPriceNodeBySkinId(targetSkinId);
        if (priceNode) {
            priceNode.active = false;
        }
    }

    // 显示指定皮肤的价格显示节点
    private showSkinPrice(skinId?: number) {
        const targetSkinId = skinId || this.currentSkinData?.id;
        if (!targetSkinId) {
            return;
        }

        const priceNode = this.getPriceNodeBySkinId(targetSkinId);
        if (priceNode) {
            priceNode.active = true;
        }
    }

    // 初始化所有皮肤的价格节点显示状态
    private initializeAllPriceNodes() {
        // 根据皮肤拥有状态来设置价格节点显示
        this.updateAllPriceNodesBasedOnOwnership();
    }

    // 根据皮肤拥有状态更新所有价格节点（公共方法，供外部调用）
    public updateAllPriceNodesBasedOnOwnership() {
        try {
            // 优先使用ItemController的皮肤拥有状态缓存
            if (this.itemController) {
                const hasSkin2101 = this.itemController.getSkinOwnership(2101);
                const hasSkin2102 = this.itemController.getSkinOwnership(2102);

                // 皮肤2101的价格节点
                if (this.priceNode2101) {
                    this.priceNode2101.active = !hasSkin2101;
                }

                // 皮肤2102的价格节点
                if (this.priceNode2102) {
                    this.priceNode2102.active = !hasSkin2102;
                }

                return;
            }

            // 备用方案：尝试从全局临时数据获取
            const tempSkinData = (window as any).tempUserSkinData;
            if (tempSkinData && tempSkinData.ownedSkins && Array.isArray(tempSkinData.ownedSkins)) {
                const ownedSkins = tempSkinData.ownedSkins;

                // 皮肤2101的价格节点
                if (this.priceNode2101) {
                    this.priceNode2101.active = !ownedSkins.includes(2101);
                }

                // 皮肤2102的价格节点
                if (this.priceNode2102) {
                    this.priceNode2102.active = !ownedSkins.includes(2102);
                }

                return;
            }

            // 默认显示所有价格节点
            if (this.priceNode2101) {
                this.priceNode2101.active = true;
            }
            if (this.priceNode2102) {
                this.priceNode2102.active = true;
            }

        } catch (error) {
            console.error("ItemBuyController: 更新价格节点失败", error);

            // 出错时显示所有价格节点
            if (this.priceNode2101) {
                this.priceNode2101.active = true;
            }
            if (this.priceNode2102) {
                this.priceNode2102.active = true;
            }
        }
    }

    // 初始化所有选择节点状态
    private initializeAllChooseNodes() {
        // 默认隐藏所有选择节点
        this.hideAllChooseNodes();

        // 默认显示第一个皮肤的选择节点（皮肤2100）
        if (this.choose001) {
            this.choose001.active = true;
        }
    }

    // 根据皮肤拥有状态更新对应的价格节点
    private updatePriceNodeBySkinData(skinData: SkinData) {
        if (!skinData) return;

        if (skinData.isHave) {
            // 已拥有，隐藏价格节点
            this.hideSkinPrice(skinData.id);
        } else {
            // 未拥有，显示价格节点
            this.showSkinPrice(skinData.id);
        }
    }

    show(backCallback: Function) {

        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0

        // 每次显示时更新所有价格节点状态
        this.updateAllPriceNodesBasedOnOwnership();

        // 延迟再次更新，确保所有数据都已加载
        this.scheduleOnce(() => {
            this.updateAllPriceNodesBasedOnOwnership();
        }, 0.2);

        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // 显示皮肤更换不允许的提示
    private showSkinChangeNotAllowedTip(): void {
        console.warn("提示：只能在游戏开始到战斗开始之间更换皮肤！");
    }

    // 强制刷新价格节点状态（公共方法，供外部调用）
    public forceRefreshPriceNodes(): void {
        // 从SkinManager直接获取拥有的皮肤列表
        try {
            const skinManager = SkinManager.getInstance();
            const ownedSkins = skinManager.getOwnedSkins();

            // 皮肤2101的价格节点
            if (this.priceNode2101) {
                const hasSkin2101 = ownedSkins.includes(2101);
                const shouldHide = hasSkin2101;

                if (shouldHide) {
                    // 使用强制隐藏方法
                    this.forceHidePriceNode(this.priceNode2101);
                } else {
                    // 使用恢复显示方法
                    this.restorePriceNode(this.priceNode2101);
                }
            }

            // 皮肤2102的价格节点
            if (this.priceNode2102) {
                const hasSkin2102 = ownedSkins.includes(2102);
                const shouldHide = hasSkin2102;

                if (shouldHide) {
                    // 使用强制隐藏方法
                    this.forceHidePriceNode(this.priceNode2102);
                } else {
                    // 使用恢复显示方法
                    this.restorePriceNode(this.priceNode2102);
                }
            }
        } catch (error) {
            console.error("ItemBuyController: 强制刷新价格节点失败", error);
        }
    }

    // 调试方法：检查节点层级和状态
    public debugNodeHierarchy(): void {
       

        const checkNode = (node: cc.Node, name: string) => {
            if (node) {
                // 检查所有父节点的active状态
                let parent = node.parent;
                let level = 1;
                while (parent && level < 5) {
                    parent = parent.parent;
                    level++;
                }
            } else {
                console.log(`${name}: 节点为空`);
            }
        };

        checkNode(this.priceNode2101, "priceNode2101");
        checkNode(this.priceNode2102, "priceNode2102");

        // 检查主界面节点
        if (this.node) {
            console.log(`ItemBuyController主节点: ${this.node.name}, active: ${this.node.active}`);
        }
    }

    // 强制隐藏价格节点的方法（使用多种方式确保隐藏）
    private forceHidePriceNode(node: cc.Node): void {
        if (!node) {
            return;
        }

        // 方法1: 设置active为false
        node.active = false;

        // 方法2: 设置透明度为0
        node.opacity = 0;

        // 方法3: 设置缩放为0
        node.scaleX = 0;
        node.scaleY = 0;

        // 方法4: 移动到屏幕外
        node.x = -10000;
        node.y = -10000;

        // 方法5: 设置为不可见
        if (node.getComponent(cc.Sprite)) {
            const sprite = node.getComponent(cc.Sprite);
            sprite.enabled = false;
        }

        // 方法6: 隐藏所有子节点
        node.children.forEach((child) => {
            child.active = false;
        });
    }

    // 恢复价格节点显示的方法
    private restorePriceNode(node: cc.Node): void {
        if (!node) {
            return;
        }

        // 只设置active为true，不改变其他UI属性
        node.active = true;
    }



    // update (dt) {}
}
