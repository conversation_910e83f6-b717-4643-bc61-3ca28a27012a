// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { LocalizedLabel } from "../../../packages/2.4.9+版本多语言插件i18n/script/LocalizedLabel";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

@ccclass
export default class KickOutDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    content: cc.Node = null
    @property(cc.Node)
    cancelBtn: cc.Node = null
    @property(cc.Node)
    kickOutBtn: cc.Node = null

    brownText: cc.Label = null
    userId: string = ''
    localizedLabel: LocalizedLabel

    onLoad() {
        this.brownText = this.content.getComponent(cc.Label)
        this.localizedLabel = this.content.getComponent('LocalizedLabel')
    }

    start() {
        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });
        //cancel 按钮点击事件
        Tools.yellowButton(this.cancelBtn, () => {
            this.hide()
        })
        //kickOut 按钮点击事件
        Tools.redButton(this.kickOutBtn, () => {
            this.hide()
            //发送玩家被踢出的消息
            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteKickOut, { 'userId': this.userId });
        })

    }

    show(userId: string, nickname: string) {
        this.node.active = true
        this.boardBg.scale = 0
        this.userId = userId

        this.brownText.string = window.getLocalizedStr('kickout2')
        this.localizedLabel.bindParam(this.truncateString(nickname))

        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();


    }

    hide() {
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    truncateString(str: string): string {
        if (str.length > 10) {
            return str.slice(0, 10) + '...';
        } else {
            return str;
        }
    }

    // update (dt) {}
}
