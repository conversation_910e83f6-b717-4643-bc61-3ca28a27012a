// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html


import ItemBuyController from "./ItemBuy Controller";
import ShipController from "../common/ShipController";
import SkinManager from "../common/SkinManager";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";
import { GlobalBean } from "../bean/GlobalBean";
import GlobalManagerController from "../GlobalManagerController";

const { ccclass, property } = cc._decorator;


//游戏道具介绍页面
@ccclass
export default class ItemController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property([cc.Node])
    shipSkinImages: cc.Node[] = [] // 船皮肤图片节点数组
    @property(ItemBuyController)
    itemBuyController: ItemBuyController = null // 购买页面控制器

    // 选择节点（在主界面显示当前选中的皮肤）
    @property(cc.Node)
    choose001: cc.Node = null // 第一个皮肤的选择节点
    @property(cc.Node)
    choose002: cc.Node = null // 第二个皮肤的选择节点
    @property(cc.Node)
    choose003: cc.Node = null // 第三个皮肤的选择节点

    backCallback: Function = null //隐藏弹窗的回调

    // 皮肤拥有状态缓存
    private skinOwnershipCache: { [skinId: number]: boolean } = {
        2100: true,  // 默认皮肤，已拥有
        2101: false, // 高级皮肤1，默认未拥有
        2102: false  // 高级皮肤2，默认未拥有
    }

    onLoad() {

    }

    start() {

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });

        // 为每个船皮肤图片添加点击事件
        this.setupShipSkinClickEvents();

        // 初始化选择节点状态 - 根据当前皮肤显示对应的choose节点
        this.initializeChooseNodesBasedOnCurrentSkin();

        // 请求获取商品列表
        this.requestProductConfigs();

        // 请求获取用户皮肤信息
        this.requestUserSkinInfo();

    }

    // 设置船皮肤图片的点击事件
    private setupShipSkinClickEvents() {
        if (!this.shipSkinImages || this.shipSkinImages.length === 0) {
            console.warn("ItemController: 没有设置船皮肤图片节点");
            return;
        }

        this.shipSkinImages.forEach((skinImage, index) => {
            if (!skinImage) {
                console.warn(`ItemController: 船皮肤图片节点 ${index} 为空`);
                return;
            }

            // 为每个皮肤图片添加点击事件
            Tools.setTouchEvent(skinImage, null, () => {
                this.showBuyDialog(index);
            }, null);
        });
    }

    // 处理皮肤点击事件
    private showBuyDialog(skinIndex: number) {
        if (!this.itemBuyController) {
            console.error("ItemController: ItemBuyController 未设置");
            return;
        }

        // 创建皮肤数据
        const skinData = this.createSkinData(skinIndex);
        const skinId = skinData.id;

        // 检查皮肤是否已拥有
        if (this.getSkinOwnership(skinId)) {
            // 已拥有皮肤，直接执行换皮肤逻辑，不打开购买页面
            this.changeSkinDirectly(skinId);
        } else {
            // 未拥有皮肤，显示购买页面
            this.itemBuyController.showSkinPurchase(skinData, () => {
                // 购买页面关闭回调
            }, this);
        }
    }

    // 直接换皮肤（已拥有皮肤的情况）
    private changeSkinDirectly(skinId: number) {
      

        // 显示对应的选择节点
        this.showChooseNode(skinId);

        // 更新皮肤管理器的当前皮肤
        this.updateCurrentSkin(skinId);

        // 发送SetSkin请求到后端，确保皮肤状态同步
        this.sendSetSkinRequest(skinId);
    }

    // 发送设置皮肤请求到后端
    private sendSetSkinRequest(skinId: number) {
       
        const setSkinRequest = {
            id: skinId  // 要设置的皮肤ID (2100-2102)
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.SetSkin, setSkinRequest);
    }





    // 更新当前皮肤
    private updateCurrentSkin(skinId: number) {
        // 使用SkinManager来更新当前皮肤
        const skinManager = SkinManager.getInstance();
        skinManager.setCurrentSkin(skinId);

        // 刷新场景中所有船只的皮肤
        ShipController.refreshAllShips();
    }

    // 创建皮肤数据
    private createSkinData(skinIndex: number): any {
        // 根据注释中的API，皮肤ID为2100-2102
        const skinId = 2100 + skinIndex;

        // 从缓存中获取皮肤拥有状态
        const isHave = this.skinOwnershipCache[skinId] || false;

        // 创建皮肤数据，使用缓存的拥有状态
        const skinData = {
            id: skinId,
            type: 1, // 1=战舰皮肤
            price: skinId === 2100 ? 0 : 300, // 默认皮肤免费，其他300金币
            isHave: isHave, // 使用缓存的拥有状态
            name: `战舰皮肤 ${skinIndex + 1}`,
            imagePath: `test_skin_${skinIndex + 1}` // 皮肤图片路径（不包含价格信息）
        };


        return skinData;
    }

    // 请求获取商品列表
    private requestProductConfigs() {
        WebSocketManager.GetInstance().sendMsg(MessageId.ProductConfigs, {});
    }

    // 根据皮肤ID获取对应的选择节点
    private getChooseNodeBySkinId(skinId: number): cc.Node | null {
        switch (skinId) {
            case 2100:
                return this.choose001; // 第一个皮肤
            case 2101:
                return this.choose002; // 第二个皮肤
            case 2102:
                return this.choose003; // 第三个皮肤
            default:
                console.warn(`未知的皮肤ID: ${skinId}`);
                return null;
        }
    }

    // 显示指定皮肤的选择节点，隐藏其他选择节点
    showChooseNode(skinId: number) {
        // 隐藏所有选择节点
        this.hideAllChooseNodes();

        // 显示指定皮肤的选择节点
        const chooseNode = this.getChooseNodeBySkinId(skinId);
        if (chooseNode) {
            chooseNode.active = true;
        }
    }

    // 隐藏所有选择节点
    private hideAllChooseNodes() {
        if (this.choose001) {
            this.choose001.active = false;
        }
        if (this.choose002) {
            this.choose002.active = false;
        }
        if (this.choose003) {
            this.choose003.active = false;
        }

    }

    // 初始化选择节点状态 - 根据当前皮肤显示对应的choose节点
    private initializeChooseNodesBasedOnCurrentSkin() {
        // 获取当前使用的皮肤ID
        const skinManager = SkinManager.getInstance();
        const currentSkinId = skinManager.getCurrentSkinId();

        // 根据当前皮肤ID显示对应的choose节点
        this.updateChooseNodesBySkinId(currentSkinId);
    }



    // 更新皮肤拥有状态缓存
    updateSkinOwnership(skinId: number, isHave: boolean) {
        this.skinOwnershipCache[skinId] = isHave;
    }

    // 获取皮肤拥有状态
    getSkinOwnership(skinId: number): boolean {
        return this.skinOwnershipCache[skinId] || false;
    }

    show(backCallback: Function) {
        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0

        // 初始化选择节点状态 - 根据当前皮肤显示对应的choose节点
        this.initializeChooseNodesBasedOnCurrentSkin();

        // 重新请求用户皮肤信息，确保状态最新
        this.requestUserSkinInfo();

        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // 请求获取用户皮肤信息
    private requestUserSkinInfo(): void {
       

        // 检查是否在有效的游戏状态中，避免在没有房间上下文时发送请求
        const loginData = GlobalBean.GetInstance().loginData;
        if (!loginData || loginData.roomId <= 0) {
           
            return;
        }

        // 发送 GetUserSkin 请求到后端
        WebSocketManager.GetInstance().sendMsg(MessageId.GetUserSkin, {});
    }

    // 处理用户皮肤信息响应
    public onGetUserSkinResponse(data: any): void {
        if (!data) {
            console.error("ItemController: 用户皮肤信息响应数据为空");
            return;
        }

        const skinManager = SkinManager.getInstance();

        // 设置当前使用的皮肤
        if (data.currentSkinId !== undefined) {
            skinManager.setCurrentSkin(data.currentSkinId);
            // 更新选择节点显示
            this.updateChooseNodesBySkinId(data.currentSkinId);
        }

        // 设置拥有的皮肤列表
        if (data.ownedSkins && Array.isArray(data.ownedSkins)) {
            try {
                // 尝试设置拥有的皮肤列表
                if (skinManager['ownedSkins'] !== undefined) {
                    skinManager['ownedSkins'] = data.ownedSkins;
                } else if (typeof skinManager.setOwnedSkins === 'function') {
                    skinManager.setOwnedSkins(data.ownedSkins);
                }

                // 更新UI显示状态
                this.updateUIBasedOnOwnedSkins(data.ownedSkins);
            } catch (error) {
                console.error("ItemController: 设置皮肤列表失败", error);
            }
        }
    }

    // 根据皮肤ID更新选择节点显示
    private updateChooseNodesBySkinId(skinId: number): void {
        this.hideAllChooseNodes();

        switch (skinId) {
            case 2100:
                if (this.choose001) this.choose001.active = true;
                break;
            case 2101:
                if (this.choose002) this.choose002.active = true;
                break;
            case 2102:
                if (this.choose003) this.choose003.active = true;
                break;
        }
    }

    // 根据拥有的皮肤更新UI状态
    private updateUIBasedOnOwnedSkins(ownedSkins: number[]): void {
        // 首先重置所有皮肤为未拥有状态
        this.skinOwnershipCache[2100] = false;
        this.skinOwnershipCache[2101] = false;
        this.skinOwnershipCache[2102] = false;

        // 更新皮肤拥有状态缓存
        ownedSkins.forEach(skinId => {
            this.updateSkinOwnership(skinId, true);
        });

        // 通知 ItemBuy Controller 更新价格显示
        if (this.itemBuyController) {
            // 隐藏已拥有皮肤的价格节点
            ownedSkins.forEach(skinId => {
                this.itemBuyController.hideSkinPrice(skinId);
            });

            // 直接调用ItemBuyController的更新方法，确保所有价格节点状态正确
            try {
                if (this.itemBuyController && this.itemBuyController.updateAllPriceNodesBasedOnOwnership) {
                    this.itemBuyController.updateAllPriceNodesBasedOnOwnership();
                }
            } catch (error) {
                console.error("ItemController: 更新价格节点失败", error);
            }

            // 同时通知ItemBuyController处理GetUserSkin响应
            try {
                if (this.itemBuyController.onGetUserSkinResponse) {
                    const skinManager = SkinManager.getInstance();
                    this.itemBuyController.onGetUserSkinResponse({
                        ownedSkins: ownedSkins,
                        currentSkinId: skinManager.getCurrentSkinId(),
                        _fromItemController: true  // 标记来源，避免重复调用
                    });
                }
            } catch (error) {
                console.error("ItemController: 通知ItemBuyController失败", error);
            }
        }
    }

    // 显示皮肤更换不允许的提示
    private showSkinChangeNotAllowedTip(): void {
        console.warn("提示：只能在游戏开始到战斗开始之间更换皮肤！");
    }



    // update (dt) {}
}
