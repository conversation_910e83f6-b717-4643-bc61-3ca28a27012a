// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { Publish } from "../../meshTools/tools/Publish";
import { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { GameMgr } from "../common/GameMgr";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import ToastController from "../ToastController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";
import HallCenterLayController, { CenterLaytouType } from "./HallCenterLayController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class HallParentController extends cc.Component {

    @property(cc.Node)
    boardTitle: cc.Node = null
    @property(cc.Node)
    boardIcon: cc.Node = null
    @property(cc.Label)
    beansNumber: cc.Label = null;
    @property(cc.Node)
    boardBtnBack: cc.Node = null; //返回按钮
    @property(cc.Node)
    boardBtnInfo: cc.Node = null; //游戏简介按钮
    @property(cc.Node)
    boardBtnItem: cc.Node = null; //道具介绍按钮
    @property(cc.Node)
    boardBtnSetting: cc.Node = null;  //设置按钮
    @property(HallCenterLayController)
    hallCenterLayController: HallCenterLayController = null;
    @property(ToastController)
    toastController: ToastController = null  //toast 的布局

    backClick: Function
    infoClick: Function
    itemClick: Function
    settingClick: Function
    startClick: Function
    createClick: Function
    seatCallback: Function


    // onLoad () {}
    protected onEnable(): void {
        this.updateGold()
    }

    start() {

        Tools.setTouchEvent(this.boardTitle, () => {
            GameMgr.H5SDK.ShowAppShop()
        })

        if (Publish.GetInstance().currencyIcon != null && Publish.GetInstance().currencyIcon !== '') {
            Tools.setNodeSpriteFrameUrl(this.boardIcon,Publish.GetInstance().currencyIcon)
        }

        Tools.imageButtonClick(this.boardBtnBack, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {
            let type = this.hallCenterLayController.getCenterLaytouType()
            switch (type) {
                case CenterLaytouType.HALL_AUTO_VIEW:
                    if (this.backClick) {
                        this.backClick()
                    }
                    break
                case CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                    // this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
                    //发送离开房间的信息
                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLeaveInvite, {});
                    break
                case CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                    this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
                    break
            }


        });
        Tools.imageButtonClick(this.boardBtnInfo, Config.buttonRes + 'board_btn_info_normal', Config.buttonRes + 'board_btn_info_pressed', () => {
            if (this.infoClick) {
                this.infoClick()
            }
        });
        Tools.imageButtonClick(this.boardBtnItem, Config.buttonRes + 'btn_item_normal', Config.buttonRes + 'btn_item_pressed', () => {
            if (this.itemClick) {
                this.itemClick()
            }
        });
        Tools.imageButtonClick(this.boardBtnSetting, Config.buttonRes + 'board_btn_setting_normal', Config.buttonRes + 'board_btn_setting_pressed', () => {
            if (this.settingClick) {
                this.settingClick()
            }
        });
        this.hallCenterLayController.setClick(() => {
            //start 按钮的点击回调
            if (this.startClick) {
                this.startClick()
            }
        }, () => {
            if (this.createClick) {
                this.createClick()
            }
        }, (userId: string, nickname: string) => {
            this.seatCallback(userId, nickname)
        })
    }

    //设置按钮点击事件的回调的
    setClick(backClick: Function, infoClick: Function, itemClick: Function, settingClick: Function, startClick: Function, createClick: Function, seatCallback: Function
    ) {

        this.backClick = backClick
        this.infoClick = infoClick
        this.itemClick = itemClick
        this.settingClick = settingClick
        this.startClick = startClick
        this.createClick = createClick
        this.seatCallback = seatCallback
    }

    //更新金币数量
    updateGold() {
        if (GlobalBean.GetInstance().loginData) {
            const coinValue = GlobalBean.GetInstance().loginData.userInfo.coin;

            // 处理undefined或null的情况
            if (coinValue === undefined || coinValue === null) {
                this.beansNumber.string = "0";
            } else {
                this.beansNumber.string = Tools.NumToTBMK(coinValue);
            }
        }
    }

    //房间已经解散但是自己重连回来被遗留在 房间中的处理
    exitTheRoom() {
        let type = this.hallCenterLayController.getCenterLaytouType()
        if (type === CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            this.toastController.showContent(window.getLocalizedStr('LeaveRoom'))
            this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
        }
    }

    //设置接受邀请成功
    setAcceptInvite(acceptInvite: AcceptInvite) {
        this.hallCenterLayController.setAcceptInvite(acceptInvite)
    }
    //离开房间
    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {
        this.hallCenterLayController.leaveRoom(noticeLeaveInvite)
    }

    //设置门票
    setFees() {
        this.hallCenterLayController.setFees()
    }
    //进入私人房间
    joinCreateRoom() {
        this.hallCenterLayController.joinCreateRoom()
    }
    joinError() {
        this.hallCenterLayController.joinError()
    }
    //准备 取消准备
    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {
        this.hallCenterLayController.setReadyState(noticeUserInviteStatus)
    }

    // update (dt) {}
}
