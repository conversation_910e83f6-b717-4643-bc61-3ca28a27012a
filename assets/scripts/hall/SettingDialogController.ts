// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { Publish } from "../../meshTools/tools/Publish";
import { AudioManager } from "../util/AudioManager";
import { Config } from "../util/Config";
import { LocalStorageManager } from "../util/LocalStorageManager";
import { Tools } from "../util/Tools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SettingDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Node)
    musicBtn: cc.Node = null
    @property(cc.Node)
    soundBtn: cc.Node = null
    @property(cc.Label)
    versoion: cc.Label = null //版本号

    music: boolean = true;
    sound: boolean = true;
    backCallback: Function = null //隐藏弹窗的回调

    onLoad () {
        this.music = LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager.GetInstance().getSoundSwitch();
    }

    start () {

        this.versoion.string = `V ${Publish.GetInstance().getVersion()}`;

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });
        this.setSwitch(this.musicBtn, this.music);
        this.setSwitch(this.soundBtn, this.sound);

        Tools.setTouchEvent(this.musicBtn, (node: cc.Node) => {
            this.music = !this.music;
            LocalStorageManager.GetInstance().setMusicSwitch(this.music);
            this.setSwitch(node, this.music);
            if (this.music) {
                AudioManager.playBgm();
            } else {
                AudioManager.stopBgm();
            }
        });

        Tools.setTouchEvent(this.soundBtn, (node: cc.Node) => {
            this.sound = !this.sound;
            LocalStorageManager.GetInstance().setSoundSwitch(this.sound);
            this.setSwitch(node, this.sound);
        });
    }

    setSwitch(node: cc.Node, switchType: boolean) {
        if (switchType) {
            Tools.setNodeSpriteFrame(node, Config.buttonRes + 'btn_switch_02');
        } else {
            Tools.setNodeSpriteFrame(node, Config.buttonRes + 'btn_switch_01');
        }

    }

    show(backCallback:Function ) {
        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }


    // update (dt) {}
}
