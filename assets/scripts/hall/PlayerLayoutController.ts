// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { GlobalBean } from "../bean/GlobalBean";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlayerLayoutController extends cc.Component {

    @property(cc.Node)
    player2: cc.Node = null; //玩家人数是 2 的节点
    @property(cc.Node)
    player3: cc.Node = null; //玩家人数是 3 的节点
    @property(cc.Node)
    player4: cc.Node = null; //玩家人数是 4 的节点
    @property(cc.Node)
    player5: cc.Node = null; //玩家人数是 5 的节点

    @property(cc.Node)
    sliding: cc.Node = null; //选中玩家人数的滑动条

    //移动时间
    moveTime: number = 0.1;


    // onLoad () {}
    protected onEnable(): void {
        let  players = GlobalBean.GetInstance().players
        switch(players){
            case 2:
                this.movePlayersliding(this.player2)
                break;
            case 3:
                this.movePlayersliding(this.player3)
                break;
            case 4:
                this.movePlayersliding(this.player4)
                break;
            case 5:
                this.movePlayersliding(this.player5)
                break;
        }
        
    }

    start() {
        
        this.setListener(this.player2,2)
        this.setListener(this.player3,3)
        this.setListener(this.player4,4)
        this.setListener(this.player5,5)
    }

    //设置监听
    setListener(playerNode: cc.Node,players:number) {

        Tools.setTouchEvent(playerNode, () => {
            GlobalBean.GetInstance().players = players
            this.movePlayersliding(playerNode)
        })

    }

    //移动滑动条
    movePlayersliding(playerNode: cc.Node){
        cc.tween(this.sliding)
        .to(this.moveTime, { position: playerNode.position })
        .start(); // 开始动画
    }



    // update (dt) {}
}
