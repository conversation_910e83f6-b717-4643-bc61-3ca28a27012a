// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { GameMgr } from "../common/GameMgr";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LeaveDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Node)
    cancelBtn: cc.Node = null
    @property(cc.Node)
    leaveBtn: cc.Node = null
    @property(cc.Label)
    tipContent: cc.Label = null

    type: number = 0 //0是完全退出这个游戏 1是退出本局游戏

    backCallback: Function = null //隐藏弹窗的回调


    // onLoad () {}

    start() {

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });
        //cancel 按钮点击事件
        Tools.yellowButton(this.cancelBtn, () => {
            this.hide()
        })
        //leave 按钮点击事件
        Tools.redButton(this.leaveBtn, () => {
            if(this.type===0){
                GameMgr.H5SDK.CloseWebView()   //这个是退出游戏的
            }else{
               //这个是退出本局游戏的
               this.hide()

               // 设置主动退出标志
               const GlobalManagerController = require("../GlobalManagerController").default;
               const globalMgr = GlobalManagerController.getInstance();
               if (globalMgr) {
                   globalMgr.setActivelyLeaving(true);
               }

               //退出当前房间游戏
               WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLeaveRoom, { 'isConfirmLeave': true });
            }
        })
    }


    show(type: number,backCallback:Function ) {
        this.type = type
        this.backCallback = backCallback
        if(type===0){
            this.tipContent.string = window.getLocalizedStr('ExitApplication') //显示完全退出的文案退出
        }else{
            this.tipContent.string = window.getLocalizedStr('QuitTheGame') //显示退出本局游戏的文案
        }

        this.node.active = true
        this.boardBg.scale = 0
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // update (dt) {}
}
