// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { LoginData, RoomConfig } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

export enum AutoOrRoom {
    AUTO,
    ROOM
}

@ccclass
export default class HallAutoController extends cc.Component {

    @property(cc.Node)
    autoLay: cc.Node = null;
    @property(cc.Node)
    roomLay: cc.Node = null;
    @property(cc.Node)
    boardTicketLay: cc.Node = null;
    @property(cc.Node)
    startButton: cc.Node = null;
    @property(cc.Node)
    createButton: cc.Node = null;
    @property(cc.Node)
    joinButton: cc.Node = null;


    boardTabAuto: cc.Node = null
    boardTabRoom: cc.Node = null

    boardTicketBtnMinus: cc.Node = null   //门票➖
    boardTicketBtnPlus: cc.Node = null     //门票➕
    ticketNumber: cc.Label = null  //门票价格

    autoOrRoom: AutoOrRoom = null

    startClick: Function = null
    createClick: Function = null
    joinClick: Function = null

    autoRoomConfig: RoomConfig;//auto 的房间数据
    createRoomConfig: RoomConfig;//room 的房间数据

    autoFeesPosition = 0;
    createPosition = 0;

    onLoad() {
        this.boardTabAuto = this.autoLay.getChildByName('board_tab_02')
        this.boardTabRoom = this.roomLay.getChildByName('board_tab_02')
        this.boardTicketBtnMinus = this.boardTicketLay.getChildByName('board_ticket_btn_minus_normal')
        this.boardTicketBtnPlus = this.boardTicketLay.getChildByName('board_ticket_btn_plus_normal')
        this.ticketNumber = this.boardTicketLay.getChildByName('ticket_number').getComponent(cc.Label)
    }


    //设置游戏开始数据
    setFees() {
        let roomConfig: RoomConfig[] = GlobalBean.GetInstance().loginData.roomConfigs
        //房间类型 1-普通场 2-私人场
        for (let i = 0; i < roomConfig.length; i++) {
            if (roomConfig[i].id == 1) {
                this.autoRoomConfig = roomConfig[i];
            }
            if (roomConfig[i].id == 2) {
                this.createRoomConfig = roomConfig[i];
            }
        }
        this.setAutoOrRoom(AutoOrRoom.AUTO);//设置初始值
    }


    start() {

        Tools.setTouchEvent(this.autoLay, () => {
            this.setAutoOrRoom(AutoOrRoom.AUTO)
        })
        Tools.setTouchEvent(this.roomLay, () => {
            this.setAutoOrRoom(AutoOrRoom.ROOM)
        })

        //start 按钮点击事件
        Tools.greenButton(this.startButton, () => {
            if (this.startClick) {
                this.startClick()
            }
        })


        //create 按钮点击事件
        Tools.greenButton(this.createButton, () => {
            if (this.createClick) {
                this.createClick()
            }
        })

        //join 按钮点击事件
        Tools.yellowButton(this.joinButton, () => {
            if (this.joinClick) {
                this.joinClick()
            }
        })

        //点击按钮减
        Tools.imageButtonClick(this.boardTicketBtnMinus, Config.buttonRes + 'board_ticket_btn_minus_normal', Config.buttonRes + 'board_ticket_btn_minus_pressed', () => {
            if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (this.autoFeesPosition === 0) {
                    return;
                }
                this.autoFeesPosition--;

                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);
            } else {
                if (this.createPosition === 0) {
                    return;
                }
                this.createPosition--;

                this.setTicketsNum(this.createRoomConfig, this.createPosition);
            }

        })
        //点击按钮加
        Tools.imageButtonClick(this.boardTicketBtnPlus, Config.buttonRes + 'board_ticket_btn_plus_normal', Config.buttonRes + 'board_ticket_btn_plus_pressed', () => {
            if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (this.autoFeesPosition === this.autoRoomConfig.fees.length - 1) {
                    return;
                }
                this.autoFeesPosition++;
                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);
            } else {
                if (this.createPosition === this.createRoomConfig.fees.length - 1) {
                    return;
                }
                this.createPosition++;

                this.setTicketsNum(this.createRoomConfig, this.createPosition);
            }

        })
    }

    //赋值门票价格
    private setTicketsNum(roomConfig: RoomConfig, position: number) {
        let fees = roomConfig.fees[position];
        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';
        GlobalBean.GetInstance().ticketsNum = fees;
    }


    //设置展示 auto 还是 room 的view
    setAutoOrRoom(autoOrRoom: AutoOrRoom) {
        if (this.autoOrRoom === autoOrRoom) {
            return
        }
        this.autoOrRoom = autoOrRoom
        GlobalBean.GetInstance().autoAndRoom = autoOrRoom
        this.boardTabAuto.active = false
        this.boardTabRoom.active = false
        this.startButton.active = false
        this.createButton.active = false
        this.joinButton.active = false


        switch (autoOrRoom) {
            case AutoOrRoom.AUTO:
                this.boardTabAuto.active = true
                this.startButton.active = true
                this.createButton.active = false
                this.joinButton.active = false
                break
            case AutoOrRoom.ROOM:
                this.boardTabRoom.active = true
                this.startButton.active = false
                this.createButton.active = true
                this.joinButton.active = true
                break
        }



        if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
            if (this.autoRoomConfig) {
                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);
            } else {
                this.ticketNumber.string = window.getLocalizedStr('free')
            }

        } else {
            if (this.createRoomConfig) {
                this.setTicketsNum(this.createRoomConfig, this.createPosition);
            } else {
                this.ticketNumber.string = window.getLocalizedStr('free')
            }

        }
    }

    //设置点击按钮的回调
    setButtonClick(startClick: Function, createClick: Function, joinClick: Function) {
        this.startClick = startClick
        this.createClick = createClick
        this.joinClick = joinClick
    }

    // update (dt) {}
}
