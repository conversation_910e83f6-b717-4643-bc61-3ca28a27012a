export interface LoginData {
    userInfo: UserInfo;
    roomId: number; //这个房间号>0 的话 就是已经在游戏中断线重连的
    inviteCode: number; //这个邀请码>0 的话 就是已经创建好房间 之后断线重连的
    roomConfigs: RoomConfig[];
}

export interface RoomConfig {
    id: number;//房间类型 1-普通场 2-私人场
    fees: number[];// 入场费列表
    playerNums: number[];// 玩家人数列表
}

export interface UserInfo {
    userId: string;// 玩家ID
    nickname: string;// 昵称
    avatar: string; // 头像
    coin: number;// 当前金币
    serverTime: number;// 服务器的秒级时间戳

}

//请求匹配
export interface PairRequest {
    playerNum: number;// 玩家人数
    fee: number;// 房间费
    gridNum: number; // 添加gridNum属性
}

//创建邀请的请求
export interface CreateInvite {
    playerNum: number;// 玩家人数
    fee: number;// 房间费
}

export interface InviteInfo {
    inviteCode: number;// 邀请码
    playerNum: number;// 玩家人数
    fee: number;// 房间费
    propMode: number;// 0 无道具 1 有道具
    creatorId: string;// 创建者用户ID
    users: InviteUser[];// 匹配到的所有玩家
}

export interface InviteUser {
    userId: string;// 玩家Id
    nickname: string;// 昵称
    avatar: string;// 头像
    ready: boolean;// 是否准备好
    creator: boolean;// 是否邀请的发起者
    onLine: boolean;// 是否在线
}


//接受邀请的请求
export interface AcceptInvite {
    inviteCode: number;// 邀请码
}

// AcceptInvite 接受邀请
export interface AcceptInvite {
    userId: string;// 用户ID
    inviteInfo: InviteInfo;// 邀请信息
}

// InviteReady 邀请者改变准备状态
export interface InviteReady {
    ready: boolean;// true 准备 false 取消准备
}

// NoticeUserInviteStatus 广播玩家的邀请状态˝
export interface NoticeUserInviteStatus {
    userId: string;// 玩家Id
    ready: boolean;// 是否准备好
    onLine: boolean;// 是否在线
}

// ChangeInviteCfg 更改邀请配置
export interface ChangeInviteCfgRequest {
    inviteCode: number;// 邀请码
    fee: number;// 房间费
}

export interface ChangeInviteCfgResult {
    fee: number;// 房间费
}

// NoticeLeaveInvite 邀请广播有人离开
export interface NoticeLeaveInvite {
    userId: string;// 玩家Id
    isCreator: boolean;// 离开者是否是创建者
}

//创建者踢出玩家请求
export interface InviteKickOut {
    userId: string // 被踢除的玩家Id
}

//通知邀请状态
export interface NoticeUserInviteStatus {
    userId: string  //用户 id
    ready: boolean //准备状态
    onLine: boolean //是否在线
}


//////////////////////////////////////////游戏内的数据//////////////////////////////////////////

//开始游戏和 重连都走这一个
export interface NoticeStartGame {

    roomId: number;// 房间ID
    roomType: number; // 房间类型  EnumBean.RoomType
    playerNum: number;// 玩家人数
    fee: number; // 房间费
    propMode: number;// 道具模式
    specialFull: number;// 多少特殊块填满技能槽
    specialRemoved: number;// 已移除特殊块数量
    users: RoomUser[];// 匹配到的所有玩家
    gameStatus: number;// 游戏状态  EnumBean.GameStatus
    countDown: number;// 游戏状态倒计时
    blockList: Block[];// 地图
    opIndex: number;// 操作索引
    lookPos: number;// 旁观座位号
    curTask: CurTask// 当前任务

}

export interface RoomUser {
    userId: string;// 用户ID
    nickName: string;// 昵称
    avatar: string;// 头像
    pos: number;// 座位号
    coin: number;// 玩家最新金币
    status: number;// 玩家状态   EnumBean.UserStatus
    score: number;// 消除分数
    rank: number //当前排名
}

export interface Block {
    id: number; // 块的ID
    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色
    type: number; // 块类型 EnumBean.BlockType 1-普通块 2-箭头X 3-箭头Y 4-炸弹 5-彩虹 6-超级箭头 7-炸弹箭头 8-彩虹箭头 9-超级炸弹 10-彩虹炸弹 11-超级彩虹
    obstacle: Obstacle; // 障碍物  
}

export interface Obstacle {
    type: number; //EnumBean.Obstacle
    fromUid: string //如果 type==1002 的话会有两个用户 id 用，隔开，前面的id是发射锁链的用户后面的id是发射冰块的用户

}
// ObstacleBlock 障碍物的块
export interface ObstacleBlock {
    id: number; // 旧的块ID(未掉落前的位置)
    obstacle: Obstacle // 障碍物 （这里代表消除障碍物之后 剩下的状态）
}


export interface CurTask {
    id: number;// 任务ID
    color: number;// 目标颜色
    require: number;// 需要完成的数量
    current: number;// 当前进度
    reward: number;// 奖励(1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)
}


//移动块-MoveBlock 的请求数据   
export interface MoveBlock {
    id: number;// 块ID
    otherId: number;// 另一个块ID
}

// NoticeScoreChg 通知分数变化(消息ID-ScoreChg)
export interface NoticeScoreChg {
    userId: string;// 玩家Id
    score: number;// 新的分数
    taskObstacle: ObstacleBlock // 障碍物

}
// MoveBlockFail 移动块失败
export interface MoveBlockFail {
    id: number;// 块ID
    otherId: number;// 另一个块ID
}

// NoticeMoveBlock 通知移动块
export interface NoticeMoveBlock {
    userId: string;// 玩家Id
    id: number;// 块ID
    otherId: number;// 另一个块ID
    score: number;// 新的分数
    specialRemoved: number;// 新的已移除特殊块数量
    groups: MoveGroup[];// 匹配分组的列表
    activate: Activate;// 激活特殊道具
    opIndex: number;// 操作索引
    isTaskOver: boolean// // 是否旧任务结束
    taskReward: TaskReward// // 任务奖励
    curTask: CurTask// 当前任务
    obstacles:ObstacleBlock[] // 障碍物 (在这里没啥用  就是用来打印数据的)
}

// TaskReward 任务奖励
export interface TaskReward {
    id: number;// 块ID
    type: number;// 块类型 EnumBean.BlockType

}

// NoticeActivate 通知激活特殊道具
export interface Activate {
    times: number  // 激活次数
    rectIDs:number[] //激活区域四个块 中左上角的块
    groups: MoveGroup[] // 匹配分组的列表
}

// MoveGroup 匹配分组
export interface MoveGroup {
    matches: Match[];// 匹配列表
    drops: DropBlock[];// 新掉落的块列表
    scoreChg: number;// 分数变化
    obstaclesChg:ObstacleBlock[] //
}

export interface DropBlock {
    id: number; // 块的ID
    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色
}

export interface UserSettlement {
    userId: string;// 玩家Id
    coinChg: number;// 金币变化
    coin: number;// 玩家最新金币
    rank: number;// 排名
    score: number;// 分数
}

// Match 单个匹配
export interface Match {
    removes: Remove[];// 移动后可消除的块列表(也可能两个特殊块叠一起，不会构成>=3的同颜色消除)
    merge: MergeBean;// 合成块
    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)
}

export interface MergeBean{
    id: number;// 块ID
    type:null;// 块类型 EnumBean.BlockType
}

// Passive 被动消除
export interface Passive {
    id: number;// 块ID
    type: number;// 块类型 EnumBean.BlockType
    score: number;// 得分
    obstacle: number;// 障碍物
    removes: Remove[];// 消除的块列表
    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)
}

// PassiveRemove 被动消除移除的块
export interface Remove {
    id: number;// 块ID
    score: number;// 得分
}

export interface NoticeSettlement {
    users: UserSettlement[];// 闲家结算列表
}

//玩家主动离开房间的请求数据
export interface LeaveRoom {
    isConfirmLeave: boolean;// 确认离开房间
}

//玩家主动离开房间-LeaveRoom
export interface NoticeLeaveRoom {
    userId: string;// 玩家Id
}

//////////////////////////////////////////断线重连相关数据结构//////////////////////////////////////////

// 断线重连请求参数
export interface ReconnectRequest {
    lastRoomID: number;// 上一次房间ID
}

// 海战房间用户信息
export interface SeabattleRoomUser {
    userId: string;// 用户ID
    nickName: string;// 昵称
    avatar: string;// 头像
    pos: number;// 座位号
    coin: number;// 玩家最新金币
    status: number;// 玩家状态
    skinId: number;// 当前使用的皮肤ID (2100-2102)
    score: number;// 分数
    rank: number;// 排名
}

// 船只部署信息（带锚点）
export interface ShipWithAnchor {
    id: number;// 船只ID
    type: string;// 船只类型名称
    gridX: number;// 网格X坐标
    gridY: number;// 网格Y坐标
    direction: number;// 船只方向 0-上 1-右 2-下 3-左
    size: number;// 船只大小（格子数）
}

// 攻击历史记录项（根据后端实际返回格式定义）
export interface AttackHistoryItem {
    attackerId: string;// 攻击者ID
    x: number;// 攻击坐标X (0-9)
    y: number;// 攻击坐标Y (0-9)
    hit: boolean;// 是否命中
    sunk: boolean;// 是否击沉（仅在hit为true时有意义）
    shipId?: number;// 击中的战舰ID（仅当hit为true时存在）
    anchorX?: number;// 被击沉战舰的锚点X坐标（仅击沉时有效）
    anchorY?: number;// 被击沉战舰的锚点Y坐标（仅击沉时有效）
    direction?: number;// 被击沉战舰的方向（仅击沉时有效）
}

// 玩家统计数据
export interface PlayerStats {
    userId: string;// 玩家ID
    hits: number;// 命中次数
    misses: number;// 未命中次数
    sunkShips: number;// 击沉船只数量
    remainingShips: number;// 剩余船只数量
    isAutoManaged?: boolean;// 是否处于托管状态（根据api.md，playerStats包含托管状态）
}

// 断线重连响应数据（扩展NoticeStartGame）
export interface ReconnectGameData extends NoticeStartGame {
    // 重写users字段，使用SeabattleRoomUser类型（包含skinId）
    users: SeabattleRoomUser[];// 房间内所有玩家，包含皮肤信息

    // 游戏状态信息（根据gameStatus提供）
    deploymentStatus?: { [userId: string]: boolean };// 玩家部署状态
    playerShips?: ShipWithAnchor[];// 当前玩家的战舰部署
    attackHistory?: AttackHistoryItem[];// 攻击历史记录
    currentAttacker?: string;// 当前攻击者ID
    remainingAttacks?: number;// 剩余攻击次数
    playerStats?: { [userId: string]: PlayerStats };// 玩家统计数据

    // 房间模式特有信息（仅房间模式提供）
    roomCode?: string;// 房间码
    ownerId?: string;// 房主ID
    readyStatus?: { [userId: string]: boolean };// 玩家准备状态
}

//玩家被踢出房间-KickOutUser
export interface NoticeUserKickOut {
    userId: string;// 玩家Id
}

export interface IllegalOperation {
    id: number; //移动块
    otherId: number; //被交换块
    tokenUserId: string; //当前操作用户的id，不是此次操作用户的 id
}









