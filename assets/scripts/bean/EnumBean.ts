
// RoomType 房间类型
export enum RoomType{
    RoomTypeCommon = 1, // 普通场
    RoomTypePrivate = 2, // 私人场
    RoomTypeVoice = 3, // 语聊房场
}

// UserStatus 用户状态
export enum UserStatus {
    UserStatusStand = 1, // 站立(旁观者)
    UserStatusSit = 2, // 已坐下(参与者，已分配座位号)
}


// GameStatus 游戏状态
export enum GameStatus{
    GameStatusMove = 1, // 移动消除

}

// BlockColor 块颜色
export enum BlockColor{
    BlockColorRed = 1, // 红色
    BlockColorBlue = 2, // 蓝色
    BlockColorGreen = 3, // 绿色
    BlockColorYellow = 4, // 黄色
    BlockColorBrown = 5, // 棕色
}


// BlockType 块类型(普通块、被动技能块)
export enum BlockType{
    BlockTypeCommon = 1, // 普通块
    BlockTypeArrowX = 2, // 箭头X
    BlockTypeArrowY = 3, // 箭头Y
    BlockTypeBomb = 4, // 炸弹
    BlockTypeRainbow = 5, // 彩虹
    BlockTypeSuperArrow = 6, // 超级箭头
    BlockTypeBombArrow = 7, // 炸弹箭头
    BlockTypeRainbowArrow = 8, // 彩虹箭头
    BlockTypeSuperBomb = 9, // 超级炸弹
    BlockTypeRainbowBomb = 10, // 彩虹炸弹
    BlockTypeSuperRain = 11, // 超级彩虹

}

// Obstacle 障碍物类型
export enum Obstacle{
    ObstacleDefault = 0, // 默认正常
    ObstacleChain = 1000, // 锁链-不能移动 能参与消除 相邻块爆炸1次解除(箭头不算爆炸)
    ObstacleIce = 1001, // 冰块-不能移动，不参与消除，相邻块爆炸1次解除(箭头不算爆炸)
    ObstacleChainIce = 1002, // 锁链+冰块-不能移动，不参与消除，相邻块爆炸2次解除(箭头不算爆炸)
}

//实时任务的类型
export enum RewardType{
    // (1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)
     RAINBOW = 1,
     ICE = 2,
     LOCK = 3,
     ARROW_X = 4,
     ARROW_Y = 5
 }

 export enum MoveType{
    MoveDefault = 0, //默认没有任何操作 
    MoveStart = 1, //发起移动操作
    MoveEnd = 2,  //收到移动结果的回执
 }


