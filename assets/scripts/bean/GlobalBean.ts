import { Singleton } from "../../meshTools/Singleton";
import { AutoOrRoom } from "../hall/HallAutoController";
import { CurTask, InviteInfo, LoginData, NoticeStartGame, ObstacleBlock, RoomUser } from "./GameBean";

export class GlobalBean extends Singleton{

    loginData:LoginData = null;  //用户数据

    public autoAndRoom = AutoOrRoom.AUTO; //当前选中的是哪种游戏模式
    public players = 2;  //玩家人数 默认两个人
    public ticketsNum = 0;  //门票价格


     //这个数据是游戏开始之后 服务器返回的数据
    public noticeStartGame: NoticeStartGame //开始游戏的数据
    public inviteInfo: InviteInfo;//这个是创建完房间返回的数据
    

    cleanData(){
        this.noticeStartGame = null
        this.inviteInfo = null

        // 清理roomId，防止下次启动时误触发重连
        if (this.loginData) {
            this.loginData.roomId = 0;
        }
    }

     //调整 users 数据的展示顺序把自己的数据调整到第一位， 并不是调整座位数据
     adjustUserData(): RoomUser[] {
        // 检查 noticeStartGame 是否存在
        if (!this.noticeStartGame || !this.noticeStartGame.users) {
            console.warn("adjustUserData: noticeStartGame 或 users 数据为空");
            return [];
        }

        // 创建原始数据的副本，避免修改原始数据
        let user: RoomUser[] = JSON.parse(JSON.stringify(this.noticeStartGame.users));
        const index = user.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);
        if (index !== -1) {
            const element = user.splice(index, 1)[0];
            user.unshift(element);
            return user;
        }
        return user;
    }

    getQueue(){
        return BlockingQueue.getInstance<number>(1000);
    }

}