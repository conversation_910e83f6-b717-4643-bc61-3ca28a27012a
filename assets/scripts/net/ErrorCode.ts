//错误码
export enum ErrorCode {
    OK = 0, // 操作成功
    ErrGameId = 1, // 错误的GameId
    ErrGameMode = 3, // 无效的游戏模式
    ErrRequestUser = 5, // 从平台请求用户信息失败
    ErrInPair = 6,// 玩家已经在匹配队列中
    ErrNotEnoughCoin = 7, // 没有足够的金币
    ErrChangeBalance = 8, // 扣除金币失败
    ErrNotFoundRoom = 9, // 没有找到指定的房间
    ErrNotFoundUser = 10, // 没有找到玩家信息
    ErrRoomConfig = 12, // 房间配置出错
    ErrParams = 13, // 请求参数错误
    ErrDefend = 16, // 系统维护中
    ErrSitHaveUser = 18, // 座位上已有其他玩家
    ErrHaveSit = 19, // 玩家已坐下
    ErrUserPlaying = 20, // 玩家游戏中

    ErrInInvite = 31,// 已经在邀请中了
    ErrPlaying = 32,// 玩家已经在游戏中了
    ErrInvalidInviteCode = 33,// 无效的邀请码
    ErrEnoughUser = 34,// 人够了,不能接受邀请
    ErrNotInvite = 35,// 不在邀请队列中
    ErrChgInvite = 36,// 修改邀请配置失败
    ErrNotInviteCreator = 37,// 不是邀请的创建者
    ErrForbidKickSelf = 38,// 不能踢除自己
    ErrInviteNotAllReady = 40,// 还有玩家没有准备好
    ErrInviteStart = 41,// 开始游戏失败
    ErrNotInPair = 44,// 不在匹配队列中



}