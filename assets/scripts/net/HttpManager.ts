

import { IHttpMsgBody } from "./IHttpMsgBody";
import { HttpUtils } from "./HttpUtils";
import { AutoMessageBean, AutoMessageId } from "./MessageBaseBean";
import { GameServerUrl } from "./GameServerUrl";
import { MeshTools } from "../../meshTools/MeshTools";
import { GameMgr } from "../common/GameMgr";
import { EventType } from "../common/EventCenter";

const GameRouteUrlDef = {
    0: {
        101: "https://game-dev.jieyou.shop/game_route/"
    },

    1: {
        101: "https://game-cn-test.jieyou.shop/game_route/",
        8001: "https://gameapi-test.soofun.online/game_route/"
    },

    2: {
        101: "https://mesh-gameapi.jieyou.shop/game_route/",
        201: "https://aws-gameapi.jieyou.shop/game_route/",
        301: "https://bysk.gameapi.gg.jieyou.shop/game_route/",
        401: "https://gameapi.fra.jieyou.shop/game_route/",
        8001: "https://gameapi.soofun.online/game_route/"
    }
}



enum InterfaceDef {
    GET_ADDRESS = "get_addr",
    UPDATE_TIME = "update_time"
}

export class HttpManager {

    private static _inst: HttpManager = null;

    public static get Instance(): HttpManager {
        if (this._inst == null) {
            this._inst = new HttpManager();
        }

        return this._inst;
    }

    public CheckUrlTimer: any = null;

    public get Url(): string {
        let env: number = MeshTools.Publish.getEnv();
        return GameRouteUrlDef[env][MeshTools.Publish.gsp];
    }

    public async ReqServerUrl(callBack:Function) {
        let params: IHttpMsgBody.IReqGetGameAddress = {} as IHttpMsgBody.IReqGetGameAddress;
        params.user_id = MeshTools.Publish.userId;
        params.room_id = MeshTools.Publish.roomId;
        params.game_mode = MeshTools.Publish.gameMode;
        params.game_id = MeshTools.Publish.getGameId().toString();
        params.app_id = MeshTools.Publish.appId.toString();
        params.app_channel = MeshTools.Publish.appChannel;

        let url: string = this.Url + InterfaceDef.GET_ADDRESS;
        let data: IHttpMsgBody.IResGetGameAddress = await HttpUtils.SyncGet(url, params);
        if (data.code == 200) {
            GameServerUrl.Http = data.data.http_addr;
            GameServerUrl.Ws = data.data.ws_addr;
            
            this.StartCheckServerUrlState();
            callBack()
            let autoMessageBean: AutoMessageBean = {
                'msgId': AutoMessageId.HttpSucceed,//短链链接成功
                'data': {}
            }
            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
            return;
        }

        let autoMessageBean: AutoMessageBean = {
            'msgId': AutoMessageId.GameRouteNotFoundMsg,//游戏线路异常的通知
            'data': {code: window.getLocalizedStr('GameRouteNotFound')}
        }
        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
    }

    public ReqServerUrlState(): void {
        if (!window.navigator.onLine) {
            setTimeout(() => {
                GameMgr.Console.Error('短链接重连')
                this.ReqServerUrlState();
            }, 1000);
            return;
        }

        let params: IHttpMsgBody.IReqUpdateTime = {} as IHttpMsgBody.IReqUpdateTime;
        params.user_id = MeshTools.Publish.userId;
        params.room_id = MeshTools.Publish.roomId;
        params.game_mode = MeshTools.Publish.gameMode;
        params.game_id = MeshTools.Publish.getGameId().toString();
        params.app_id = MeshTools.Publish.appId.toString();
        params.app_channel = MeshTools.Publish.appChannel;
        params.curr_http_addr = GameServerUrl.Http;
        params.curr_ws_addr = GameServerUrl.Ws;

        let url: string = this.Url + InterfaceDef.UPDATE_TIME;
        HttpUtils.Get(url, params, (data: IHttpMsgBody.IResBase) => {
            if (data.code == 200) {
                this.StartCheckServerUrlState();
            }
            else {
                this.StopCheckServerUrlState();
                this.ReqServerUrl(()=>{});
            }
        });
    }

    public StartCheckServerUrlState(): void {
        this.CheckUrlTimer = setTimeout(() => {
            this.ReqServerUrlState();
        }, 180000); // 这里是三分钟
    }

    public StopCheckServerUrlState(): void {
        if (this.CheckUrlTimer != null) {
            clearInterval(this.CheckUrlTimer);
            this.CheckUrlTimer = null;
        }
    }
}