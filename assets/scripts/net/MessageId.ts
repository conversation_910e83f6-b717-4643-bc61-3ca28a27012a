//消息 id
export enum MessageId {
    MsgTypeCreateWs = 'CreateWs',                   // 创建ws连接
    MsgTypeNoticeUserCoin = 'NoticeUserCoin', // 同步玩家金币
    MsgTypeHeartbeat = 'Heartbeat',           // 心跳
    MsgTypeLogin = 'Login',               // 玩家登录
    MsgTypeUserInfo = 'UserInfo',           // 玩家请求自己信息
    MsgTypePairRequest = 'PairRequest',     // 玩家请求匹配
    MsgTypeCancelPair = 'CancelPair',        // 玩家取消匹配
    MsgTypePairResult = 'PairResult',       // 服务通知客户端匹配到了其他玩家
    MsgTypeEnterRoom = 'EnterRoom',        // 玩家请求进入房间
    MsgTypeSitDown = 'SitDown',        // 玩家请求坐下
    MsgTypeRobotSitDown = 'RobotSitDown',    // 机器人请求坐下
    MsgTypeStand = 'Stand',      // 玩家请求站起
    MsgTypeReady = 'Ready',      // 客户端通知已经准备好
    MsgTypeLeaveRoom = 'LeaveRoom',     // 玩家主动离开房间
    MsgTypeUserOffline = 'UserOffline',     // 玩家离线
    MsgTypeKickOutUser = 'KickOutUser',     // 玩家被踢出房间

    MsgTypeCreateInvite = 'CreateInvite',           // 创建邀请
    MsgTypeAcceptInvite = 'AcceptInvite',           // 接受邀请
    MsgTypeInviteReady = 'InviteReady',            // 邀请者准备
    MsgTypeChgInviteCfg = 'ChgInviteCfg',           // 邀请创建者更改玩法配置
    MsgTypeLeaveInvite = 'LeaveInvite',            // 离开邀请
    MsgTypeNoticeInviteStatus = 'NoticeInviteStatus',// 广播邀请状态
    MsgTypeInviteKickOut = 'InviteKickOut',         // 邀请创建者踢出玩家
    MsgTypeInviteStart = 'InviteStart',            // 邀请创建者开始游戏

    MsgTypeViewerList = 'ViewerList',          // 旁观者列表
    MsgTypeGameStart = 'GameStart',             // 开始游戏
    MsgTypeFirstMove = 'FirstMove',            // 先手
    MsgTypeFirstMoveEnd = 'FirstMoveEnd',            // 先手 动画结束
    MsgTypeUserPosList = 'UserPosList',          // 玩家座位号列表
    MsgTypeRollDice = 'RollDice',             // 掷骰子
    MsgTypeMoveChess = 'MoveChess',           // 移动棋子
    MsgTypeUseProp = 'UseProp',            // 使用道具
    MsgTypeChoiceProp = 'ChoiceProp',       // 挑选道具
    MsgTypeChoicePropResult = 'ChoicePropResult',// 挑选道具的结果
    MsgTypeChoiceAdvance = 'ChoiceAdvance',      // 选择前进点数
    MsgTypeMoveChessEnd = 'MoveChessEnd',     // 移动棋子结束
    MsgTypeSettlement = 'Settlement',       // 大结算
    ProductConfigs = "ProductConfigs",// 获取商品列表
    BuyProduct = "BuyProduct",// 购买商品
    SetSkin = "SetSkin",// 设置皮肤
    GetUserSkin = "GetUserSkin",// 获取用户皮肤信息
    NoticeUserCoin = "NoticeUserCoin",// 通知用户金币变化

    MsgTypeMoveBlock = "MoveBlock",// 移动块
    MsgTypeScoreChg = "ScoreChg",// 更新积分


    /*=================================================================*/
    DeployShips = "DeployShips",// 船只数据
    NoticePlayerReady = "NoticePlayerReady",// 通知玩家已就绪
    BattleStart = "BattleStart",// 战斗开始
    Attack = "Attack",// 攻击位置

    NoticeAttack="NoticeAttack",// 攻击结果

    NoticeSkinChange="NoticeSkinChange",// 皮肤切换通知

    // 托管相关消息
    CancelAutoManaged="CancelAutoManaged",// 取消托管
    NoticeAutoManagedChange="NoticeAutoManagedChange",// 托管状态变更广播

}