export namespace IHttpMsgBody
{
    export interface IResBase
    {
        code: number,
        docs: string,
    }

    export interface IResGetGameAddress extends IResBase
    {
        data: IGetAddressData
    }

    interface IGetAddressData 
    {
        http_addr: string,
        ws_addr: string,
    }

    export interface IReqGetGameAddress
    {
        user_id: string,
        app_id: string,
        app_channel: string,
        game_id: string,
        game_mode: string,
        room_id: string
    }

    export interface IReqUpdateTime extends IReqGetGameAddress
    {
        curr_http_addr: string,
        curr_ws_addr: string
    }
}