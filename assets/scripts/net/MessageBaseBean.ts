//发消息的对象
export interface SendMessageBean {
    msgId: string;
    data:  any;
}

//收消息的对象
export interface ReceivedMessageBean {
    msgId: string;
    code:  number;
    msg:   string;
    data:  any;
}



//程序内部的通知
export interface AutoMessageBean {
    msgId: string;
    data:  any;
}

export enum AutoMessageId {

    HttpSucceed = 'httpSucceed',//短链链接成功
    ReconnectionFailureMsg = 'reconnectionFailure',//长链接重连失败
    LinkExceptionMsg = 'LinkExceptionMsg',//长链接异常
    GameRouteNotFoundMsg = 'gameRouteNotFoundMsg',//游戏线路异常的通知
    JumpHallPage = 'jumpHallPage', //跳转进大厅页面





    SwitchGameSceneMsg = 'switchGameScene',//切换游戏场景
    WalletUpdateMsg = 'walletUpdateMsg',//更新金豆余额的通知
    ServerCodeUpdateMsg = 'serverCodeUpdateMsg',//更新 code 的通知
    
    LoginSuccessfulMsg = 'LoginSuccessfulMsg',//登录成功
    FailedToDeductGoldCoins = 'Failed to deduct gold coins',//扣除金币失败

    isReady = 'isReady',//是否准备好

    BattleStart = 'BattleStart',//开始游戏

    GameStart = 'GameStart',//游戏开始

    NoticeAttack = 'NoticeAttack',//攻击结果

    NoticeSkinChange = 'NoticeSkinChange',//皮肤变更通知

    // 托管相关自动消息
    NoticeAutoManagedChange = 'NoticeAutoManagedChange',//托管状态变更通知

}

