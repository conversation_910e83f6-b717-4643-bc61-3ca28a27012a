"use strict";var e,t=function(){return t=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},t.apply(this,arguments)};function n(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}
/*!
 * Yoda.js v0.3.10
 * (c) 2021-present By <PERSON><PERSON><PERSON>
 * Released under the ISC License.
 */var r=new Promise((function(t){e=t}));function o(e){return e&&r.then(e),r}function i(e){var t=this;if(!(e&&e.namespace&&e.name))throw new Error("register 参数： {namespace, name}");var n=this[e.namespace];n||(n=this[e.namespace]={}),n[e.name]=function(n,r,o){n=n||{},o||"function"!=typeof r||(o=r,r=null),r=r||{},o=o||function(){},e.before&&(n=e.before(n));var i,a,s=t.genCallbackId();if(e.multiCallback||r.multiCallback?a=o:((i=new Promise((function(e){return a=e}))).then((function(){delete t._callbacks[s]})),i.then(o)),e.after){var c=a;a=function(t){return c(e.after(t))}}t._callbacks[s]=a;try{t.invoke({namespace:e.namespace,name:e.name,params:n,callbackId:s})}catch(e){a({result:125014,message:e.message})}return i}}function a(e){var t=e.namespace,n=e.name,r=e.params,o=e.callbackId;window.__yodaBridge__.invoke(t,n,JSON.stringify(r),""+o)}
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)};function c(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function l(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var u,d=[{namespace:"system",name:"getDeviceInfo"},{namespace:"system",name:"getAppInfo"},{namespace:"system",name:"getNetworkType"},{namespace:"webview",name:"open"},{namespace:"webview",name:"getLaunchParams"},{namespace:"webview",name:"close"},{namespace:"webview",name:"backOrClose"},{namespace:"tool",name:"canIUse"},{namespace:"tool",name:"checkAppInstalled"},{namespace:"tool",name:"launchApp"},{namespace:"ui",name:"setTopBarStyle"},{namespace:"ui",name:"setTitle"},{namespace:"ui",name:"setTopBarButton"},{namespace:"ui",name:"removeTopBarButton"},{namespace:"ui",name:"setSlideBackBehavior"},{namespace:"ui",name:"setPhysicalBackButtonBehavior"},{namespace:"ui",name:"showLoading"},{namespace:"ui",name:"hideLoading"},{namespace:"ui",name:"showToast"},{namespace:"ui",name:"showDialog"},{namespace:"event",name:"addEventListener"},{namespace:"event",name:"removeEventListener"},{namespace:"event",name:"dispatchEvent"}],p=navigator.userAgent.indexOf(" Android ")>-1,f={"tool.checkAppInstalled":{before:function(e){return{identifier:e[p?"android":"ios"]}}}};function h(e,t,n){return c(this,void 0,void 0,(function(){var r;return l(this,(function(o){switch(o.label){case 0:return[4,e.tool.canIUse({namespace:t,name:n})];case 1:return[2,1===(r=o.sent()).result&&r.canUse]}}))}))}!function(e){e.x="x",e.y="y",e.z="z"}(u||(u={}));var v,g,m="native_yoda_shaked",y={level:2,times:3,intervalTime:1300,iOSIntervalTime:500,androidIntervalTime:1300,isEmitImmediately:!0},_={1:{basic:13,total:450},2:{basic:16,total:700},3:{basic:20,total:1100}},w={1:{basic:9,total:220},2:{basic:12,total:400},3:{basic:14,total:500}},b=function(){function e(e){var t=this;this.shakeConfig={},this.shakedArr=[],this.isChecking=!1,this.nowStatus={x:0,y:0,z:0},this.lastStatus={x:0,y:0,z:0},this.checkIsShakeStoped=!1,this.notEmitButShaked=!0,this.isShakeStarted=!1,this.isAndroid=!1,this.isArrivedConfigStandard=!1,this.levelMap=_,this.dispatchEvent=null,this.setStillShaking=function(e,t){var n,r=this;return void 0===t&&(t=300),function(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];clearTimeout(n),n=setTimeout((function(){e.apply(r,o)}),t)}}((function(){return t.notShaking()}),1e3),this.dispatchEvent=e.dispatchEvent,this.isAndroid=e.isAndroid,this.levelMap=this.isAndroid?_:w}return e.prototype.setOptions=function(e){e?(this.shakeConfig=s(s({},y),e),this.shakeConfig.times=this.isAndroid?this.shakeConfig.times-1:2*(this.shakeConfig.times-1),this.shakeConfig.intervalTime=this.isAndroid?this.shakeConfig.androidIntervalTime:this.shakeConfig.iOSIntervalTime):this.shakeConfig=s({},y)},e.prototype.resetShakeStatus=function(e){this.shakedArr=[],this.isChecking=!1,this.notEmitButShaked=!1,e&&(this.isArrivedConfigStandard=!1,this.isShakeStarted=!1)},e.prototype.checkIsShake=function(e){var t=this;if(this.isChecking=!0,Object.keys(this.nowStatus).forEach((function(n){return t.nowStatus[n]=9.8*e[n]})),this.judgeArrivedTerminal())return this.lastStatus={x:0,y:0,z:0},this.handleShakeHappend(),this.setStillShaking(),void(this.isChecking=!1);this.judgeLikelyShake()?this.handleArrivedStandard():this.isChecking=!1},e.prototype.judgeLikelyShake=function(){var e=this.nowStatus,t=e.x,n=e.y,r=e.z,o=this.levelMap[this.shakeConfig.level],i=o.basic,a=o.total;return t>i||n>i||r>i||t*t+n*n+r*r>a},e.prototype.judgeArrivedTerminal=function(){var e=this,t=0,n=0,r=u.x;return Object.keys(this.nowStatus).forEach((function(o){n=Math.abs(e.lastStatus[o]-e.nowStatus[o]),t<n&&(r=o,t=n)})),this.lastStatus[r]*this.nowStatus[r]<0},e.prototype.handleArrivedStandard=function(){this.lastStatus=s({},this.nowStatus),this.checkIsShakeStoped=!1,this.setStillShaking()},e.prototype.handleShakeHappend=function(){return c(this,void 0,void 0,(function(){return l(this,(function(e){switch(e.label){case 0:return this.isShakeStarted?[3,2]:[4,this.dispatchEvent({type:"native_yoda_shake_start",data:{}})];case 1:e.sent(),e.label=2;case 2:return this.isShakeStarted=!0,console.log("!!!handleShakeHappend!!!!!!!!!!!!!!!"),this.updateShakedArr((new Date).getTime()),[2]}}))}))},e.prototype.notShaking=function(){console.log("notShaking!!!!!"),this.checkIsShakeStoped=!0,this.checkDoDelayEmit()},e.prototype.updateShakedArr=function(e){var t=this.shakeConfig.intervalTime;this.shakedArr.length>0?e-this.shakedArr[this.shakedArr.length-1]<=t?this.shakedArr.push(e):(this.shakedArr=[],this.isChecking=!1):this.shakedArr.push(e),console.log(this.shakedArr.length,this.shakeConfig.times),this.shakedArr.length>=this.shakeConfig.times?this.handleArrivedEmitStandard():this.isChecking=!1},e.prototype.handleArrivedEmitStandard=function(){this.isArrivedConfigStandard=!0,this.resetShakeStatus(),this.shakeConfig.isEmitImmediately?this.dispatchEvent({type:m,data:{}}):this.notEmitButShaked=!0},e.prototype.checkDoDelayEmit=function(){this.notEmitButShaked&&this.checkIsShakeStoped&&!this.shakeConfig.isEmitImmediately&&this.dispatchEvent({type:m,data:{}}),this.isShakeStarted&&this.dispatchEvent({type:"native_yoda_shake_end",data:{isArrivedConfigStandard:this.isArrivedConfigStandard}}),this.resetShakeStatus(!0)},e}(),E=null,k=null;function S(e){return c(this,void 0,void 0,(function(){var t,n;return l(this,(function(r){switch(r.label){case 0:return[4,e.system.startAccelerometer({interval:60})];case 1:return t=r.sent(),k=E.checkIsShake.bind(E),[4,e.event.addEventListener("accelerometer-change",k)];case 2:if(n=r.sent(),1===t.result&&1!==n.result&&O(e),1!==t.result||1!==n.result)throw new Error("摇一摇初始化失败，请稍后重试。AccelerometerRes:"+JSON.stringify(t)+";eventRes:"+JSON.stringify(n));return[2]}}))}))}function O(e){return c(this,void 0,void 0,(function(){var t,n;return l(this,(function(r){switch(r.label){case 0:return[4,h(e,"system","stopAccelerometer")];case 1:if(!r.sent())throw new Error("当前APP暂不支持摇一摇");return E=null,[4,e.event.removeEventListener("accelerometer-change",k)];case 2:return t=r.sent(),[4,e.system.stopAccelerometer()];case 3:if(n=r.sent(),1!==t.result||1!==n.result)throw new Error("摇一摇停止监听失败。stopRes:"+JSON.stringify(n)+";eventRes:"+JSON.stringify(t));return[2]}}))}))}var P=m,A=((v={})[P]=function(e,t){return c(this,void 0,void 0,(function(){return l(this,(function(n){switch(n.label){case 0:return[4,h(t,"system","startAccelerometer")];case 1:if(!n.sent())throw new Error("当前APP暂不支持摇一摇");if(E)throw new Error("当前存在尚未结束监听的摇一摇");return E=new b({dispatchEvent:t.event.dispatchEvent,isAndroid:"android"==(r=navigator.userAgent,void 0===r&&(r=""),r.includes("Android")||r.includes("Adr")?"android":r.includes("iPhone")||r.includes("iPad")?"ios":"else")}),function(e){if(e){var t=e.level,n=e.times;e.intervalTime;var r,o=e.isEmitImmediately;if(void 0!==t&&!_[t])throw new Error("请传入正确的level，可选值为1、2、3");if(void 0!==n){if("number"!=typeof(r=n)||r%1!=0)throw new Error("times应为整数");if(n<1)throw new Error("times应大于1")}if(void 0!==o&&"boolean"!=typeof o)throw new Error("isEmitImmediately应为boolean类型")}}(e),E.setOptions(e),E.resetShakeStatus(),[4,S(t)];case 2:return n.sent(),[2]}var r}))}))},v),L=((g={})[P]=O,g);function C(e){function t(t,n,r){if(n.__yodaCallbackId__=n.__yodaCallbackId__||{},n.__yodaCallbackId__[t])throw new Error("addEventListener "+t+" 的 listener 已经被绑定过了");var o=e.genCallbackId();e._callbacks[o]=n;var i=e._eventListeners[t];return i||(i=e._eventListeners[t]={}),i[o]=!0,n.__yodaCallbackId__[t]=o,e.event._addEventListener({type:t,listener:o},r)}function n(t,n,r){n.__yodaCallbackId__&&(delete n.__yodaCallbackId__[t],function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}(n.__yodaCallbackId__)&&(delete n.__yodaCallbackId__,delete e._callbacks[r]))}return{addEventListenerHandler:function(n,r,o){var i;return c(this,void 0,void 0,(function(){var a,s;return l(this,(function(c){switch(c.label){case 0:a="string"==typeof n?n:n.type,s="string"==typeof n?{}:n.options,c.label=1;case 1:return c.trys.push([1,3,,4]),[4,null===(i=A[a])||void 0===i?void 0:i.call(A,s,e)];case 2:return c.sent(),[3,4];case 3:return[2,{result:-1,message:c.sent().message}];case 4:return[2,t(a,r,o)]}}))}))},removeEventListener:function(t,r,o){var i;return c(this,void 0,void 0,(function(){var a,s,c,u;return l(this,(function(l){switch(l.label){case 0:if(s=e._eventListeners[t],!r)return[3,5];l.label=1;case 1:return l.trys.push([1,3,,4]),[4,null===(i=L[t])||void 0===i?void 0:i.call(L,e)];case 2:return l.sent(),[3,4];case 3:return[2,{result:-1,message:l.sent().message}];case 4:if(!((a=r.__yodaCallbackId__&&r.__yodaCallbackId__[t])&&s&&s[a]))return c=Promise.resolve({result:1}),o&&c.then(o),[2,c];l.label=5;case 5:return(u=e.event._removeEventListener({type:t,listener:a},o)).then((function(o){if(1===o.result)if(r)delete s[a],n.call(e,t,r,a);else if(s)for(var i in delete e._eventListeners[t],s)n.call(e,t,e._callbacks[i],i)})),[2,u]}}))}))}}}var T=navigator.userAgent,I=document.cookie;function R(e){var t={};return T.split(" ").forEach((function(e){var n=e.split("/");t[n[0]]=n[1]})),t[e]?t[e]:t}function N(){if(-1!==T.indexOf("Android"))return"WebView";if(-1!==T.indexOf("iPhone")){var e=R();return e.Yoda&&"WK"===e.WebViewType?"YodaWKWebView":e.Yoda||"WKWebView"!==e.WebView?"UIWebView":"WKWebView"}return""}function U(e){var t=I.match(new RegExp("(^| )"+e+"=([^;]+)"));return t?t[2]:""}function D(e,t,n){void 0===t&&(t={}),void 0===n&&(n=0);var r=this,o=(new Date).getTime()-n;r.ready((function(){r.tool.sendRadarLog&&r.tool.sendRadarLog({removeStashedLog:[],sendImmediate:!0,customData:{project_id:e||"yoda_kpn_"+U("kpn"),url_package:{page_type:2,page:location.href,params:"",identity:U("did")},data:[{key:"event",value:{duration:n&&o},dimension:s({yoda_version:R("Yoda")||"",webview_type:N()},t)}]}})}))}function x(){var e=this,t=this,n=function(){var n=Date.now();t.register({namespace:"tool",name:"getApiList"}),t.tool.getApiList({},(function(r){var o=r.result,i=r.apiList;1!==o&&(i=d);for(var a=i.length;a--;){var c=i[a],l=f[c.namespace+"."+c.name];l&&(c=s(s({},c),l)),1===o&&(e._apisMap={result:o,apiList:i}),t.register(c)}!function(e){var t=e.event,n=C(e);t&&t.addEventListener&&(t._addEventListener=t.addEventListener,t.addEventListener=n.addEventListenerHandler),t&&t.removeEventListener&&(t._removeEventListener=t.removeEventListener,t.removeEventListener=n.removeEventListener)}(t),D.call(t,"2fa790a375",{name:"yoda_auto_register",category:"yoda_js_sdk",result_type:o,extra_info:JSON.stringify({res:e._apisMap,apiLength:i.length})},n),t._readyResolve()}))};window.__yodaBridge__?n():window.__yodaBridgeReady__=n}function j(e){var t=e.id,n=e.params,r=this._callbacks[t];r&&r(n)}var M=Number.MAX_SAFE_INTEGER||9007199254740991,H=function(){function t(){this.isInYoda=!!window.__yodaBridge__,this._instanceId=this.genInstanceId(),this._callbackId=1,this._callbacks={},this._eventListeners={},this._apisMap={},this._readyPromise=r,this._readyResolve=e,this.ready=o,this._plugins=[],this.version="0.3.10-alpha.2",this.overflow=!1,this.plugin=function(e,t){e._plugins=[];var n=function(n){var r=t[n],o=n[0].toUpperCase()+n.slice(1),i="before"+o,a="after"+o;e[n]=function(t){var n=e._plugins,o=n.length,s=-1;!function c(){for(var l=function(){var r=n[s][i];if("function"==typeof r){var o=!1;return r.call(e,t,(function(){o||(o=!0,c())})),{value:void 0}}};++s<o;){var u=l();if("object"==typeof u)return u.value}r.call(e,t);for(var d=0;d<o;d++){var p=n[d][a];"function"==typeof p&&p.call(e,t)}}()}};for(var r in t)n(r);return function(t){"function"==typeof t&&(t=new t(e)),e._plugins.push(t)}}(this,{register:i,invoke:a,callbackHandler:j,init:x})}return t.prototype.genCallbackId=function(){var e=this.genNextId();return e>=M?(this.overflow=!0,this._callbackId=1,this.genCallbackId()):""+e},t.prototype.genNextId=function(){var e=this._callbackId++,t=this._instanceId+e;return this.overflow&&this._callbacks[t]?this.genNextId():t},t.prototype.genInstanceId=function(){var e=""+M,t=Number(e.slice(0,3)),n=Math.floor(Math.random()*(t+1)),r=""+Date.now(),o=r.slice(r.length-9),i=new Array(e.length-12).fill(0).join("");return Number(""+n+o+i)},t}(),G=new H;window.__yodaBridgeCallback__=function(e,t){G.callbackHandler({id:e,params:t})},G.plugin({beforeInvoke:function(e,t){var n=e.namespace,r=e.name,o=e.params,i=e.callbackId;if("tool"===n&&0!==Object.keys(this._apisMap).length){if("getApiList"===r)return void window.__yodaBridgeCallback__(i,this._apisMap);if("canIUse"===r){var a=this._apisMap.apiList,s=o.namespace,c=o.name,l=a.some((function(e){var t=e.namespace,n=e.name;return t===s&&n===c}));return void window.__yodaBridgeCallback__(i,{result:1,canUse:l})}}t()}}),setTimeout((function(){return G.init()}));var B=/yoda/i.test(navigator.userAgent);var W=B?G:function e(t){return new Proxy(t,{get:function(){return e((function(){}))},apply:function(){return Promise.resolve({})}})}({});var K="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function V(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function F(e,t){return e(t={exports:{}},t.exports),t.exports}var J=F((function(e,t){!function(e,t){function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r,o,i,a=n(t),s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==K?K:"undefined"!=typeof self?self:{};!function(){void 0===s.window&&(s.window={addEventListener:function(){},removeEventListener:function(){},setTimeout:function(){},setInterval:function(){},_WEBLOGGER_MOCKED_WINDOW_:!0,global:s}),void 0===s.document&&(s.document=s.window.document||{cookie:"",addEventListener:function(){},removeEventListener:function(){},querySelector:function(){},querySelectorAll:function(){}},void 0===s.window.document&&(s.window.document=s.document)),void 0===s.navigator&&(s.navigator=s.window.navigator||{userAgent:""},void 0===s.window.navigator&&(s.window.navigator=s.navigator)),void 0===s.screen&&(s.screen=s.window.screen||{},void 0===s.window.screen&&(s.window.screen=s.screen)),void 0===s.history&&(s.history=s.window.history||{},void 0===s.window.history&&(s.window.history=s.history)),void 0===s.location&&(s.location=s.window.location||{hostname:"",search:"",href:"",origin:""},void 0===s.window.location&&(s.window.location=s.location));try{void 0===s.localStorage&&(s.localStorage=s.window.localStorage||{getItem:function(){},setItem:function(){}},void 0===s.window.localStorage&&(s.window.localStorage=s.localStorage))}catch(e){}}(),Object.defineProperty||(Object.defineProperty=function(e,t,n){e[t]="[object Object]"===Object.prototype.toString.call(n)&&n.hasOwnProperty("value")?n.value:n}),Object.assign||Object.defineProperty(Object,"assign",{value:function(e){if(null===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var r=arguments[n];if(null!==r)for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},writable:!0,configurable:!0}),Function.prototype.bind||(Function.prototype.bind=function(e){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var t=Array.prototype.slice.call(arguments,1),n=this,r=function(){},o=function(){var o=this instanceof r?this:e;return n.apply(o,t.concat(Array.prototype.slice.call(arguments)))};return this.prototype&&(r.prototype=this.prototype),o.prototype=new r,o}),Array.prototype.indexOf||(Array.prototype.indexOf=function(e,t){var n;if(null==this)throw new TypeError('"this" is null or not defined');var r=Object(this),o=r.length>>>0;if(0===o)return-1;var i=+t||0;if(Math.abs(i)===1/0&&(i=0),i>=o)return-1;for(n=Math.max(i>=0?i:o-Math.abs(i),0);n<o;){if(n in r&&r[n]===e)return n;n++}return-1}),Object.keys||(Object.keys=(r=Object.prototype.hasOwnProperty,o=!{toString:null}.propertyIsEnumerable("toString"),i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],function(e){if("object"!=typeof e&&"function"!=typeof e||null===e)throw new TypeError("Object.keys called on non-object");var t=[];for(var n in e)r.call(e,n)&&t.push(n);if(o)for(var a=0,s=i;a<s.length;a++){var c=s[a];r.call(e,c)&&t.push(c)}return t})),Array.prototype.forEach||(Array.prototype.forEach=function(e,t){var n,r;if(null==this)throw new TypeError(" this is null or not defined");var o=Object(this),i=o.length>>>0;if("function"!=typeof e)throw new TypeError(e+" is not a function");for(arguments.length>1&&(n=t),r=0;r<i;){var a=void 0;r in o&&(a=o[r],e.call(n,a,r,o)),r++}}
    /*! *****************************************************************************
        Copyright (c) Microsoft Corporation.

        Permission to use, copy, modify, and/or distribute this software for any
        purpose with or without fee is hereby granted.

        THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
        REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
        AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
        INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
        LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
        OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
        PERFORMANCE OF THIS SOFTWARE.
        ***************************************************************************** */);var c=function(e,t){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function l(e,t){function n(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var u=function(){return(u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function d(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function p(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function f(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function h(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,o++)r[o]=i[a];return r}var v,g,m={unknown:0,none:1,wifi:2,"4g":3,"3g":4,"2g":5,"5g":7,"slow-2g":5};!function(e){e.CLICK="CLICK",e.DOUBLE_CLICK="DOUBLE_CLICK",e.TRIPLE_CLICK="TRIPLE_CLICK",e.LONG_PRESS="LONG_PRESS",e.PULL="PULL",e.DRAG="DRAG",e.SCALE="SCALE",e.PULL_DOWN="PULL_DOWN",e.PULL_UP="PULL_UP",e.AUTO="AUTO"}(v||(v={})),function(e){e.PV="PV",e.SHOW="SHOW",e.VIDEO="VIDEO",e.CUSTOM="CUSTOM",e.RADAR="RADAR"}(g||(g={}));var y={sessionId:"",appDevicePackageReady:!1,identityPackageReady:!1},_=function(){function e(e,t){switch(this.page="",this.identity="",t){case"web":this.page_type=2;break;case"native":this.page_type=1;break;case"mina":this.page_type=3;break;default:this.page_type=0}this.update(e.page,e.params)}return e.prototype.update=function(e,t){void 0===e&&(e=""),e&&e!==this.page&&(this.page=e,this.identity=this.generatePageId(e)),t&&(this.params=Object.assign(this.params||{},t))},e.prototype.toJSON=function(){return{page:this.page,identity:this.identity,page_type:this.page_type,params:JSON.stringify(this.params)}},e.prototype.generatePageId=function(e){return e+(new Date).getTime()},e}(),w=["Kwai","Kwai_Lite","Kwai_Pro","livemate","ksthanos","ksNebula","ksnebula","kwaiying","pearl","kinder","m2u","LOLita","XFunCore","ACVideoCore","ZIKZAK"].concat(["UVideo","Vstatus","Kwaigo","MvMaster"]);function b(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.error;return n&&n.call.apply(n,h([console],e))}catch(e){return}}function E(e,t,n,r){return"attachEvent"in e?e.attachEvent("on"+t,n):e.addEventListener(t,n,r)}function k(e,t,n,r){return"attachEvent"in e?e.detachEvent("on"+t,n):e.removeEventListener(t,n,r)}function S(e,t,n){if(void 0===n&&(n=" "),(t-=e.length)<=0)return e;for(var r="";t;)1&t&&(r+=n),t>>=1,n+=n;return r+e}var O=function(){var e=null;return function(){try{if(null===e)for(var t=window.navigator.userAgent.toLowerCase(),n=0;n<w.length;n++)if(t.indexOf(w[n].toLowerCase())>-1)return e=!0}catch(e){return!1}}}();function P(e){for(var t={},n=0,r=e.split("&");n<r.length;n++){var o=r[n].split("="),i=o[0],a=o[1];i in t?t[i]instanceof Array?t[i].push(a):t[i]=[t[i],a]:t[i]=a}return t}function A(e){void 0===e&&(e=location.href);var t=e.lastIndexOf("?");return-1===t?{page:e}:{page:e.slice(0,t),params:P(e.slice(t+1))}}var L=function(e){return e&&"object"==typeof e&&!Array.isArray(e)};function C(e){return!e||!/^(https?:)?\/\//.test(e)||(b("[error 108]","请注意当前埋点页面信息为 "+e+"，不符合规范，上报失败！"),!1)}var T,I=(T="",function(){if(T)return T;try{var e=window.devicePixelRatio||1,t=Math.floor(screen.width*e),n=Math.floor(screen.height*e);return T=t+"x"+n}catch(e){return""}}),R=function(){return!(!((null===window||void 0===window?void 0:window.Worker)&&(null===window||void 0===window?void 0:window.Uint8Array)&&window.URL)||function(){var e,t,n=(null===(e=null===window||void 0===window?void 0:window.navigator)||void 0===e?void 0:e.userAgent)||"",r=/mobile|tablet|ip(ad|hone|od)|android|(windows phone)/i.test(n),o="MacIntel"===(null===window||void 0===window?void 0:window.navigator.platform)&&(null===(t=null===window||void 0===window?void 0:window.navigator)||void 0===t?void 0:t.maxTouchPoints)>1;return r||o}())},N=function(e){try{return Math.abs(Math.floor(e))}catch(t){return e}},U=function(){function e(){this.events={}}return e.prototype.on=function(e,t){t&&"function"==typeof t&&(this.events[e]=this.events[e]||[],this.events[e].push(t))},e.prototype.off=function(e,t){var n,r,o;this.events[e]&&(t&&"function"==typeof t&&(r=t,(o=(n=this.events[e]).indexOf(r))>-1&&n.splice(o,1)),t||(this.events[e]=[]))},e.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.events[e]&&this.events[e].forEach((function(e){e.apply(void 0,t)}))},e}(),D=0,x=function(e){function t(n,r){var o,i=e.call(this)||this;return i.version="3.9.49",i.plugins={},i.flush=function(){i.logger.flush()},i.baseOption=u({},r),i.logConfig=(o=n,u({env:"production",proto:"v3",timeout:3e4,wait:1e3,maxBatchLength:50,sampleRate:1,yoda:"undefined"!=typeof window&&window.yoda,forbidV2HttpUrlPage:!0},o||{})),i.initUrlPackage(),t.__instance||(t.__instance=i),i}return l(t,e),Object.defineProperty(t.prototype,"sessionId",{get:function(){return y.sessionId},enumerable:!1,configurable:!0}),t.prototype.initUrlPackage=function(){this.updateCurrentUrlPackage()},t.prototype.updateCurrentUrlPackage=function(e,t){if(void 0===e&&(e={}),void 0===t&&(t="web"),"object"==typeof e&&e.page&&this.currentUrlPackage&&e.page===this.currentUrlPackage.page)return this.currentUrlPackage.update(e.page,e.params);this.currentUrlPackage=new _(e,t)},t.prototype.updateReferUrlPackage=function(e,t){void 0===e&&(e=this.currentUrlPackage),void 0===t&&(t="web"),this.referUrlPackage=e instanceof _?e:new _(e,t)},t.prototype.updateBase=function(e){this.updateCommonPackage(e)},t.prototype.updateCommonPackage=function(e){"object"==typeof e&&this.commonPackage.update(e)},t.prototype.addPlugins=function(){var e=this;this.logConfig.plugins&&this.logConfig.plugins.length&&this.logConfig.plugins.forEach((function(t){"object"==typeof t&&"function"==typeof t.apply&&e.addPluginInstance(t)}))},t.prototype.addPluginInstance=function(e){if(e){var t=e.key||e.constructor&&e.constructor.key||"plugin_auto_key_"+D++;"function"!=typeof e.apply||e.weblog&&e.weblog===this||e.apply(this),this.plugins[t]=e}},t.prototype.plug=function(e,t){if(this.plugins[e.key])return b("[code 301]",e.key+" 插件重复加载！");this.addPluginInstance(new e(this,t))},t.prototype.unplug=function(e){var t=this.plugins[e];t&&(t.destroy(),delete this.plugins[e])},t.prototype.unplugAll=function(){for(var e in this.plugins)this.plugins[e]&&this.unplug(e)},t.prototype.generateLog=function(e,t){return{}},t.prototype.send=function(e,t,n){void 0===e&&(e=v.CLICK);var r=this.generateLog(e.toUpperCase(),t),o="object"==typeof t&&t.callback||void 0;return"function"==typeof this.beforeSend&&this.beforeSend(e,t,r),this.logger.send(r,!!n,o)},t.prototype.collect=function(e,t){this.send(e,t)},t.prototype.sendImmediately=function(e,t){this.send(e,t,!0)},t.prototype.sendPackage=function(e,t){this.logger.sendPackage(e,t)},t.prototype.destroy=function(){this.unplugAll()},t}(U);function j(e,t,n){var r={};if(e.length>0)for(var o=t?decodeURIComponent:function(e){return e},i=e.split(/;\s/g),a=null,s=null,c=null,l=0,u=i.length;l<u;l++){if(null!==(c=i[l].match(/([^=]+)=/i)))try{a=decodeURIComponent(c[1]),s=o(i[l].substring(c[1].length+1))}catch(e){}else a=decodeURIComponent(i[l]),s="";null!==a&&(r[a]=s)}return r}var M,H={};function G(e,t,n){void 0===t&&(t={}),void 0===n&&(n=!1);try{if(!n&&e in H)return H[e];if(!(null===window||void 0===window?void 0:window.document))return;var r=document.cookie||"";return r===M?H[e]:(M=r,(H=j(r,!t.raw))[e])}catch(e){}}function B(e,t,n){void 0===n&&(n={});try{document.cookie=function(e,t,n,r){void 0===r&&(r={});var o=encodeURIComponent(e)+"="+(n?encodeURIComponent(t):t),i=r.expires,a=r.path||"/",s=r.domain||"";return i instanceof Date&&(o+="; expires="+i.toUTCString()),"number"==typeof i&&(o+="; max-age=="+i),""!==a&&(o+="; path="+a),""!==s&&(o+="; domain="+s),!0===r.secure&&(o+="; secure"),o}(e,t,!n.raw,n),H[e]=t}catch(e){}}var W={getCookie:G,setCookie:B,parseCookieString:j};function V(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}function F(e){if(void 0===e&&(e="undefined"!=typeof location?location.hostname:""),!e)return"";var t=e.split("."),n=t.length;return n<=2?"":"."+t[n-2]+"."+t[n-1]}var J="";function z(){if(J)return J;try{if(!(J=G("did")||G("_did"))){J="web_"+function(){for(var e=1e9*Math.random()>>>0,t=[],n=0;n<7;n++)t.push("0123456789ABCDEF".charAt(16*Math.random()));return e+t.join("")}();var e=new Date;e.setFullYear(e.getFullYear()+1),B("_did",J,{expires:e,domain:F(),path:"/"})}return J}catch(e){return""}}var q,Y,Q,X=function(e){function t(t,n,r){var o=e.call(this,{},n)||this;if("string"==typeof t){var i=A(t);o.page=i.page,o.params=i.params}else o.page=(t||{}).page||"",o.params=(t||{}).params||void 0;return o.identity=o.generatePageId(),r&&"string"==typeof t&&o.init(t,r),o}return l(t,e),t.prototype.init=function(e,t){void 0===t&&(t={});var n=function(e,t){var n=e.url,r=e.page,o=e.params,i=e.pageId;if(!r&&"string"==typeof n){var a=A(n);r=a.page,o=a.params}if("function"==typeof t)try{var s=t({url:n,page:r,params:o});"string"==typeof s?r=s:"object"==typeof s&&(s.page&&(r=s.page),s.params&&(o=s.params),s.pageId&&(i=s.pageId))}catch(e){}else if("object"==typeof t){var c="";for(var l in t)if((n||r||"").indexOf(l)>-1){c=t[l];break}c&&(r=c)}return{page:r,params:o,pageId:i}}({url:e,page:this.page,params:this.params},t),r=n.page,o=n.params;this.update(r,o)},t.prototype.attachUrl=function(){if(this.params||(this.params={}),null===location||void 0===location?void 0:location.href){this.params.origin_url=this.params.origin_url||(null===location||void 0===location?void 0:location.href);var e=A(location.href).page,t=void 0===e?"":e;this.params.origin_pathname=t}},t.prototype.getRealUrlPackage=function(){var e=A(location.href),t=e.page,n=e.params;return{page:t,params:JSON.stringify(u({page_code:this.page,url:location.href,query:n},n)),page_type:this.page_type,identity:this.identity}},t.prototype.generatePageId=function(){return V()},t}(_),$=function(){function e(e){if(this.identity_package={device_id:void 0,global_id:void 0,user_id:void 0,union_id:void 0,open_id:void 0,iu_id:void 0},this.app_package={product:void 0,language:void 0,platform:void 0,container:"H5",package_name:void 0,product_name:void 0,version_name:void 0,channel:void 0,version_code:void 0},this.experiment=void 0,this.service_name=void 0,this.safety_id=void 0,this.sub_biz=void 0,this.device_package={os_version:void 0,model:void 0,ua:void 0},this.need_encrypt=!1,this.network_package={type:m.unknown},this.h5_extra_attr={sdk_name:"webLogger",sdk_version:"3.9.49",sdk_bundle:"log.hybrid.js"},this.global_attr={entry_tag:[]},this.update(e),this.app_package.version_name){var t=this.app_package.version_name.lastIndexOf(".");this.app_package.version_code=+this.app_package.version_name.slice(t+1)||0}this.app_package.version_name||(this.app_package.version_name=void 0),this.app_package.version_code||(this.app_package.version_code=void 0)}return e.prototype.getH5ExtraAttr=function(e){return Object.assign({},this.h5_extra_attr,e)},e.prototype.update=function(e){if("object"==typeof e){!function(e,t){if(L(e)&&L(t)){var n=function(e,t,n){L(e[n])&&L(t[n])?Object.assign(e[n],t[n]):L(e[n])||L(t[n])||(e[n]=t[n])};for(var r in t)if(e.hasOwnProperty(r))n(e,t,r);else for(var o in e)L(e[o])&&e[o].hasOwnProperty(r)&&n(e[o],t,r)}}(this,e);var t=e.network_type;t&&m[t]&&(this.network_package.type=m[t])}},e.prototype.updateGlobalAttr=function(e){Object.assign(this.global_attr||{},e)},e.prototype.toJSON=function(){this.identity_package.user_id||(this.identity_package.user_id=void 0);var e=u(u({},this),{toJSON:function(){return u(u({},e),{h5_extra_attr:JSON.stringify(e.h5_extra_attr),global_attr:JSON.stringify(e.global_attr)})}});return e.global_attr=u({},this.global_attr),this.global_attr.entry_tag&&this.global_attr.entry_tag.length?e.global_attr.entry_tag=this.global_attr.entry_tag.slice():delete e.global_attr.entry_tag,Object.keys(this.global_attr).length||delete e.global_attr,e},e}(),Z="https://data-track.corp.kuaishou.com",ee=Z+"/",te="production",ne="logger",re="logger-oversea",oe="rest/wd/common/log/collect/",ie=[te,"test","development",ne,"oversea"],ae="https://logsdk.kwai-pro.com/",se={v2:"rest/kd/log/collect?_json=1&biz=",v3:oe+"misc2",radar:oe+"radar"},ce={v2:{production:["https://wlog.ksapisrv.com/","https://wlog.gifshow.com/"][Math.round(Math.random())],development:ee,test:ee,oversea:ae},v3:{production:"https://log-sdk.ksapisrv.com/",development:ee,test:ee,oversea:ae}},le=function(e,t,n){return void 0===e&&(e=te),void 0===t&&(t=!1),void 0===n&&(n="v3"),-1===ie.indexOf(e)?e+se[n]:t&&ce.v3[e]?ce.v3[e]+se.radar:ce[n][e]&&se[n]?ce[n][e]+se[n]:ce.v3.production+se.v3},ue=function(e,t){if(!t)return e;try{return/\?(.+?)$/.test(e)?e.replace(/\?(.+?)$/,"?"+t+"&$1"):e+"?"+t}catch(t){return e}},de=function(){function e(e,t){var n=this;this.asyncQueue=[],this.throttleQueue=[],this.errorQueue=[],this.sendingQueue={},this.url="",this.isV2=!1,this.isDebug=!1,this.radarUrl="",this.drained=!1,this.batchCount=50,this.sendingYield=null,this.flush=function(e){n.sendLogs(n.throttleQueue.concat(n.asyncQueue),e),n.throttleQueue=[],n.asyncQueue=[]},this.drain=function(){n.drained=!0,n.flush(),n.flushErrorLogs(),setTimeout((function(){n.drained=!1}),1e3)},this.config=e,this.config.maxBatchLength&&this.config.maxBatchLength>1&&(this.batchCount=Math.min(50,this.config.maxBatchLength)),this.commonPackage=t,this.isDebug=this.config.logger||"logger"===this.config.env,this.isV2="v2"===this.config.proto,this.updateUrls()}return e.prototype.sendData=function(e,t){return"function"==typeof this.config.sender?this.config.sender(e,t):this.baseSendData(e,t)},e.prototype.updateUrls=function(){var e=this.config.env;e&&/^(https?:)?\/\//.test(e)?this.url=e:this.url=le(e),this.formatUrl()},e.prototype.formatUrl=function(){this.radarUrl||(this.radarUrl=this.url.replace(-1!==this.url.indexOf(se.v2)?se.v2:se.v3,se.radar),-1!==this.radarUrl.indexOf(ce.v2.production)&&this.radarUrl.replace(ce.v2.production,ce.v3.production));var e=this.commonPackage.app_package,t=e.product_name,n=e.product,r="v=3.9.49&kpn="+(t||n);this.url=ue(this.url,r),this.radarUrl=ue(this.radarUrl,r)},e.prototype.getCommonPackageJSON=function(){return this.commonPackage.toJSON()},e.prototype.send=function(e,t,n){if(void 0===t&&(t=!1),n||this.drained)return this.sendLogs([e],n);t?this.sendAsync(e,n):this.sendThrottle(e)},e.prototype.sendAsync=function(e,t){return p(this,void 0,void 0,(function(){return f(this,(function(t){switch(t.label){case 0:return this.asyncQueue.push(e),this.asyncQueue.length>=this.batchCount?(this.flush(),[2]):this.sendingYield?[4,this.sendingYield]:[3,2];case 1:t.sent(),this.sendingYield=null,t.label=2;case 2:return t.trys.push([2,4,,5]),[4,Promise.resolve()];case 3:case 4:return t.sent(),[3,5];case 5:return this.asyncQueue.length?(this.flush(),[2]):[2]}}))}))},e.prototype.sendThrottle=function(e){var t=this;this.throttleQueue.push(e),this.throttleQueue.length>=this.batchCount?this.flush():(clearTimeout(this.batchWaitTimer),this.batchWaitTimer=setTimeout((function(){t.flush()}),this.config.wait))},e.prototype.sendLogs=function(e,t){var n=this;if(!e||!e.length)return"function"==typeof t&&t();var r=this.buildLogPackage(e,this.url);this.sendPackage(r,(function(e){e?n.errHandler(r):n.flushErrorLogs(),"function"==typeof t&&t(e)}))},e.prototype.sendPackage=function(e,t){var n=this.config.timeout;try{this.sendData(u(u({},e),{isDebug:this.isDebug,timeout:n}),t)}catch(e){"function"==typeof t&&t(e)}},e.prototype.buildLogPackage=function(e,t){return this.isV2?this.buildV2Package(e,t):this.buildV3Package(e,t)},e.prototype.buildV2Package=function(e,t){return{url:t,data:{log:{event:e}}}},e.prototype.buildV3Package=function(e,t,n){var r=this.getCommonPackageJSON();return n&&Object.assign(r,n),{url:t,data:{common:r,logs:e}}},e.prototype.errHandler=function(e){var t;if(this.isV2)this.errorQueue.unshift(e);else{var n=e.data;if(n.logs.length){delete n.common.h5_extra_attr.http_seq_id,delete n.common.h5_extra_attr.client_timestamp;for(var r=!1,o=0;o<this.errorQueue.length;o++){var i=this.errorQueue[o];if(i.url===e.url&&i.data.logs.length+n.logs.length<=100&&JSON.stringify(i.data.common)===JSON.stringify(n.common)){r=!0,(t=i.data.logs).push.apply(t,n.logs);break}}r||(this.errorQueue.length>=5&&this.errorQueue.pop(),this.errorQueue.unshift(e))}}},e.prototype.flushErrorLogs=function(){var e=this;this.errorQueue.forEach((function(t){e.sendPackage(t)})),this.errorQueue=[]},e.prototype.destory=function(){this.batchWaitTimer&&clearTimeout(this.batchWaitTimer),this.compensateTimer&&clearTimeout(this.compensateTimer)},e.prototype.sendRadar=function(e,t){var n=t?{service_name:t}:void 0,r=this.buildV3Package([e],this.radarUrl||this.url,n);this.sendPackage(r)},e}(),pe=(null===(q=null===window||void 0===window?void 0:window.navigator)||void 0===q?void 0:q.userAgent)||"",fe=function(){if(Y)return Y;var e="unknow",t=/android/i,n="Mac OS",r="Windows",o="Android",i="iPhone",a="iPhone",s="Windows Phone",c="Linux";Y={os:{name:e,version:e},model:e};try{var l=pe.match(/\((.*?)\)/);if(!l)return t.test(pe)&&(Y.os.name=o),Y;var u=l[1]+")",d=u.split(";").map((function(e){return e.trim()})),p=void 0,f=void 0,h=void 0;if(pe.indexOf(s)>-1)h=p=s,(m=u.match(/Windows\sPhone\s(.*?)[;\)\s]/))&&(f=m[1]),h=d[d.length-1].replace(")","");else if(t.test(pe)){p=o;var v=u.match(/android.*?;(.*?)build\//i);v&&(h=(h=v[1].split(";").pop())&&h.trim());for(var g=0;g<d.length;g++)if(t.test(d[g])){f=d[g].replace(t,""),h||(h=d[g+1]);break}}else if(pe.indexOf(i)>-1||pe.indexOf(a)>-1)h=pe.indexOf(i)>-1?i:a,p="iOS",(m=u.match(/OS\s(.*?)\slike/))&&(f=m[1]);else if(pe.indexOf(n)>-1)h=p=n,(m=u.match(/OS\sX\s(.*?)[;\)\s]/))&&(f=m[1]);else if(pe.indexOf(r)>-1){var m;h=p=r,(m=u.match(/Windows\s(.*?)[;\)]/))&&(f=m[1])}else if(pe.indexOf("Nokia")>-1){p="Symbian";var y=pe.match(/Symbian.*?\/(.*?);/);y&&(f=y[1]);var _=pe.match(/Nokia(.*?)\//);_&&(h=_[1])}else u.indexOf(c)>-1&&(p=h=c);Y={os:{name:p||e,version:f&&f.replace(/_/g,".").trim()||e},model:h||e}}catch(e){}return Y};function he(){return void 0===Q&&(Q=O()&&G("kpn")||function(){if("undefined"==typeof window)return"";for(var e=window.navigator.userAgent,t=[[/ Kwai\//,"KUAISHOU"],[/ ksthanos\//,"THANOS"],[/ ksNebula\//i,"NEBULA"]],n=0;n<t.length;n++){var r=t[n],o=r[0],i=r[1];if(o.exec(e))return i}return""}()||""),Q}var ve={supportsPushState:function(){var e=window.navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history},getUAInfo:fe,getDefaultKpn:he},ge=[["a7","640x1136",["iPhone 5","iPhone 5s"]],["a7","1536x2048",["iPad Air","iPad Mini 2","iPad Mini 3"]],["a8","640x1136",["iPod touch (6th gen)"]],["a8","750x1334",["iPhone 6"]],["a8","1242x2208",["iPhone 6 Plus"]],["a8","1536x2048",["iPad Air 2","iPad Mini 4"]],["a9","640x1136",["iPhone SE"]],["a9","750x1334",["iPhone 6s"]],["a9","1242x2208",["iPhone 6s Plus"]],["a9x","1536x2048",["iPad Pro (1st gen 9.7-inch)"]],["a9x","2048x2732",["iPad Pro (1st gen 12.9-inch)"]],["a10","750x1334",["iPhone 7"]],["a10","1242x2208",["iPhone 7 Plus"]],["a10x","1668x2224",["iPad Pro (2th gen 10.5-inch)"]],["a10x","2048x2732",["iPad Pro (2th gen 12.9-inch)"]],["a11","750x1334",["iPhone 8"]],["a11","1242x2208",["iPhone 8 Plus"]],["a11","1125x2436",["iPhone X"]],["a12","828x1792",["iPhone Xr"]],["a12","1125x2436",["iPhone Xs"]],["a12","1242x2688",["iPhone Xs Max"]],["a12x","1668x2388",["iPad Pro (3rd gen 11-inch)"]],["a12x","2048x2732",["iPad Pro (3rd gen 12.9-inch)"]]],me=function(){try{var e=fe(),t=O(),n=e.model||e.os.name||"unknown";if("iOS"!==e.os.name||t)return n;var r=function(){for(var e=I(),t=[],n=0,r=ge;n<r.length;n++){var o=r[n];e===o[1]&&(t=t.concat(o[2]))}return t.length&&t}();return r?r.join(" or "):n}catch(e){return"unknown"}},ye={},_e=function(e){try{if(window&&window.localStorage&&"undefined"!=typeof Storage&&window.localStorage instanceof Storage){var t=Number(function(e){try{if(window&&window.localStorage){var t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return t}}}catch(e){return null}return null}(e))||0;return t+1>1e8&&(t=0),function(e,t){try{window&&window.localStorage&&window.localStorage.setItem(e,JSON.stringify(t))}catch(e){return!1}}(e,t+1),t}}catch(e){}return ye[e]||(ye[e]=0),ye[e]++},we=0,be=O();function Ee(){try{var e=fe().os.name;return we=e?"Android"===e?be?6:8:"iOS"===e?be?7:9:10:0}catch(e){return 0}}function ke(){var e="unknown",t=null===window||void 0===window?void 0:window.navigator,n=(null==t?void 0:t.connection)||(null==t?void 0:t.mozConnection)||(null==t?void 0:t.webkitConnection);return n&&(e=n.type||n.effectiveType),e}var Se=function(e){function t(t){var n=e.call(this,u({platform:Ee(),container:10===we?"WEB":"H5",version_name:be&&G("appver",void 0,!0)||"",network_type:ke(),device_id:z(),global_id:be&&G("egid")||"",app_package:{language:navigator.language},device_package:{os_version:fe().os.version,model:me(),ua:navigator.userAgent}},t))||this;return Object.assign(n.h5_extra_attr,{host_product:he(),resolution:I(),screen_with:N(screen.width),screen_height:N(screen.height),device_pixel_ratio:window.devicePixelRatio||1,domain:window.location.origin},t.h5_extra_attr),n}return l(t,e),t.prototype.getVersionName=function(){return this.app_package.version_name},t.prototype.update=function(t){e.prototype.update.call(this,t);var n=this.app_package.version_name;if("string"==typeof n){var r=n.lastIndexOf(".");this.app_package.version_code=+n.slice(r+1)||0,this.h5_extra_attr.app_version_name=n}},t.prototype.setAdditionalSeqIdPackage=function(e){var t,n;this.additional_seq_id_package={channel:3,channel_seq_id:(n="NORMAL",void 0===n&&(n=""),_e("WEBLOGGER_CHANNEL_SEQ_ID_"+n)),custom_type:e,custom_seq_id:(t=e,_e("WEBLOGGER_V2_SEQ_ID_"+t))}},t.prototype.increaseH5SeqId=function(e){this.h5_extra_attr.client_timestamp=N((new Date).valueOf()),this.h5_extra_attr.seq_id=_e(e?"WEBLOGGER_H5_CUSTOM_SEQ_ID":"WEBLOGGER_H5_SEQ_ID")},t}($),Oe=function(e,t,n){return b("[error 400]","埋点上报接口请求报错","\nurl:",t,"\ndata",n,"\nerror:",e||"server decode log failed")};function Pe(e,t){var n=e.url,r=e.data,o=e.timeout,i=new XMLHttpRequest;i.open("POST",n),i.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),"object"!=typeof r||r instanceof Uint8Array||(r=JSON.stringify(r)),o&&(i.timeout=o),i.onload=function(){var e=function(e,t,n,r){var o;if(e<200||e>=300&&304!==e);else try{var i=JSON.parse(t),a=i.exception,s=i.result,c=i.error_msg;a&&(o=a),1!==s&&(o=c||"result is "+s)}catch(e){o=e.message}return o&&Oe(o,n,r),o}(i.status,i.response,n,r);t&&t(e)},i.ontimeout=i.onerror=function(e){e&&Oe(e,n,r),t&&t(e?"networkTimeout":"")},i.send(r)}var Ae=function(e){var t=function(t){return b("[error 401]","GET 请求出错 url: "+e,t)};try{var n=new XMLHttpRequest;n.open("GET",e),n.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),n.send(),n.onerror=t}catch(e){t(e)}},Le=null===window||void 0===window?void 0:window.navigator,Ce=Le&&-1!==Le.userAgent.indexOf("Chrome")&&"function"==typeof Le.sendBeacon;function Te(e,t){var n=e.url,r=e.data,o=e.timeout,i=e.isDebug;if(r=JSON.stringify(r),!i&&function(e){var t=e.url,n=e.data;if(!Ce)return!1;"object"==typeof n&&n.fd&&(n=n.fd);try{return Le.sendBeacon(t,n)}catch(e){return b("[error 403]","navigator.sendBeacon 报错",e),!1}}({url:n,data:r}))return t&&t();Pe({url:n,data:r,timeout:o},t)}var Ie,Re=function(e,t,n){return void 0===e&&(e=Z),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.log;n&&n.call.apply(n,h([console],e))}catch(e){return}}("%c埋点抓包校验: %c"+e+"/#/logger/index?sessionId="+n,"color:#1abf89;font-size:1.2em;line-height:2.4em;","font-size:1.2em;"),t+"/"+n+"/"};!function(e){e.loading="loading",e.loaded="loaded"}(Ie||(Ie={}));var Ne,Ue,De,xe=Object.create(null),je="weblogger_switch",Me=function(e){var t=function(){try{var e=G(je)||"undefined"!=typeof sessionStorage&&sessionStorage.getItem(je);if(!e)return;var t=JSON.parse(e),n=t.loggerSessionId,r=t.reportHost,o=t.loggerHost,i=t.handshakeApi;return n?(Ae(i||r+"/"+n),Re(o,r,n)):""}catch(e){return""}}()||function(e){void 0===e&&(e=location.href);var t=e.lastIndexOf("?");if(-1===t)return"";var n=P(e.slice(t+1)).webloggerSwitch;if(!n)return"";try{var r=decodeURIComponent(n),o=JSON.parse(r),i=o.loggerSessionId,a=o.reportHost,s=o.loggerHost,c=o.handshakeApi;return i?("undefined"!=typeof sessionStorage&&sessionStorage.setItem(je,r),Ae(c||a+"/"+i),Re(s,a,i)):""}catch(e){return""}}();if(t)return le(t,!1,e)},He=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.baseSendData=r.sendLog,r}return l(t,e),t.prototype.updateUrls=function(){var e=this.config,t=e.env,n=void 0===t?te:t,r=e.logger,o=e.proto,i=e.isBridge,a=e.disableCompress,s=Me(o);if(s)this.url=s,this.isDebug=!0;else if(r||n===ne||n===re){var c=function(e,t){void 0===e&&(e=z()),void 0===t&&(t=!1);var n=t?"https://data-track-sgp.corp.kuaishou.com":Z;return Ae(n+"/rest/"+e),Re(n,n+"/rest",e)}(this.commonPackage.identity_package.device_id,n===re);this.url=le(c,!1,o),this.isDebug=!0}else n&&/^(https?:)?\/\//.test(n)?this.url=n:(this.isDebug=n!==te,this.url=le(n,!1,o),this.radarUrl=le(n,!0,o));this.enableAsyncGzip=!a&&"v3"===o&&!i&&R()&&n===te&&!s&&!r,this.enableAsyncGzip&&function(e,t,n){if(void 0===t&&(t=!0),xe[e])return n&&n(e+" load repeat");var r=xe[e]={state:Ie.loading},o=document.createElement("script");o.src=e,t&&(o.async=!0);var i=document.getElementsByTagName("script")[0];i&&i.parentNode?i.parentNode.insertBefore(o,i):document.head.appendChild(o),o.onload=function(){r.state=Ie.loaded,n&&n()},o.onerror=function(t){r.state=Ie.loaded,n&&n(e+" loaded failed, "+t)}}("https://static.yximgs.com/udata/pkg/ks-track-platform-new/weblogger/3.9.49/async/gzipper.min.js"),this.formatUrl()},t.prototype.sendLog=function(e,t){!this.drained&&this.enableAsyncGzip&&this.sendGzip(e,t)||Te(e,t)},t.prototype.sendGzip=function(e,n){var r;return!!(null===(r=t.Gzipper)||void 0===r?void 0:r.sendData)&&t.Gzipper.sendData(e,(function(t){t&&Te(e,n)}))},t.prototype.getCommonPackageJSON=function(){return this.commonPackage.toJSON()},t.prototype.send=function(t,n,r){void 0===n&&(n=!1),this.isV2&&(this.commonPackage.setAdditionalSeqIdPackage(t.getEventType()),t.common_package=this.getCommonPackageJSON()),e.prototype.send.call(this,t,n,r)},t}(de);!function(e){var t,n,r,o,i,a,s,c;(t=e.ElementStatus||(e.ElementStatus={}))[t.UNKNOWN_STATUS=0]="UNKNOWN_STATUS",t[t.CHECKED=1]="CHECKED",t[t.UNCHECKED=2]="UNCHECKED",(n=e.PageShowAction||(e.PageShowAction={}))[n.UNKNOWN_ACTION=0]="UNKNOWN_ACTION",n[n.ENTER=1]="ENTER",n[n.LEAVE=2]="LEAVE",n[n.RESUME=3]="RESUME",(r=e.ActionStatus||(e.ActionStatus={}))[r.UNKNOWN_STATUS=0]="UNKNOWN_STATUS",r[r.SUCCESS=1]="SUCCESS",r[r.FAIL=2]="FAIL",(o=e.ActionType||(e.ActionType={}))[o.UNKNOWN_ACTION_TYPE=0]="UNKNOWN_ACTION_TYPE",o[o.CLICK=1]="CLICK",o[o.LEFT_PULL=2]="LEFT_PULL",o[o.RIGHT_PULL=3]="RIGHT_PULL",o[o.UP_PULL=4]="UP_PULL",o[o.DOWN_PULL=5]="DOWN_PULL",(i=e.SubAction||(e.SubAction={}))[i.UNKNOWN_SUB_ACTION=0]="UNKNOWN_SUB_ACTION",i[i.PAGE_ENTER=1]="PAGE_ENTER",i[i.PAGE_LEAVE=2]="PAGE_LEAVE",i[i.PAGE_RESUME=3]="PAGE_RESUME",i[i.PAGE_PAUSE=4]="PAGE_PAUSE",(a=e.Direction||(e.Direction={}))[a.UNKNOWN2=0]="UNKNOWN2",a[a.UP=1]="UP",a[a.DOWN=2]="DOWN",a[a.LEFT=3]="LEFT",a[a.RIGHT=4]="RIGHT",(s=e.TaskStatus||(e.TaskStatus={}))[s.UNKNOWN_STATUS=0]="UNKNOWN_STATUS",s[s.START=1]="START",s[s.RETRY=2]="RETRY",s[s.PAUSE=3]="PAUSE",s[s.RESUME=4]="RESUME",s[s.PENDING=5]="PENDING",s[s.PROCESSING=6]="PROCESSING",s[s.SUCCESS=7]="SUCCESS",s[s.FAIL=8]="FAIL",s[s.CANCEL=9]="CANCEL",s[s.FINISH=10]="FINISH",(c=e.OperationType||(e.OperationType={}))[c.UNKNOWN_OPERATION=0]="UNKNOWN_OPERATION",c[c.CLICK=1]="CLICK",c[c.DOUBLE_CLICK=2]="DOUBLE_CLICK",c[c.TRIPLE_CLICK=3]="TRIPLE_CLICK",c[c.LONG_PRESS=4]="LONG_PRESS",c[c.PULL=5]="PULL",c[c.DRAG=6]="DRAG",c[c.SCALE=7]="SCALE",c[c.PULL_DOWN=8]="PULL_DOWN",c[c.PULL_UP=9]="PULL_UP",c[c.RIGHT_CLICK=10]="RIGHT_CLICK",c[c.AUTO=11]="AUTO"}(Ne||(Ne={})),function(e){var t,n;(t=e.ShowType||(e.ShowType={}))[t.UNKNOWN_TYPE=0]="UNKNOWN_TYPE",t[t.PAGE_AUTO=10]="PAGE_AUTO",t[t.PAGE_CUSTOM=11]="PAGE_CUSTOM",t[t.ELEMENT=12]="ELEMENT",(n=e.TaskEventType||(e.TaskEventType={}))[n.UNKNOWN_TYPE=0]="UNKNOWN_TYPE",n[n.USER_OPERATION=1]="USER_OPERATION",n[n.STAY_LENGTH_STAT_EVENT=2]="STAY_LENGTH_STAT_EVENT",n[n.BACKGROUND_TASK_EVENT=3]="BACKGROUND_TASK_EVENT"}(Ue||(Ue={})),function(e){var t;(t=e.ShowType||(e.ShowType={}))[t.UNKNOWN2=0]="UNKNOWN2",t[t.PAGE=1]="PAGE"}(De||(De={}));var Ge,Be,We=!0,Ke=(new Date).valueOf(),Ve=function(e,t){var n,r;void 0===t&&(t=!1);var o=e.type,i=e.currentUrlPackage,a=e.referUrlPackage,s=e.name,c=void 0===s?"":s,l=e.action,d=e.beginTime,p=e.params,f=e.contentPackage,h=e.status,v=e.actionType,g=e.auto,m=((n={status:h?Ne.ActionStatus[h]||Ne.ActionStatus.UNKNOWN_STATUS:Ne.ActionStatus.SUCCESS})[t?"show_type":"action_type"]=v?Ne.ActionType[v]||Ne.ActionType.UNKNOWN_ACTION_TYPE:Ne.ActionType.CLICK,n.url_package=i,n.refer_url_package=a,n[t?"content_wrapper":"content_package"]=f,n);if("PV"===o){var y=Ne.SubAction.PAGE_ENTER,_=Ne.PageShowAction.ENTER,w=We,b=0;switch(We=!1,l){case"leave":_=Ne.PageShowAction.LEAVE,y=Ne.SubAction.PAGE_LEAVE,b=N((new Date).valueOf()-(d||Ke));break;case"enter":_=Ne.PageShowAction.ENTER,y=Ne.SubAction.PAGE_ENTER,Ke=(new Date).valueOf();break;case"visible":_=Ne.PageShowAction.RESUME,y=Ne.SubAction.PAGE_RESUME,Ke=(new Date).valueOf();break;case"hidden":_=Ne.PageShowAction.LEAVE,y=Ne.SubAction.PAGE_PAUSE,b=N((new Date).valueOf()-(d||Ke))}var E=g?Ue.ShowType.PAGE_AUTO:Ue.ShowType.PAGE_CUSTOM;return t&&(w=void 0,E=De.ShowType.PAGE),{show_event:u({action:_,sub_action:y,type:E,first_load:w,time_cost:0,stay_length:b},m)}}return{show_event:u({action:t?0:Ne.PageShowAction.ENTER,type:t?0:Ue.ShowType.ELEMENT,sub_action:t?0:Ne.SubAction.PAGE_ENTER,element_package:(r={},r[t?"action2":"action"]=c,r.params=JSON.stringify(p),r)},m)}},Fe=function(e,t){var n,r;void 0===t&&(t=!1);var o=e.sessionId,i=e.currentUrlPackage,a=e.referUrlPackage,s=e.name,c=e.params,l=e.type,d=e.contentPackage,p=e.status,f=e.taskType,h=e.operationDirection,v=((n={url_package:i,refer_url_package:a,element_package:(r={},r[t?"action2":"action"]=s,r.params=JSON.stringify(c),r)})[t?"content_wrapper":"content_package"]=d,n);if(t){var g="USER_OPERATION"===f||"CLICK"===l&&!f,m=l&&Ne.OperationType[l]||Ne.OperationType.CLICK;return g?{click_event:u({type:m,direction:h&&Ne.Direction[h]||Ne.Direction.UNKNOWN2},v)}:{task_event:u({action2:s,status:p&&Ne.TaskStatus[p]||Ne.TaskStatus.UNKNOWN_STATUS},v)}}return{task_event:u({type:f&&Ue.TaskEventType[f]||Ue.TaskEventType.USER_OPERATION,status:p&&Ne.TaskStatus[p]||Ne.TaskStatus.UNKNOWN_STATUS,operation_type:Ne.OperationType[l]||Ne.OperationType.CLICK,operation_direction:h&&Ne.Direction[h]||Ne.Direction.UNKNOWN2,session_id:o},v)}},Je=function(e){var t=e.name,n=e.params,r=e.currentUrlPackage,o=e.referUrlPackage,i=d(e,["name","params","currentUrlPackage","referUrlPackage"]);return{key:t,value:JSON.stringify(u(u({url_package:r,refer_url_package:o},n),i))}},ze=function(e){var t=e.currentUrlPackage,n=e.referUrlPackage,r=e.options;return{video_stat_event:u(u({},r),{url_package:t,refer_url_package:n})}},qe=(Ge=(new Date).getTimezoneOffset()/60)<=0?"GMT+"+S(-Ge+"",2,"0")+":00":"GMT-"+S(Ge+"",2,"0")+":00",Ye=function(){function e(e,t){void 0===t&&(t=!1),this.client_timestamp=N((new Date).valueOf()),this.client_increment_id=0,this.session_id=y.sessionId,this.event_id="",t||(this.time_zone=qe),Object.assign(this,e),this.genIncrementId()}return e.prototype.genIncrementId=function(){this.client_increment_id=this.isCustomStatEvent()?_e("WEBLOGGER_CUSTOM_INCREAMENT_ID_KEY"):_e("WEBLOGGER_INCREAMENT_ID_KEY")},e.prototype.isCustomStatEvent=function(){return!(!this.stat_package||!("custom_stat_event"in this.stat_package))},e.prototype.getEventType=function(){if(this.event_package){var e=this.event_package,t=e.task_event,n=e.show_event,r=e.click_event,o=e.custom_event;if(n)return"showEvent";if(r)return"clickEvent";if(t)return"taskEvent";if(o)return"customEvent"}return"customEvent"},e}();function Qe(e,t,n){var r;void 0===n&&(n=!1);var o,i=t.eventId,a=t.currentUrlPackage,s=t.referUrlPackage,c=t.contentPackage,l=t.name,u=t.params;switch(n&&"RADAR"!==e&&"CUSTOM"!==e&&(a&&(a.page2=a.page,delete a.page),s&&(s.page2=s.page,delete s.page)),e){case"PV":case"SHOW":return new Ye({event_package:Ve({type:e,status:t.status,currentUrlPackage:a,referUrlPackage:s,action:t.type,beginTime:t.beginTime,actionType:t.actionType,name:l,params:u,contentPackage:c,operationDirection:t.operationDirection,auto:t.auto},n),event_id:i},n);case"CUSTOM":case"CUSTOM_STAT_EVENT":var p=Je({name:l,params:u,currentUrlPackage:a,referUrlPackage:s});return new Ye(n?{event_package:{custom_event:p},event_id:i}:{stat_package:{custom_stat_event:p},event_id:i},n);case"RADAR":return new Ye({stat_package:{custom_stat_event:Je({name:l,params:u,currentUrlPackage:a,referUrlPackage:s})},event_id:i},n);case"HEART_BEAT_EVENT":case"LAUNCH_EVENT":case"APP_USAGE_STAT_EVENT":case"EXCEPTION_EVENT":case"DEVICE_STAT_EVENT":return new Ye({stat_package:(r={},r[e.toLowerCase()]=(o=t,o.currentUrlPackage,o.referUrlPackage,o.taskType,d(o,["currentUrlPackage","referUrlPackage","taskType"])),r)},n);case"VIDEO":return new Ye(n?{stat_package:ze({currentUrlPackage:a,referUrlPackage:s,options:u.params})}:{event_package:Fe({type:e,status:t.status,taskType:t.taskType,sessionId:y.sessionId,currentUrlPackage:a,referUrlPackage:s,name:l,params:u.params,contentPackage:c},n),event_id:i},n);default:return new Ye({event_package:Fe({type:e,status:t.status,taskType:t.taskType,sessionId:y.sessionId,currentUrlPackage:a,referUrlPackage:s,name:l,params:u,contentPackage:c},n),event_id:i},n)}}var Xe,$e=null,Ze="tool",et=!1,tt=function(e){return void 0===e&&(e=Be||"undefined"!=typeof window&&window.yoda),Be&&Be.isInYoda?Be:e&&e.isInYoda?Be=e:null},nt=function(){return p(void 0,void 0,void 0,(function(){return f(this,(function(e){switch(e.label){case 0:if(!Be||et)return[2];e.label=1;case 1:return e.trys.push([1,6,,7]),$e?[4,$e]:[3,3];case 2:return e.sent(),[3,5];case 3:return[4,$e=Be.ready()];case 4:e.sent(),Be.register({namespace:"webview",name:"getPageLoadData"}),Be.register({namespace:"tool",name:"setClientLog"}),Be.register({namespace:"tool",name:"sendRadarLog"}),Be.register({namespace:"tool",name:"sendSummarizedLog"}),Be.register({namespace:"tool",name:"getKswitchData"}),e.label=5;case 5:return $e=null,et=!0,[3,7];case 6:return e.sent(),[3,7];case 7:return[2]}}))}))},rt=function(e,t){return void 0===t&&(t="tool"),p(void 0,void 0,void 0,(function(){var n,r,o;return f(this,(function(i){switch(i.label){case 0:return n=!1,Be?et?[3,2]:[4,nt()]:[3,5];case 1:i.sent(),i.label=2;case 2:return i.trys.push([2,4,,5]),[4,Be.tool.canIUse({namespace:t,name:e})];case 3:return r=i.sent().canUse,n=r,[3,5];case 4:return o=i.sent(),b("[error 206]","yoda.tool.canIUse({ '"+t+"', '"+e+"' }) 报错: "+o.message),[3,5];case 5:return[2,n]}}))}))},ot=function(e,t){return p(void 0,void 0,void 0,(function(){var n;return f(this,(function(r){switch(r.label){case 0:return Be?et?[3,2]:[4,nt()]:[2];case 1:r.sent(),r.label=2;case 2:return r.trys.push([2,4,,5]),[4,Be[Ze].setClientLog({type:e,data:t},(function(n,r){n&&1!==n.result&&b("[error 203]","yoda.tool.setClientLog 埋点发送失败",JSON.stringify({type:e,data:t}),"result: "+n+", message: "+r)}))];case 3:return[2,r.sent()];case 4:return n=r.sent(),b("[error 209]","yoda."+Ze+".setClientLog() 报错: "+n.message),[3,5];case 5:return[2]}}))}))},it=function(){return p(void 0,void 0,void 0,(function(){var e;return f(this,(function(t){switch(t.label){case 0:return Be?[4,rt(e="setClientLog","tool")]:[2,!1];case 1:return t.sent()?(Ze="tool",[2,!0]):[4,rt(e,"platform")];case 2:return t.sent()?(Ze="platform",[2,!0]):[2,!1]}}))}))},at=void 0,st=function(e,t){return p(void 0,void 0,void 0,(function(){var n,r;return f(this,(function(o){switch(o.label){case 0:return Be?et?[3,2]:[4,nt()]:[2];case 1:o.sent(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,null===(r=Be.tool)||void 0===r?void 0:r.handleEntryTag({type:e,params:t})];case 3:return(n=o.sent())&&n.data&&n.data.entryTag?[2,n.data.entryTag]:[3,5];case 4:return b("[error 213]","操作染色参数报错",o.sent()),[3,5];case 5:return[2,null]}}))}))},ct={isSupportBridgeLog:it,isSupportBridge:rt,getWebviewLoadPerf:function(e){return void 0===e&&(e={}),p(void 0,void 0,void 0,(function(){var t;return f(this,(function(n){switch(n.label){case 0:return Be?et?[3,2]:[4,nt()]:[2];case 1:n.sent(),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,null===(t=Be.webview)||void 0===t?void 0:t.getPageLoadData(e)];case 3:return[2,n.sent()];case 4:return b("[error 207]","yoda.webview.getPageLoadData() 报错: "+n.sent().message),[3,5];case 5:return[2]}}))}))},sendRadarLog:function(e){return void 0===e&&(e={}),p(void 0,void 0,void 0,(function(){var t;return f(this,(function(n){switch(n.label){case 0:return Be?et?[3,2]:[4,nt()]:[2];case 1:n.sent(),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,null===(t=Be.tool)||void 0===t?void 0:t.sendRadarLog(e,(function(t,n){t&&1!==t.result&&b("[error 205]","yoda.tool.sendRadarLog 埋点发送失败",JSON.stringify(e),"result: "+t+", message: "+n)}))];case 3:return[2,n.sent()];case 4:return b("[error 207]","yoda.tool.sendRadarLog() 报错: "+n.sent().message),[3,5];case 5:return[2]}}))}))},setClientLog:ot,initYoda:nt,yodaAlready:et,getKDSWebviewLoadPerf:function(e){return void 0===e&&(e={}),p(void 0,void 0,void 0,(function(){var t;return f(this,(function(n){switch(n.label){case 0:return Be?et?[3,2]:[4,nt()]:[2];case 1:n.sent(),n.label=2;case 2:return[4,rt("getPageLoadData","system")];case 3:if(!n.sent())return[3,7];n.label=4;case 4:return n.trys.push([4,6,,7]),[4,null===(t=Be.system)||void 0===t?void 0:t.getPageLoadData(e)];case 5:return[2,n.sent()];case 6:return b("[error 212]","yoda.system.getPageLoadData() 报错",n.sent().message),[3,7];case 7:return[2]}}))}))},sendSummarizedLog:function(e){return void 0===e&&(e={}),p(void 0,void 0,void 0,(function(){var t;return f(this,(function(n){switch(n.label){case 0:if(!Be)return[2];n.label=1;case 1:return n.trys.push([1,5,,6]),et?[3,3]:[4,nt()];case 2:n.sent(),n.label=3;case 3:return[4,null===(t=Be.tool)||void 0===t?void 0:t.sendSummarizedLog(e)];case 4:return[2,n.sent()];case 5:return b("[error 208]","yoda.tool.sendSummarizedLog() 报错: "+n.sent().message),[3,6];case 6:return[2]}}))}))}},lt=function(e,t,n,r){void 0===e&&(e=v.CLICK),void 0===t&&(t={}),"string"!=typeof e&&(e=e.type),e=e.toUpperCase();var o={};return"string"==typeof t?(o.contentPackage=r,"CUSTOM"===e?(o.key=t,o.value=n):(o.params=n,"PV"===e?o.page=t:o.action=t)):o=t,[e,o]},ut="WEBLOGGER_SILENCED";"undefined"!=typeof window&&("object"!=typeof window._WEBLOGGER&&(window._WEBLOGGER={_silenced:!1,constructors:[],instances:[],silence:function(e){try{e?sessionStorage.setItem(ut,"1"):sessionStorage.removeItem(ut),window._WEBLOGGER.instances.forEach((function(t){null==t||t.silence(e)}))}catch(e){}}}),Xe=window._WEBLOGGER);var dt,pt,ft=!1,ht=Math.random(),vt=function(e){function t(t,n){void 0===t&&(t={});var r,o,i,a,s=e.call(this,t,n)||this;return s.yoda=null,s.SampledPageMap={},s.beforeUnload=function(e){if(!ft){for(var t in s.logger.drain(),s.plugins){var n=s.plugins[t];"function"==typeof n.beforeUnload&&n.beforeUnload(e)}ft=!0,setTimeout((function(){ft=!1}),2e3)}},s.isBridge=t.bridgeMode&&t.yoda&&t.yoda.isInYoda,s.logConfig.isBridge=s.isBridge,r="sessionId",o=n&&n.session_id||G("sid")||(i="",O()&&(i=G("session_id")||G("sid"))?i:V()),y[r]=o,s.isV2="v2"===s.logConfig.proto,s.getSampled(t),s.presetBaseOption(s.baseOption),s.commonPackage=new Se(s.baseOption),s.logger=new He(s.logConfig,s.commonPackage),s.initYoda(),s.addPlugins(),E(window,"pagehide",s.beforeUnload),E(window,"beforeunload",s.beforeUnload),a=s,(null==Xe?void 0:Xe.instances)&&a&&-1===Xe.instances.indexOf(a)&&(Xe.instances.push(a),Xe._silenced&&(null==a||a.silence(!0))),s}return l(t,e),t.prototype.getSampled=function(e){"function"==typeof e.sampleRateFn?this.sampled=!!e.sampleRateFn():void 0!==e.sampleRate&&(this.sampled=ht<Number(e.sampleRate))},t.prototype.presetBaseOption=function(e){this.isBridge||(this.isV2?("number"!=typeof e.product&&b("[error 101]","请设置有效的 product 值，值类型为 number!"),delete e.product_name):(e.product_name&&"string"==typeof e.product_name||b("[error 100]","请设置有效的 product_name 值，值类型为 string!"),delete e.product),e.user_id||(e.user_id=G("userId")||G("userName")||void 0,e.user_id||function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,h([console],e))}catch(e){return}}("[warn 102]","user_id 为空，请确定是否传入，如果是异步通过 updateCommonPackage 接口传入，可以忽略该提示!")),e.device_id||(e.device_id=z(),e.device_id||b("[error 103]","device_id 为空，请设置有效的设备 id!")),!e.global_id&&O()&&(e.global_id=G("egid")||""))},Object.defineProperty(t.prototype,"Utils",{get:function(){return{yoda:this.yoda&&ct,cookie:W,ua:ve,io:{sendData:Te}}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isKSwitchSampled",{get:function(){return void 0!==typeof at},enumerable:!1,configurable:!0}),t.prototype.initYoda=function(){return p(this,void 0,void 0,(function(){var e=this;return f(this,(function(t){switch(t.label){case 0:return this.logConfig.yoda&&!this.yoda&&(this.yoda=tt(this.logConfig.yoda)),this.yoda?[4,nt()]:[3,2];case 1:t.sent(),t.label=2;case 2:return p(void 0,void 0,void 0,(function(){var e,t,n,r,o;return f(this,(function(i){switch(i.label){case 0:if(!Be)return[2,at=null];if(void 0!==at)return[2,at];i.label=1;case 1:return i.trys.push([1,4,,5]),[4,rt("getKswitchData","tool")];case 2:return i.sent()?[4,Be.tool.getKswitchData({keys:[{key:"webloggerSampling"}]})]:[2,at=null];case 3:if((e=i.sent())&&e.data&&("string"==typeof(t=e.data[0])&&(t=JSON.parse(t)),n=t?t.value:null)){for(o in"string"==typeof n&&(n=JSON.parse(n)),r="undefined"!=typeof location?location.host:"*",n)"*"!==o&&-1===o.indexOf(r)||(at||(at={}),at[o]=n[o]);if(at)return[2,at]}return[3,5];case 4:return b("[error 201]","yoda.tool.getKswitchData 采样配置获取出错",i.sent().message),[3,5];case 5:return[2,at=null]}}))})).then((function(){e.currentUrlPackage&&(e.currentUrlPackage.sampled=e.getPageSampled())})),p(void 0,void 0,void 0,(function(){var e;return f(this,(function(t){switch(t.label){case 0:return Be?[4,rt("getDeviceInfo","system")]:[2,null];case 1:if(!t.sent())return[3,5];t.label=2;case 2:return t.trys.push([2,4,,5]),[4,null===(e=Be.system)||void 0===e?void 0:e.getDeviceInfo()];case 3:return[2,t.sent()];case 4:return b("[error 210]","yoda.system.getDeviceInfo() 报错",t.sent().message),[3,5];case 5:return[2]}}))})).then((function(t){t&&t.mod&&(e.commonPackage.device_package.model=t.mod)})),p(void 0,void 0,void 0,(function(){var e;return f(this,(function(t){switch(t.label){case 0:return Be?[4,rt("getAppInfo","system")]:[2,null];case 1:if(!t.sent())return[3,5];t.label=2;case 2:return t.trys.push([2,4,,5]),[4,null===(e=Be.system)||void 0===e?void 0:e.getAppInfo()];case 3:return[2,t.sent()];case 4:return b("[error 209]","yoda.system.getAppInfo() 报错",t.sent().message),[3,5];case 5:return[2]}}))})).then((function(t){t&&(t.did&&(e.commonPackage.identity_package.device_id=t.did),t.userId&&!e.commonPackage.identity_package.user_id&&(e.commonPackage.identity_package.user_id=t.userId),t.appver&&(e.commonPackage.app_package.version_name=t.appver))})),[2]}}))}))},t.prototype.initUrlPackage=function(){e.prototype.initUrlPackage.call(this);var t,n=this.logConfig.referer,r="web";n?(t=n.value,r=n.type&&n.type||r):"undefined"!=typeof document&&document.referrer&&(t=document.referrer),t&&this.updateReferUrlPackage(t,r)},t.prototype.updateCurrentUrlPackage=function(e,t){if(void 0===e&&(e=(null===location||void 0===location?void 0:location.href)||""),void 0===t&&(t="web"),"object"==typeof e&&!e.force&&this.currentUrlPackage){var n=e.page,r=void 0===n?this.currentUrlPackage.page:n,o=e.params,i=void 0===o?{}:o;if(r===this.currentUrlPackage.page)return this.currentUrlPackage.update(r,i)}this.currentUrlPackage=new X(e,t,this.logConfig.urlMap),this.currentUrlPackage.sampled=this.getPageSampled(),this.logConfig.attachUrl&&this.currentUrlPackage.attachUrl()},t.prototype.updateReferUrlPackage=function(e,t){void 0===e&&(e=this.currentUrlPackage),void 0===t&&(t="web"),this.referUrlPackage=e instanceof X?e:new X(e,t,this.logConfig.urlMap)},t.prototype.getPageSampled=function(){if(!at)return"boolean"!=typeof this.sampled||this.sampled;var e="undefined"!=typeof location?location.href.replace(/https?:\/\//,"").split("?")[0]:"*";if(this.SampledPageMap&&void 0!==this.SampledPageMap[e])return this.SampledPageMap[e];if(at){var t=void 0,n=0,r=null,o=[];for(var i in at)if(at.hasOwnProperty(i)){var a=0;if("*"===i?a=1:0===e.indexOf(i)&&(i===e?a=100:"/"===i[i.length-1]&&(a=11-1/i.length)),!a)continue;var s=at[i],c=s.radar,l=s.overwrite,u=s.overall;a+=l?1e3:0,c&&o.push({radar:c,level:a}),a>n&&(n=a,t=u)}n&&(r={},o.sort((function(e,t){return e.level-t.level})).forEach((function(e){Object.assign(r,e.radar)})));var d=void 0;d=n>1e3||void 0===this.sampled?Math.random()<Number(t)&&(!r||{radar:r}):"boolean"!=typeof this.sampled||this.sampled,this.SampledPageMap&&(this.SampledPageMap[e]=d)}return this.SampledPageMap&&this.SampledPageMap[e]},Object.defineProperty(t.prototype,"isSendSampled",{get:function(){return this.currentUrlPackage&&this.currentUrlPackage.sampled||this.logger.isDebug},enumerable:!1,configurable:!0}),t.prototype.generateLog=function(e,t,n){void 0===n&&(n=Qe);var r,o=t,i=o.action,a=o.params,s=o.status,c=o.type,l=o.eventId,d=o.contentPackage,p=o.urlPage;if(d&&(d="string"==typeof d?d:JSON.stringify(d)),e===g.PV){var f=t,h=f.page,v=f.type;v||(t.type=v="enter"),"enter"===v&&h?this.updateCurrentUrlPackage({page:h,params:a}):this.currentUrlPackage.update(h,a)}else p&&p.page&&(r={page:p.page,identity:p.identity||V(),params:JSON.stringify(p.params),page_type:this.currentUrlPackage.page_type});var m={currentUrlPackage:r||this.currentUrlPackage.toJSON(),referUrlPackage:this.referUrlPackage?this.referUrlPackage.toJSON():void 0,contentPackage:d};if(e!==g.RADAR&&m.currentUrlPackage.page.indexOf("http"),e===g.PV){var y=t.type;return n(e,u(u(u({},t),{type:y}),m),this.isV2)}if(e===g.SHOW)return n(e,u(u(u({},t),{name:i}),m),this.isV2);if(e===g.VIDEO)return n(e,u({params:t,name:"VIDEO"},m),this.isV2);if(e===g.RADAR)return n(e,t,this.isV2);if(e===g.CUSTOM){var _=t,w=_.key,b=_.value,E=_.eventId;return n(e,u(u(u({},t),{params:b,eventId:E,name:w}),m),this.isV2)}return n(e,u(u(u({},t),{params:a,status:s,taskType:c,eventId:l,name:i}),m),this.isV2)},t.prototype.collect=function(e,t,n,r){var o=lt(e,t,n,r),i=o[0],a=o[1];return this.send(i,a,!1)},t.prototype.sendImmediately=function(e,t,n,r){var o=lt(e,t,n,r),i=o[0],a=o[1];return this.send(i,a,!0)},t.prototype.beforeSend=function(e,t,n){var r,o,i=this;if(this.emit("event",{type:e,action:t.type||t.action,data:n}),"CLICK"===e){var a=null===(r=t)||void 0===r?void 0:r.entryTag;this.yoda&&a&&st("setPendingEntryTag",{entryTag:u({page_name:this.currentUrlPackage.page,element_action:null==t?void 0:t.action},a)})}else"PV"===e&&this.yoda&&!this.isBridge&&"enter"===((null===(o=t)||void 0===o?void 0:o.type)||"enter")&&(this.logger.sendingYield=p(i,void 0,void 0,(function(){var e,t;return f(this,(function(n){switch(n.label){case 0:return e=this.currentUrlPackage.identity,[4,st("clearEntryTag",{subPage:e})];case 1:return n.sent(),[4,st("consumeEntryTag",{subPage:e})];case 2:return t=n.sent(),this.commonPackage.updateGlobalAttr({entry_tag:t}),[2]}}))})))},t.prototype.send=function(e,t,n){if(!this._silenced){var r=this.generateLog(e,t);if(this.beforeSend(e,t,r),"RADAR"===e)return this.logger.sendRadar(r,null==t?void 0:t.serviceName);if((!this.isV2||!this.logConfig.forbidV2HttpUrlPage||C(this.currentUrlPackage.page))&&this.isSendSampled){var o="object"==typeof t&&t.callback||void 0;this.logger.send(r,!!n,o)}}},t.prototype.destroy=function(){e.prototype.destroy.call(this),k(window,"pagehide",this.beforeUnload),k(window,"beforeunload",this.beforeUnload),function(e){if(null==Xe?void 0:Xe.instances){var t=Xe.instances.indexOf(e);-1!==t&&Xe.instances.splice(t,1)}}(this)},t.prototype.silence=function(e){this._silenced=e},t.Logger=He,t}(x);try{pt=tt(a.default.default||a.default)}catch(e){pt=tt("undefined"!=typeof window&&(window.yoda||window.KSYoda))}var gt,mt=null===(dt=null===window||void 0===window?void 0:window.navigator)||void 0===dt?void 0:dt.platform,yt=mt&&-1!==["iOS","Android"].indexOf(mt),_t=function(e){function t(t,n){var r=e.call(this,u(u({},t),{yoda:pt||void 0,bridgeMode:!(null==t?void 0:t.disableBridge)}),n)||this;return r.entered="",r.checkYodaSupport(),r}return l(t,e),t.prototype.checkYodaSupport=function(){return p(this,void 0,void 0,(function(){var e,t=this;return f(this,(function(n){switch(n.label){case 0:return this.waitYodaQueue&&this.yodaStatus?[2]:(this.waitYodaQueue=[],this.yodaStatus="CHECKING",this.logConfig.disableBridge?(this.yodaStatus="DISABLED",[3,3]):[3,1]);case 1:return[4,it()];case 2:e=n.sent(),this.yodaStatus=e?"READY":"DISABLED",this.waitYodaQueue.forEach((function(e){var n=e.type,r=e.options,o=e.immediately;t.send(n,r,o)})),this.waitYodaQueue=[],n.label=3;case 3:return[2]}}))}))},t.prototype.sendByBridge=function(e,t,n){void 0===e&&(e=v.CLICK);var r=t.contentPackage;if(r&&(r="string"==typeof r?r:JSON.stringify(r)),"PV"===e){var o=t,i=o.page,a=o.type,s=o.params,c=o.coPage;a||(t.type=a="enter"),"enter"===a?((i=i||this.currentUrlPackage.page)&&(this.updateCurrentUrlPackage({page:i,params:s||this.currentUrlPackage.params,force:!0}),this.entered=i),c&&(this.currentUrlPackage.coPage=c)):this.currentUrlPackage.update(i,s)}var l=!!this.currentUrlPackage.coPage,d=function(e,t){var n=t,r=n.action,o=n.params,i=n.eventId,a=n.contentPackage,s=n.currentUrlPackage,c=n.status;n.name;var l=n.feedLogCtx;switch(e){case"PV":var d=t,p=d.type,f=d.beginTime;return u(u({},s),{actionType:p,contentPackage:a,pageType:"H5",status:c||"SUCCESS",beginTime:f,eventId:i});case"SHOW":return{action:r,params:JSON.stringify(o),contentPackage:a,feedLogCtx:l,eventId:i};case"RADAR":case"CUSTOM":var h=t,v=h.key,g=h.value,m=h.biz;return{key:v,value:JSON.stringify(u(u({},g),{url_package:s})),biz:m,eventId:i};default:var y=t.type;return{action:r,params:JSON.stringify(o),contentPackage:a,type:y||"USER_OPERATION",status:c||"UNKNOWN_STATUS",operationType:e,operationDirection:"UNKNOWN2",feedLogCtx:l,eventId:i}}}(e,u(u({},t),{contentPackage:r,currentUrlPackage:this.currentUrlPackage.toJSON()})),p=-1!==["CUSTOM","RADAR"].indexOf(e);this.commonPackage.increaseH5SeqId(p);var f=this.baseOption,h=f.service_name,g=f.sub_biz,m=f.need_encrypt;if(d.h5ExtraAttr=JSON.stringify(this.commonPackage.getH5ExtraAttr({bridge_info:(null==pt?void 0:pt.version)||!0,coPage:l})),d.realtime=!!n,d.serviceName=h||"",d.subBiz=g||"",d.needEncrypt=m||!1,d.container=yt?"REACT_NATIVE":"H5",this.emit("event",{type:e,action:t.type||t.action,data:d}),"PV"!==e){if(this.currentUrlPackage&&!this.logConfig.disablePV&&(d.urlPage={page:this.currentUrlPackage.page,params:JSON.stringify(this.currentUrlPackage.params),identity:this.currentUrlPackage.identity,coPage:l}),"SHOW"===e)return ot("addElementShowEvent",d);if(p){this.logConfig.biz&&!d.biz&&(d.biz=this.logConfig.biz);var y=this.logConfig.customStatToCustom?"addCustomEvent":"addCustomStatEvent";return ot(y,d)}return ot("addTaskEvent",d)}this.logConfig.disablePV||C(this.currentUrlPackage.page)&&("enter"!==(a=t.type)&&"visible"!==a||(d.coPage=l,d.pageType=yt?"NATIVE":"H5",ot("setCurrentPage",d)))},t.prototype.updateCurrentUrlPackage=function(e,t){if(void 0===e&&(e=(null===location||void 0===location?void 0:location.href)||""),void 0===t&&(t="web"),"object"==typeof e&&!e.force&&this.currentUrlPackage){var n=e.page,r=void 0===n?this.currentUrlPackage.page:n,o=e.params,i=void 0===o?{}:o;if(!r||r===this.currentUrlPackage.page){this.currentUrlPackage.update(r,i);var a=this.entered===r;return void("DISABLED"!==this.yodaStatus&&a&&this.send("PV",{type:"visible",page:r,params:i},!0))}}this.currentUrlPackage=new X(e,t,this.logConfig.urlMap),this.currentUrlPackage.sampled=this.getPageSampled(),this.logConfig.attachUrl&&this.currentUrlPackage.attachUrl()},t.prototype.sendByHttp=function(t,n,r){return void 0===t&&(t=v.CLICK),e.prototype.send.call(this,t,n,r)},t.prototype.send=function(e,t,n){void 0===e&&(e=v.CLICK),this.checkYodaSupport();var r="CUSTOM"===e&&this.logConfig.enableV3CustomEvent;return this.logConfig.openHttpSender||"DISABLED"===this.yodaStatus||r?this.sendByHttp(e,t,n):"CHECKING"!==this.yodaStatus?"READY"===this.yodaStatus?(this.beforeSend(e,t),this.sendByBridge(e,t,n)):void 0:void this.waitYodaQueue.push({type:e,options:t,immediately:n})},t}(vt),wt=function(){function e(){}return e.prototype.apply=function(e){this.weblog=e},e}();gt=_t,void 0!==window._GLOBAL_KS_WEBLOGGER_?window._GLOBAL_KS_WEBLOGGER_.Factory=gt:window.Weblog=gt,(null==Xe?void 0:Xe.constructors)&&gt&&-1===Xe.constructors.indexOf(gt)&&(gt._classId=Xe.constructors.length,Xe.constructors.push(gt)),function(){if(null==Xe?void 0:Xe.silence)try{Xe._silenced=!!sessionStorage.getItem(ut)}catch(e){}}(),e.BasePlugin=wt,e.Weblog=_t,e.default=_t,Object.defineProperty(e,"__esModule",{value:!0})}(t,G)}));V(J);var z,q=F((function(e,t){e.exports=function(){var e=function(){return(e=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function n(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function r(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];try{var o=null===console||void 0===console?void 0:console.log;return o&&o.call.apply(o,n([console,"radar:"],t(e),!1))}catch(e){return}}function o(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];try{var o=null===console||void 0===console?void 0:console.error;o&&o.call.apply(o,n([console,"radar:"],t(e),!1))}catch(e){return}}s.prototype.use=function(){for(var e,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var i=this.middlewares.pop();(e=this.middlewares).push.apply(e,n([],t(r),!1)),this.middlewares.push(i)},s.prototype.invoke=function(e){t=this.middlewares;var t,n=e,r=void 0,o=-1;return i(0);function i(e){if(e<=o)return new Error("`next` 被重复调用");var a=t[o=e];if(a=e===t.length?r:a)try{return a(n,i.bind(null,e+1))}catch(e){return e}}};var i,a=s;function s(){for(var e,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];this.middlewares=[],(e=this.middlewares).push.apply(e,n([],t(r),!1))}function c(e){return"object"==typeof e&&Object.keys(e).every((function(t){return"number"==typeof e[t]}))}var l=[(i={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"}).LOAD,i.RES,i.API,i.ERROR,i.EVENT,i.BLOOD],u=((T=q={}).RADAR_INIT="radar_init",T.H5_LOAD="h5_load",T.FMP="fmp",T.MAIN_API="main_api",T.H5_CUSTOM_METRIC="h5_custom_metric",[q.FMP,q.H5_LOAD,q.MAIN_API,q.RADAR_INIT,q.H5_CUSTOM_METRIC]),d={load:function(e){var t=e.dimension,n=e.value;if(e=t.event_name,u.indexOf(e)<0)return!1;if(!c(n))return!1;switch(e){case"fmp":var r=n.fmp_time;return!(!n.fmp||!r);case"h5_load":return r=n.total,!(!n.load_event_time||!r);case"h5_custom_metric":return r=n.custom_metric_timestamp,!(!t.custom_metric_name||!r)}return!0},api:function(e){var t=e.dimension,n=(e=e.value,t.custom_failed),r=t.api,o=t.status,i=t.res_type,a=t.protocol,s=(t=t.cached,e.duration),l=e.size;return"boolean"==typeof(void 0!==n&&n)&&"string"==typeof(void 0===r?"":r)&&"number"==typeof(void 0===o?0:o)&&"string"==typeof(void 0===i?"":i)&&"string"==typeof(void 0===a?"":a)&&"boolean"==typeof(void 0!==t&&t)&&"number"==typeof(void 0===s?0:s)&&"number"==typeof(void 0===l?0:l)&&c(e)},resource:function(e){var t=e.dimension,n=(e=e.value,t.failed);if("boolean"!=typeof n)return!1;if(n){var r=t.res_path;if("string"!=typeof t.file||"string"!=typeof r)return!1}return!!n||"string"==typeof t.file&&c(e)},error:function(e){var t,n=(e=e.dimension).error_type,r=e.error_cons_type,o=e.msg;return!!(r&&n&&o)&&(r=e.col,n=e.line,e.sample_rate,o=function(e,t){var n={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}(e,["col","line","sample_rate"]),!(r&&"number"!=typeof r||n&&"number"!=typeof n)&&"object"==typeof(t=o)&&Object.keys(t).every((function(e){return"string"==typeof t[e]})))},event:function(e){var t=e.value;return e=e.dimension.name,!(void 0!==t&&!c(t)||!e)},blood:function(e){var t=!0;if((e=e.dimension.data)instanceof Array){if(0===e.length)return!1;e.forEach((function(e){"string"!=typeof e.com&&(t=!1)}))}return t}};function p(e){return JSON.parse(JSON.stringify(e))}function f(e){try{var t;if(window&&window.localStorage&&"undefined"!=typeof Storage&&window.localStorage instanceof Storage)return t=Number(function(e){try{if(window&&window.localStorage){var t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return t}}}catch(e){return null}return null}(e))||0,function(e,t){try{window&&window.localStorage&&window.localStorage.setItem(e,JSON.stringify(t))}catch(e){return}}(e,(t=g<t+1?0:t)+1),t}catch(e){}}function h(e){return m[e]||(m[e]=0),m[e]++}function v(e){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];1==+function(e){var n,r,o={};e=e.split("&");try{for(var i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return{value:(e=e&&r>=e.length?void 0:e)&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),a=i.next();!a.done;a=i.next()){var s=t(a.value.split("="),2),c=s[0],l=s[1];c in o?o[c]instanceof Array?o[c].push(l):o[c]=[o[c],l]:o[c]=l}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return o}(location.search.slice(1)).debug&&function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];try{var o=null===console||void 0===console?void 0:console.warn;return o&&o.call.apply(o,n([console,"radar:"],t(e),!1))}catch(e){return}}.apply(void 0,n(["[radar] ".concat(e,": ")],t(r),!1))}var g=1e8,m={};window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{};var y=["c_dimension1","c_dimension2","c_dimension3"];w.prototype.getSampleData=function(){var e=this;this.isInYoda&&(window.updateYodaSampleRateWithParams=function(t){r("104",t),e.updateSampleData(t)}),window.__yodaCommonDataObject__&&window.__yodaCommonDataObject__.sampleData?(this.updateSampleData(),this.collect(i.EVENT,{name:"客户端采样率注入成功",event_type:"radar_sample_inject_success"},{})):this.isInYoda&&"function"==typeof this.weblog.Utils.yoda.getWebviewLoadPerf&&this.weblog.Utils.yoda.getWebviewLoadPerf().then((function(t){t.sampleData&&e.updateSampleData(t.sampleData)}))},w.prototype.updateSampleData=function(e){var t=this,n=null;try{var o=e||window.__yodaCommonDataObject__.sampleData;n="string"==typeof o?JSON.parse(o):o}catch(e){return void r("error: ",e)}this.sampleData=this.defaultSampleList,n&&Object.keys(n).forEach((function(e){"load"===e?t.sampleData.core=n[e]:t.sampleData[e]=n[e]}))},w.prototype.updateUrlPackage=function(){this.weblog.currentUrlPackage&&(this.currentUrlPackage=this.weblog.currentUrlPackage,this.referUrlPackage=this.weblog.referUrlPackage)},w.prototype.setDimensions=function(e){var t=this,n=Object.keys(e);n.some((function(e){return-1===y.indexOf(e)}))&&o("113","".concat(n.join("、"))),n.forEach((function(n){null==e[n]?delete t.customDimension[n]:t.customDimension[n]=e[n]}))},w.prototype.decorateLog=function(e){this.currentUrlPackage||this.updateUrlPackage(),0<Object.keys(this.customDimension).length&&Object.assign(e.dimension,this.customDimension),this.currentUrlPackage&&this.currentUrlPackage.page!==(null==(e=this.weblog.currentUrlPackage)?void 0:e.page)&&(this.flush(),this.updateUrlPackage())},w.prototype.getCommonData=function(){var t,n=null==(n=(void 0===(n=this.weblog.commonPackage)?{}:n).app_package)?void 0:n.version_name;return n={h5_extra_attr:JSON.stringify(e(e({},null==(t=this.weblog.commonPackage)?void 0:t.getH5ExtraAttr()),{app_version_name:n,url:location.href,hash:location.hash,online:null===navigator||void 0===navigator?void 0:navigator.onLine,downlink:null==(t=null===navigator||void 0===navigator?void 0:navigator.connection)?void 0:t.downlink,rtt:null==(t=null===navigator||void 0===navigator?void 0:navigator.connection)?void 0:t.rtt})),url_package:this.currentUrlPackage&&this.currentUrlPackage.toJSON(),refer_url_package:this.referUrlPackage&&this.referUrlPackage.toJSON(),app_version_name:n,project_id:null==(t=this.config)?void 0:t.projectId},"1"===(t="radar_report_test",(t=new RegExp("(?:^|; ?)".concat(t,"=(1|0)(;|$)")).exec(document.cookie))&&t[1])&&(n.is_report_test=!0),n},w.prototype.reportByHttp=function(){var e=this.getCommonData(),t=(e={project_id:this.config.projectId,radar_session_id:this.radarSessionId,h5_extra_attr:e.h5_extra_attr},this.logQueue.filter((function(e){return"load"===e.key}))),n=this.logQueue.filter((function(e){return"load"!==e.key}));Array.isArray(t)&&t.length&&this.collectLog(e,t,"radarSDK"),Array.isArray(n)&&n.length&&this.collectLog(e,n,"radarSDKSupplement")},w.prototype.collectLog=function(t,n,r){"function"!=typeof this.weblog.collect&&"function"!=typeof this.weblog.sendByHttp?o(114):(this.weblog.sendByHttp||this.weblog.collect).call(this.weblog,"RADAR",{name:"radar_log",params:e(e({},t),{data:function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}(n),!1)}),serviceName:r})},w.prototype.reportByBridge=function(){var e=this.weblog.Utils;if(!(e=(void 0===e?{}:e).yoda))return!1;var t,n=this.logQueue.filter((function(e){return"load"===e.key})),r=this.logQueue.filter((function(e){return"load"!==e.key}));try{return n.length&&(t=n[0],e.sendSummarizedLog({key:"h5_load",data:t})),r.length&&e.sendRadarLog({data:r}),!0}catch(e){return!1}};var _=w;function w(t){var n,s,c,u=this;if(this.queueConfig={wait:1e3,maxBatchLength:200},this.logQueue=[],this.batchTimer=0,this.eventName="onpagehide"in window?"pagehide":"beforeunload",this.customDimension={},this.ignoreList=["https://web-trace.ksapisrv.com/ktrace/collect"],this.collect=function(t,n,s){var c=p({key:t,dimension:n=void 0===n?{}:n,value:s=void 0===s?{}:s});if(!u.sampleData||!1!==u.sampleData.isHit){if(!((n=c)?l.indexOf(n.key)<0?(o("117"),0):d[n.key](n):(o("116"),0)))return o("107, key: ".concat(c.key)),r(c),void u.collect(i.EVENT,{message:"radar上报日志类型错误",name:"radar_log_error",extra_info:JSON.stringify(c)},{});Object.keys(c.value).forEach((function(e){c.value[e]=c.value[e].toString()})),u.decorateLog(c);var g=u.getCommonData(),m=g.app_version_name;if(delete g.app_version_name,(t=new a((function(t,n){return t.dimension.app_version_name=m,e(e(e({},t),g),{event_client_timestamp:Date.now(),event_trigger_source:"H5",radar_session_id:u.radarSessionId})}))).use((function(t,n){return t.key&&"load"===t.key&&(t.dimension=e(e({},t.dimension),{load_device_increase_id:f("LOAD_DEVICE_INCREASE_ID"),load_session_increase_id:h("LOAD_SESSION_INCREASE_ID")})),t.key&&"load"!==t.key&&(t.dimension=e(e({},t.dimension),{other_device_increase_id:f("OTHER_DEVICE_INCREASE_ID"),other_session_increase_id:h("OTHER_SESSION_INCREASE_ID")})),n()})),(c=t.invoke(c))instanceof Error)throw c;try{window.dispatchEvent(new CustomEvent("get-radar-next-data",{detail:{kv:p(c)}}))}catch(t){}if(delete c.dimension.broadcast_info,u.logQueue.push(c),v("radarLogNext",JSON.parse(JSON.stringify(c))),u.logQueue.length>u.queueConfig.maxBatchLength-1)return u.flush(),clearTimeout(u.batchTimer),void(u.batchTimer=0);u.batchTimer||(u.batchTimer=window.setTimeout((function(){u.flush(),clearTimeout(u.batchTimer),u.batchTimer=0}),u.queueConfig.wait))}},this.flush=function(){var e;u.logQueue.length<=0||(e=null,(e=u.config.httpReportFirst?e:u.reportByBridge())&&!u.config.httpReportFirst||u.reportByHttp(),u.logQueue=[])},"object"!=typeof t||null===t)throw new Error("radar error 108");if(null===t.weblogger||"object"!=typeof t.weblogger)throw new Error("radar error 106");if("string"!=typeof t.projectId)throw new Error("radar error 109");this.radarSessionId="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),this.weblog=t.weblogger,void 0!==(null==(n=null==(n=(this.config=t).weblogger)?void 0:n.logger)?void 0:n.radarUrl)&&this.ignoreList.push(null==(n=null==(n=t.weblogger)?void 0:n.logger)?void 0:n.radarUrl),void 0!==(null==(n=null==(n=t.weblogger)?void 0:n.logger)?void 0:n.url)&&this.ignoreList.push(null==(t=null==(n=t.weblogger)?void 0:n.logger)?void 0:t.url),null!=(n=this.config)&&n.ignoreList&&Array.isArray(null==(t=this.config)?void 0:t.ignoreList)&&(this.ignoreList=this.ignoreList.concat(this.config.ignoreList.map((function(e){return e.replace(/^(https?:)?\/\//,"")})))),this.queueConfig=e(e({},this.queueConfig),null!=(n=this.config)&&n.queueConfig?this.config.queueConfig:{}),this.isInYoda=!(null==(n=null==(t=this.weblog)?void 0:t.Utils)||!n.yoda),null!=(t=this.config)&&t.customDimensions&&this.setDimensions(null==(n=this.config)?void 0:n.customDimensions),t=window,n=this.eventName,s=this.flush,"attachEvent"in t?t.attachEvent("on"+n,s):t.addEventListener(n,s,c),this.updateUrlPackage(),"undefined"!=typeof window&&window.yodaCollectErrorDataDestroy&&window.yodaCollectErrorDataDestroy(),this.sampling="number"==typeof(t=this.config.sampling)&&0<=t&&t<=1?t:(o("110"),1),this.isInYoda?this.sampleData=this.defaultSampleList={isHit:!0,core:this.sampling,api:1,resource:.1,error:1,event:1}:this.sampleData=this.defaultSampleList={isHit:null!==(n=this.sampling)&&"number"==typeof n&&Math.random()<n,core:this.sampling},this.getSampleData()}var b=function(e,t){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},E=function(){return(E=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function k(e,t){var n={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}var S,O=function(){return(O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function P(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function A(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function L(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,A([console,"radar:"],P(e),!1))}catch(e){return}}function C(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.error;n&&n.call.apply(n,A([console,"radar:"],P(e),!1))}catch(e){return}}S={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},I.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},I.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===S.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return L("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?(L("115，".concat(this.key)),t):n},I.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},I.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return O(O({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,O(O({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):L("110,".concat(this.key))};var T=I;function I(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}function R(e,t){var n={};return Object.keys(e).forEach((function(r){var o=function e(t,n,r){var o=n[t];if(!o)throw new Error("【Radar Util】 perf key ".concat(t," is unexpected!"));if(!o.cachedSkip||!e("cached",n,r)){if("function"==typeof o.custom)return o.custom(t,r);n=o.end,t=o.start;var i=+r[n]||0,a=+r[t]||0;return!o.notCatchIfUndefined||void 0!==r[n]&&void 0!==r[t]?i-a:void 0}}(r,e,t);null!=o&&(n[r]=o)})),n}function N(e,t){window.__RADAR_PERFORMANCE_TIMING_[e]=t}window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{};var U={fmp:{end:"radarFmp",start:"navigationStart",notCatchIfUndefined:!0},redirect:{end:"redirectEnd",start:"redirectStart"},dns:{end:"domainLookupEnd",start:"domainLookupStart"},dns_start:{end:"domainLookupEnd",start:"navigationStart"},tcp:{end:"connectEnd",start:"connectStart"},tcp_start:{end:"connectEnd",start:"navigationStart"},redirect_count:{custom:function(e,t){return t.redirectCount}},ssl:{custom:function(e,t){return t.requestStart&&t.secureConnectionStart?t.requestStart-t.secureConnectionStart:0}},ssl_start:{custom:function(e,t){return t.secureConnectionStart&&t.redirectStart?t.secureConnectionStart-t.redirectStart:0}},ttfb:{end:"responseStart",start:"requestStart"},request_start:{end:"requestStart",start:"navigationStart"},trans:{end:"responseEnd",start:"responseStart"},dom_parse:{end:"domInteractive",start:"responseEnd"},dom_parse_start:{end:"domInteractive",start:"navigationStart"},blank:{end:"domLoading",start:"navigationStart"},fb:{end:"responseStart",start:"navigationStart"},dom_ready:{end:"domContentLoadedEventEnd",start:"navigationStart"},total:{end:"loadEventStart",start:"navigationStart"},resource:{end:"loadEventStart",start:"domInteractive"},fp:{custom:function(){if("function"!=typeof performance.getEntries)return null;var e=performance.getEntries().filter((function(e){return"first-paint"===e.name}))[0];return e?Math.ceil(e.startTime+e.duration):null}},js_cost:{custom:function(){if("function"!=typeof performance.getEntries)return null;var e=(t="undefined"!=typeof window?window.performance:null)&&t.timing?"function"==typeof t.timing.toJSON?((e=t.timing.toJSON())&&!e.redirectStart&&(e.redirectStart=e.navigationStart),e):t.timing:null,t=performance.getEntries(),n=performance.getEntries().filter((function(e){return"navigation"===e.initiatorType}))[0];return t=t.filter((function(e){return"script"===e.initiatorType}))[0],n=n?n.domComplete:e.domComplete-e.navigationStart,t&&n?n-t.fetchStart:null}},tti:{end:"domInteractive",start:"navigationStart"},protocol:{custom:function(e,t){return t.nextHopProtocol}},dom_num:{custom:function(){return"undefined"!=typeof document&&void 0!==document.all?document.all.length:null}}},D={main_api:{end:"apiEnd",start:"apiStart",notCatchIfUndefined:!0},main_api_offset:{end:"apiStart",start:"domLoading",notCatchIfUndefined:!0}},x={navigation_start_time:"navigationStart",fetch_start_time:"fetchStart",dns_start_time:"domainLookupStart",dns_end_time:"domainLookupEnd",connect_start_time:"connectStart",ssl_start_time:"secureConnectionStart",request_start_time:"requestStart",response_start_time:"responseStart",response_end_time:"responseEnd",dom_interactive_time:"domInteractive",dom_loading_time:"domLoading",dom_ready_time:"domContentLoadedEventEnd",load_event_time:"loadEventStart",fmp_time:"radarFmp",connect_end_time:"connectEnd",load_event_end:"loadEventEnd",dom_complete:"domComplete",dom_content_loaded_event_start:"domContentLoadedEventStart",redirect_start:"redirectStart",redirect_end:"redirectEnd"};function j(e,t){if(!Array.isArray(t))return-1;var n=-1;return t.forEach((function(t,r){new RegExp(t).test(e)&&(n=r)})),n}var M=function(e){function t(t){var n=this;if((n=e.call(this,{core:null==t?void 0:t.core,key:S.LOAD,version:"1.0.4-alpha.0",name:"RadarNavigationCollect"})||this).hasFMP=!1,n.customKeys=[],n.custom_metric={},n.mainApiList=[],n.mainApiResult=[],n.radarMainApiList=[],n.mainJSList=[],n.radarMainJSList=[],n.radarFmp=0,n.h5Load=function(){var e=n.getPerformanceData();n.collect(E({event_name:"h5_load"},e))},n.addMainApiResult=function(e){if(!n.mainApiList||0===n.mainApiList.length)return!1;var t,r,o,i,a,s,c,l,u,d,p,f,h,v,g,m,y,_,w=j((e.api||"").replace(/\?.+$/,""),n.mainApiList);return-1<w&&(null==(_=n.mainApiResult[w])||!_.url)&&(_=e.api,t=e.status,r=e.size,o=e.cached,i=e.method,l=e.perfTime,e.has_fmp,a=e.protocol,s=e.custom_failed,c=e.res_type,y=k(e,["api","status","size","cached","method","perfTime","has_fmp","protocol","custom_failed","res_type"]),n.mainApiResult[w]=E({url:_,status:t,size_radar:r,cached:o,method:i,perfTime:l,protocol:a,res_type:c,custom_failed:s},y),l=y.fb,u=y.dns,d=y.tcp,p=y.ssl,f=y.ttfb,h=y.trans,v=y.total,g=y.fetch_start,m=y.duration,y=y.start_time,n.radarMainApiList[w]={api:_,status:t,size:r,cached:o,method:i,protocol:a,fb:l,dns:u,tcp:d,ssl:p,ttfb:f,trans:h,total:v,fetch_start:g,duration:m,custom_failed:s,res_type:c,reg:n.mainApiList[w],start_time:y},n.hasFMP&&!e.has_fmp&&(_=n.getPerformanceData(),n.collect(E({event_name:"main_api"},_)))),n.mainApiResult.filter((function(e){return!!e.url})).length!==n.mainApiList.length},n.addMainJSResult=function(e){if(!n.mainJSList||0===n.mainJSList.length)return!1;var t,r,o,i,a,s,c,l,u,d,p,f,h,v,g,m=j((e.file||"").replace(/\?.+$/,""),n.mainJSList);return-1<m&&!n.radarMainJSList[m].file&&(t=e.file,r=e.cached,o=e.size,i=e.failed,a=e.res_type,s=e.protocol,c=e.fb,l=e.dns,u=e.tcp,d=e.ssl,p=e.ttfb,f=e.trans,h=e.total,v=e.fetch_start,g=e.duration,e=e.start_time,n.radarMainJSList[m]={file:t,cached:r,size:o,failed:i,res_type:a,protocol:s,fb:c,dns:l,tcp:u,ssl:d,ttfb:p,trans:f,total:h,fetch_start:v,duration:g,start_time:e,reg:n.mainJSList[m]}),n.radarMainJSList.filter((function(e){return!!e.file})).length!==n.mainJSList.length},t.core)return"string"==typeof t.mainApi?(n.mainApiList.push(t.mainApi),n.mainApiResult.push({}),n.radarMainApiList.push({})):Array.isArray(t.mainApi)&&(3<t.mainApi.length&&L("207"),n.mainApiList=t.mainApi.slice(0,3),n.mainApiList.forEach((function(){n.mainApiResult.push({}),n.radarMainApiList.push({})}))),"string"==typeof t.mainJs?(n.mainJSList.push(t.mainJs),n.radarMainJSList.push({})):Array.isArray(t.mainJs)&&(3<t.mainJs.length&&L("211"),n.mainJSList=t.mainJs.slice(0,3),n.mainJSList.forEach((function(){return n.radarMainJSList.push({})}))),performance.timing?n.init():(null!=(t=n.core)&&t.collect("event",{name:"radar_error",message:"performance.timing 不存在无法收集数据",category:S.LOAD}),C("208")),n;throw new Error("radar error 212")}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return b(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t.prototype.init=function(){N("radar_init_time",Date.now());var e,t,n,r=this.getPerformanceData();this.collect(E({event_name:"radar_init"},r)),0<performance.timing.loadEventStart?this.h5Load():(r=window,e="load",t=this.h5Load,"attachEvent"in r?r.attachEvent("on"+e,t):r.addEventListener(e,t,n))},t.prototype.customStage=function(e,t){if(void 0===t&&(t={sendTimeline:!0}),this.isHit){if(void 0===e||"string"!=typeof e||""===e)throw new Error("radar error 206");var n=Date.now();this.customKeys.includes(e)?C("204，".concat(e)):2<this.customKeys.length?C("205，".concat(e)):n&&(this.custom_metric[e+"_time"]=n,this.customKeys.push(e),t&&1==t.sendTimeline&&(t=this.getPerformanceData(),this.collect(E(E({},t),{stack:(new Error).stack,event_name:"h5_custom_metric",custom_metric_name:e,custom_metric_timestamp:n}))))}else C("200")},t.prototype.fmp=function(e){if(this.isHit){if(void 0!==e&&"number"!=typeof e)throw new Error("radar error 201");if(void 0!==e&&e<performance.timing.navigationStart)return L("210"),void(null!=(t=this.core)&&t.collect("event",{name:"radar_fmp_error",extra_info:{time:e,navigationStart:performance.timing.navigationStart}}));var t=e||Date.now();this.hasFMP?L("202"):t&&performance.timing&&(this.hasFMP=!0,this.radarFmp=t,N("fmp_time",performance.timing.radarFmp=t),e=this.getPerformanceData(),performance.timing.fmp=e.fmp,N("fmp",e.fmp),this.collect(E({event_name:"fmp",stack:(new Error).stack},e)))}else L("200")},t.prototype.getPerformanceData=function(){var e,t=R(E(E({},U),D),performance.timing);return Object.keys(x).forEach((function(e){0<=performance.timing[x[e]]&&(t[e]=performance.timing[x[e]])})),t.radar_main_js_start=null==(e=window.__RADAR_PERFORMANCE_TIMING_)?void 0:e.radar_main_js_start,t.radar_init_time=null==(e=window.__RADAR_PERFORMANCE_TIMING_)?void 0:e.radar_init_time,t},t.prototype.destroy=function(){var e,t,n,r;e=window,t="load",n=this.h5Load,"attachEvent"in e?e.detachEvent("on"+t,n):e.removeEventListener(t,n,r),this.customStage=this.fmp=function(){L("213")}},t.prototype.collect=function(e){var t=e.protocol,n=e.event_name,r=(e.custom_metric,e.custom_metric_name),o=e.stack;e.main_api_list,e=k(e,["protocol","event_name","custom_metric","custom_metric_name","stack","main_api_list"]),this.baseCollect({value:e,dimension:{protocol:t,event_name:n,custom_metric_name:r,custom_metric:JSON.stringify(this.custom_metric),main_api_list:this.mainApiResult,radar_main_api_list:this.radarMainApiList,radar_main_js_list:this.radarMainJSList,is_official:1,stack:o}}),null!=(e=this.core)&&e.flush()},t}(T),H=function(e,t){return(H=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},G=function(){return(G=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function B(e,t){var n={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}var W,K=function(){return(K=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function V(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function F(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function J(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,F([console,"radar:"],V(e),!1))}catch(e){return}}function z(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.error;n&&n.call.apply(n,F([console,"radar:"],V(e),!1))}catch(e){return}}W={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},Y.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},Y.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===W.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return J("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?(J("115，".concat(this.key)),t):n},Y.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},Y.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return K(K({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,K(K({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):J("110,".concat(this.key))};var q=Y;function Y(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}function Q(e,t){var n={};return Object.keys(e).forEach((function(r){var o=function e(t,n,r){var o=n[t];if(!o)throw new Error("【Radar Util】 perf key ".concat(t," is unexpected!"));if(!o.cachedSkip||!e("cached",n,r)){if("function"==typeof o.custom)return o.custom(t,r);n=o.end,t=o.start;var i=+r[n]||0,a=+r[t]||0;return!o.notCatchIfUndefined||void 0!==r[n]&&void 0!==r[t]?i-a:void 0}}(r,e,t);null!=o&&(n[r]=o)})),n}var X=/^(https?:)?\/\//;function $(e,t){var n;if(e)return n=e.replace(X,""),t.some((function(e){return 0<=n.indexOf(e.replace(X,""))}))}function Z(e){if("string"!=typeof e)return"";try{var t=e.lastIndexOf(".");return e.substr(t+1)}catch(e){return""}}function ee(){return 0<performance.timing.radarFmp}function te(e){return"__radar_".concat(e=void 0===e?"":e).concat(Math.random().toString(36).substring(2,8))}function ne(e,t){var n=window.XMLHttpRequest;return n?(window.XMLHttpRequest=function(){var r,o,i=new n,a=(i._radarRequest=Object.create(null),i._radarResponse=Object.create(null),te());return i._radarRequest.id=a,i.addEventListener("readystatechange",(function(){if(4===i.readyState&&"function"==typeof t){i._radarResponse.status=i.status,i._radarResponse.statusText=i.statusText;var e,n,r,o=function(e,t){switch(null==t&&(t={}),typeof e){case"object":var n,r=[];for(n in e){var o=e[n],i=n.toLowerCase();r.push("".concat(i,":\t").concat(o))}return r.join("\n")+"\n";case"string":for(var a=0,s=(r=e.split("\n")).length;a<s;a++){var c=r[a];/([^:]+):\s*(.+)/.test(c)&&(c=null!=(c=RegExp.$1)?c.toLowerCase():void 0,o=RegExp.$2,c&&null==t[c]&&(t[c]=o))}return t}}(i.getAllResponseHeaders());for(e in o)r=o[e],i._radarResponse&&i._radarResponse.headers&&!i._radarResponse.headers[e]&&(n=e.toLowerCase(),i._radarResponse.headers[n]=r);if(i.responseType&&"text"!==i.responseType)"document"===i.responseType?(i._radarResponse.xml=i.responseXML,i._radarResponse.data=i.responseXML):i._radarResponse.data=i.response;else{i._radarResponse.text=i.responseText,i._radarResponse.data=i.responseText;try{i._radarResponse.xml=i.responseXML}catch(e){}}"responseURL"in i&&(i._radarResponse.finalUrl=i.responseURL),t(i._radarRequest,i._radarResponse)}})),"function"==typeof i.open&&(r=i.open,i.open=function(e,t,n,o,i){var a=[].slice.call(arguments);return this._radarRequest=K({method:e,url:t,async:n,user:o,pass:i},this._radarRequest),this._radarResponse.headers={},r.apply(this,a)}),"function"==typeof i.send&&(o=i.send,i.send=function(){var t=[].slice.call(arguments);return"function"==typeof e&&(this._radarRequest._config=t[0],e(this._radarRequest)),o.apply(this,t)}),i},["DONE","HEADERS_RECEIVED","LOADING","OPENED","UNSENT"].forEach((function(e){window.XMLHttpRequest[e]=n[e]})),window.XMLHttpRequest.prototype=n.prototype,function(){n&&(window.XMLHttpRequest=n)}):(z("118"),!1)}function re(e,t){var n=function(e){if("function"!=typeof e.fetch)return null;var t,n=e.fetch;return e.fetch=(t=e.fetch,function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=oe.reduce((function(e,t){return[t].concat(e)}),[]),o="string"==typeof t[0]?new Request(t[0],t[1]):t[0],i=(o.id=te(),r.forEach((function(e){e=e.request,o=e(o)||o})),o.clone()),a=(e=e.apply(void 0,F([],V(t),!1)),null),s=null,c=!1;return e.then((function(e){var t,n=e.headers.get("content-type");return n&&ie.some((function(e){return-1<n.indexOf(e)}))?(c=!0,t=e.clone(),a=t.clone(),t.clone().text()):(a=e,"[object Object]")})).then((function(e){s=e;try{return i.text()}catch(e){return"Radar: Request.text()失败"}})).then((function(e){o._config=e;var t=null;return c?(t=a.clone()).data=s:t=a,r.forEach((function(e){(e=e.response)&&(t=e(t,o)||t)})),a}),(function(e){throw r.forEach((function(t){(t=t.responseError)&&(e=t(e,o)||e)})),e}))}.apply(void 0,F([t],V(e),!1))}),{register:function(e){return oe.push(e),function(){var t=oe.indexOf(e);0<=t&&oe.splice(t,1)}},clear:function(){oe=[]},unRegister:function(){n&&(e.fetch=n)}}}("function"==typeof importScripts?self:window);return n&&n.register({request:function(t,n){"function"==typeof e&&e(t)},response:function(e,n){"function"==typeof t&&t(n,e)},responseError:function(e,n){"function"==typeof t&&t(n,{status:0,statusText:e})}}),null==n?void 0:n.unRegister}var oe=[],ie=["application/json","text/html","text/plain"],ae=["//wlog.kuaishou.com/rest/n/log/web/collect","/rest/wd/common/log/collect/misc2","/rest/wd/common/log/collect/radar","/rest/kd/log/collect"];window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{};var se={start_time:{custom:function(e,t){return Math.round(t.startTime)}},fetch_start:{custom:function(e,t){return Math.round(t.fetchStart)}},dns:{end:"domainLookupEnd",start:"domainLookupStart"},tcp:{end:"connectEnd",start:"connectStart"},ssl:{custom:function(e,t){return 0===t.secureConnectionStart?0:t.connectEnd-t.secureConnectionStart}},ttfb:{end:"responseStart",start:"requestStart"},trans:{end:"responseEnd",start:"responseStart"},fb:{end:"responseStart",start:"fetchStart"},total:{end:"responseEnd",start:"fetchStart"},size:{custom:function(e,t){return t.encodedBodySize},noThrottle:!0},protocol:{custom:function(e,t){return t.nextHopProtocol}},file:{custom:function(e,t){return t.name},noThrottle:!0},res_type:{custom:function(e,t){return t.initiatorType}},cached:{custom:function(e,t){return 0===t.transferSize||(void 0===t.transferSize||null==t.transferSize)&&0===t.domainLookupStart},noThrottle:!0}},ce={navigation_start:"startTime",redirect_start:"redirectStart",redirect_end:"redirectEnd",dns_start:"domainLookupStart",dns_end:"domainLookupEnd",connect_start:"connectStart",connect_end:"connectEnd",ssl_start:"secureConnectionStart",request_start:"requestStart",response_start:"responseStart",response_end:"responseEnd"};function le(e){var t="";if("string"==typeof e)t=e;else try{t=JSON.stringify(e)}catch(n){t=String(e)}for(var n=0,r=0;r<t.length;r++){var o=t.charCodeAt(r);n+=o<128?1:o<2048?2:o<65536?3:o<1<<21?4:o<1<<26?5:o<1<<31?6:Number.NaN}return n}var ue,de=function(e){function t(t){var n,r=e.call(this,{core:null==(t=void 0===t?{}:t)?void 0:t.core,sampling:null==t?void 0:t.sampling,key:W.API,version:"1.0.4-alpha.0",name:"RadarAPICollect"})||this;if(r.ignoreList=ae,r.queue=[],r.logList=[],r.perfList=[],r.broadcastApi=(n=[],{on:i,off:o,once:function(e){var t=i((function(n){e(n),t()}));return t},emit:function(e){n.forEach((function(t){return t(e)}))}}),r.beforeHook=function(e){var t,n,o;$(e.url,r.ignoreList)||"js"===Z(e.url)||"css"===Z(e.url)||(e={api:(t=e.url,n=(o=document.location).protocol,o=o.host,0===t.indexOf("//")?n+t:0===t.indexOf("/")?"".concat(n,"//").concat(o).concat(t):t),method:e.method,request:e,startTime:Date.now()},r.queue.push(e))},r.afterHook=function(e,t){if(o=r.queue.find((function(t){return t.request===e}))){delete o.request;var n=o.startTime,o=B(o,["startTime"]),i=Date.now(),a=i-n,s=t.status;if("function"==typeof r.customizeRadarStatus)try{s=r.customizeRadarStatus(t)}catch(t){console.error(t)}var c,l,u,d,p,f,h=r.customHook({request:e,response:t,duration:a}),v=(v=t.headers)?"function"==typeof v.get?+v.get("Content-Length")||0:+v["content-length"]||0:0;a=G(G({duration:a,logTime:Date.now(),status:s,size:+v,content_type:(c=t.headers)?"function"==typeof c.get?c.get("Content-Type"):c["content-type"]:null,custom_failed:!(200<=s&&s<300)},o),h),t.finalUrl&&(a.api=t.finalUrl),"string"==typeof t.text&&(a.responseData=t.text),0!==G(G({},t),{status:a.status}).status||h.intercept_report?(ee()||(p=performance.timing.apiStart,performance.timing.apiStart=p?Math.min(p,n):n,f=performance.timing.apiEnd,performance.timing.apiEnd=f?Math.max(f,i):i),a.has_fmp=ee(),r.mergeAPIPerf(null,a)||r.logList.push(a)):(v=a.res_type,s=a.protocol,o=a.api,t=a.custom_failed,h=a.status,c=a.cached,n=a.method,i=a.response_code,l=a.response_msg,u=a.content_type,d=a.broadcast_info,p=a.responseData,(f=B(a,["res_type","protocol","api","custom_failed","status","cached","method","response_code","response_msg","content_type","broadcast_info","responseData"])).size&&0!=f.size||!p||(f.size=le(p)),r.baseCollect({value:f,dimension:{res_type:v,protocol:s,api:o,custom_failed:t,status:h,cached:c,method:n,response_code:i,response_msg:l,content_type:u,broadcast_info:d,is_official:1}})),r.queue=r.queue.filter((function(t){return t.request&&t.request!==e}))}},void 0===t||null==t||!t.core)throw new Error("radar error 310");function o(e){-1!==(e=n.indexOf(e))&&n.splice(e,1)}function i(e){return n.push(e),o.bind(void 0,e)}if(void 0!==t.ignoreList){if(!Array.isArray(t.ignoreList))throw new Error("radar error 309");t.ignoreList.forEach((function(e){if("string"!=typeof e)throw new Error("radar error 309")})),r.ignoreList=r.ignoreList.concat(t.ignoreList)}return t.core.ignoreList&&(r.ignoreList=r.ignoreList.concat(t.core.ignoreList)),r.APIHook=t.APIHook,r.customizeRadarStatus=t.customizeRadarStatus,r.init(),r}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return H(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t.prototype.init=function(){var e,t=this;try{this.observer=new PerformanceObserver((function(e){return t.observeAPI(e)})),this.observer.observe({entryTypes:["resource"]}),this.unhookXML=ne((function(e){return t.beforeHook(e)}),(function(e,n){return t.afterHook(e,n)})),this.unhookFetch=re((function(e){return t.beforeHook(e)}),(function(e,n){return t.afterHook(e,n)}))}catch(t){z("308"),null!=(e=this.core)&&e.collect("event",{name:"radar_error",message:"PerformanceObserver 不存在无法收集数据",category:W.API})}},t.prototype.customHook=function(e){var t={};if(void 0===this.APIHook)return t;if("function"!=typeof this.APIHook)return J("300"),t;var n={};try{n=this.APIHook(e)}catch(e){return J("301"),{}}if(!n)return J("302"),t;e=n.response_msg;var r=n.response_code,o=n.status,i=n.custom_failed,a=n.intercept_report;if(n=n.broadcast_info,!0===Boolean(a)&&(t.intercept_report=!0),void 0!==e)try{var s=JSON.stringify(e);100<s.length&&J("307"),t.response_msg=s.slice(0,100)}catch(e){t.response_msg="response_msg返回结果异常",J("303")}return void 0!==r&&(a=Number(r),isNaN(a)?J("304"):t.response_code=a),void 0!==o&&(e=Number(o),isNaN(e)?J("305"):t.status=e),void 0!==i&&(!0===i||!1===i?t.custom_failed=i:J("306")),t.broadcast_info=n,t},t.prototype.observeAPI=function(e){var t=this;e.getEntriesByType("resource").forEach((function(e){if(0<t.ignoreList.length&&$(e.name,t.ignoreList))return!1;var n,r,o,i;"resource"!==e.entryType||"fetch"!==e.initiatorType&&"xmlhttprequest"!==e.initiatorType||"js"===Z(e.name)||"css"===Z(e.name)||(n=Q(se,e),0<(i=e.serverTiming||[]).length&&(r=null,i.forEach((function(e){"total-timing"===e.name&&(r=e.duration)})),n.total_server_timing=r,n.server_data_list=JSON.stringify(i)),n.perfTime=Date.now(),o={encoded_body_size:e.encodedBodySize,decoded_body_size:e.decodedBodySize},Object.keys(ce).forEach((function(t){o[t]=Number(e[ce[t]]+performance.timing.navigationStart)})),i=G(G(G({},n),{api:n.file}),o),t.mergeAPIPerf(i)||t.perfList.push(i))}))},t.prototype.destroy=function(){this.unhookXML&&this.unhookXML(),this.unhookFetch&&this.unhookFetch(),this.observer&&this.observer.disconnect()},t.prototype.mergeAPIPerf=function(e,t){var n=e||t;if(t=e?this.logList:this.perfList,n){var r,o,i,a,s,c,l,u,d,p,f,h=-1,v=(n.api||"").replace(/\?.+$/,""),g=n.size;if(t.forEach((function(e,t){var r=(e.api||"").replace(/\?.+$/,"");v===r&&(Object.assign(n,e,n.apiTime),delete n.file,g&&(n.size=g),n.size&&0!=n.size||!n.responseData||(n.size=le(n.responseData)),delete n.responseData,h=t)})),-1!==h&&(t.splice(h,1),!0!==n.intercept_report))return e=n.res_type,t=n.protocol,r=n.api,o=n.custom_failed,i=n.status,a=n.cached,s=n.method,c=n.response_code,l=n.response_msg,n.has_fmp,u=n.server_data_list,d=n.content_type,p=n.broadcast_info,f=B(n,["res_type","protocol","api","custom_failed","status","cached","method","response_code","response_msg","has_fmp","server_data_list","content_type","broadcast_info"]),this.baseCollect({dimension:{res_type:e,protocol:t,api:r,custom_failed:o,status:i,cached:a,method:s,response_code:c,response_msg:l,content_type:d,broadcast_info:p,is_official:1,server_data_list:u},value:f}),this.broadcastApi.emit(JSON.parse(JSON.stringify(n))),!0}return!1},t}(q),pe=function(e,t){return(pe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},fe=function(){return(fe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},he=function(){return(he=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function ve(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function ge(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function me(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,ge([console,"radar:"],ve(e),!1))}catch(e){return}}function ye(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.error;n&&n.call.apply(n,ge([console,"radar:"],ve(e),!1))}catch(e){return}}function _e(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}function we(e,t){var n={};return Object.keys(e).forEach((function(r){var o=function e(t,n,r){var o=n[t];if(!o)throw new Error("【Radar Util】 perf key ".concat(t," is unexpected!"));if(!o.cachedSkip||!e("cached",n,r)){if("function"==typeof o.custom)return o.custom(t,r);n=o.end,t=o.start;var i=+r[n]||0,a=+r[t]||0;return!o.notCatchIfUndefined||void 0!==r[n]&&void 0!==r[t]?i-a:void 0}}(r,e,t);null!=o&&(n[r]=o)})),n}ue={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},_e.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},_e.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===ue.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return me("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?(me("115，".concat(this.key)),t):n},_e.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},_e.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return he(he({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,he(he({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):me("110,".concat(this.key))},T=_e;var be=/^(https?:)?\/\//;function Ee(e){if("string"!=typeof e)return"";try{var t=e.lastIndexOf(".");return e.substr(t+1)}catch(e){return""}}var ke=["//wlog.kuaishou.com/rest/n/log/web/collect","/rest/wd/common/log/collect/misc2","/rest/wd/common/log/collect/radar","/rest/kd/log/collect"];window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{};var Se,Oe={start_time:{custom:function(e,t){return Math.round(t.startTime)}},fetch_start:{custom:function(e,t){return Math.round(t.fetchStart)},noThrottle:!0},dns:{end:"domainLookupEnd",start:"domainLookupStart",cachedSkip:!0},tcp:{end:"connectEnd",start:"connectStart",cachedSkip:!0},ssl:{cachedSkip:!0,custom:function(e,t){return 0===t.secureConnectionStart?0:t.requestStart-t.secureConnectionStart}},ttfb:{end:"responseStart",start:"requestStart",cachedSkip:!0},trans:{end:"responseEnd",start:"responseStart",cachedSkip:!0},fb:{end:"responseStart",start:"fetchStart",cachedSkip:!0},total:{end:"responseEnd",start:"fetchStart"},duration:{end:"responseEnd",start:"fetchStart"},size:{cachedSkip:!0,custom:function(e,t){return t.encodedBodySize},noThrottle:!0},protocol:{custom:function(e,t){return t.nextHopProtocol}},file:{custom:function(e,t){return t.name},noThrottle:!0},res_type:{custom:function(e,t){return t.initiatorType}},cached:{custom:function(e,t){return 0===t.transferSize||null==t.transferSize&&0===t.domainLookupStart},noThrottle:!0}},Pe={redirect_start:"redirectStart",redirect_end:"redirectEnd",dns_start:"domainLookupStart",dns_end:"domainLookupEnd",connect_start:"connectStart",connect_end:"connectEnd",ssl_start:"secureConnectionStart",request_start:"requestStart",response_start:"responseStart",response_end:"responseEnd"},Ae=function(e){function t(t){var n,r,o=this;if((o=e.call(this,{core:null==t?void 0:t.core,sampling:null==t?void 0:t.sampling,version:"1.0.4-alpha.0",key:ue.RES,name:"RadarResourceCollect"})||this).ignoreList=ke,o.broadcastJs=(r=[],{on:a,off:i,once:function(e){var t=a((function(n){e(n),t()}));return t},emit:function(e){r.forEach((function(t){return t(e)}))}}),o.onResError=function(e){var t;(t=e).message||null!=t.lineno||(e={failed:!0,file:e.target.src||e.target.href,res_path:(t=e&&e.path||e&&e.composedPath&&e.composedPath(),Array.isArray(t)?t.map((function(e){var t=void 0===(t=e.tagName)?"":t,n=e.id;return e=e.getAttribute?e.getAttribute("class"):e.className||"",t=t.toLowerCase(),n&&(t+="#".concat(n)),e&&(t+=e.split(/\s+/g).map((function(e){return".".concat(e)})).join("")),t})).filter((function(e){return e})).join(","):"")},o.collect(e),o.broadcastJs.emit(e))},o.observeResource=function(e){e.getEntriesByType("resource").forEach((function(e){if(0<o.ignoreList.length&&function(e,t){var n;if(e)return n=e.replace(be,""),t.some((function(e){return 0<=n.indexOf(e.replace(be,""))}))}(e.name,o.ignoreList))return!1;var t,n,r;("resource"!==e.entryType||"fetch"===e.initiatorType||"xmlhttprequest"===e.initiatorType)&&("resource"!==e.entryType||"fetch"!==e.initiatorType&&"xmlhttprequest"!==e.initiatorType||"js"!==Ee(e.name)&&"css"!==Ee(e.name))||(t=we(Oe,e),n={encoded_body_size:e.encodedBodySize,decoded_body_size:e.decodedBodySize},Object.keys(Pe).forEach((function(t){n[t]=Number(e[Pe[t]]+performance.timing.navigationStart)})),r=fe(fe({failed:!1},t),n),o.collect(r),setTimeout((function(){o.broadcastJs.emit(r)})))}))},o.resourceHook=t.resourceHook,void 0!==t.ignoreList){if(!Array.isArray(t.ignoreList))throw new Error("radar error 802");t.ignoreList.forEach((function(e){if("string"!=typeof e)throw new Error("radar error 802")})),o.ignoreList=o.ignoreList.concat(t.ignoreList)}function i(e){-1!==(e=r.indexOf(e))&&r.splice(e,1)}function a(e){return r.push(e),i.bind(void 0,e)}return null!=(n=t.core)&&n.ignoreList&&(o.ignoreList=o.ignoreList.concat(t.core.ignoreList)),o.init(),o}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return pe(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t.prototype.init=function(){var e,t,n,r,o=this;try{this.observeResource(performance),this.observer=new PerformanceObserver((function(e){return o.observeResource(e)})),this.observer.observe({entryTypes:["resource"]})}catch(t){ye("801"),null!=(e=this.core)&&e.collect("event",{name:"radar_error",message:"PerformanceObserver 不存在无法收集数据",category:ue.RES})}t="error",n=function(e){return o.onResError(e)},r=!0,"attachEvent"in(e=window)?e.attachEvent("on"+t,n):e.addEventListener(t,n,r)},t.prototype.collect=function(e){var t=e.cached,n=e.failed,r=e.file,o=e.protocol,i=e.res_type,a=e.res_path;e=function(e,t){var n={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}(e,["cached","failed","file","protocol","res_type","res_path"]);try{var s=this.resourceHook&&this.resourceHook({file:r,cached:t,protocol:o,res_type:i,failed:n});if(null!=s&&s.intercept_report)return}catch(e){ye("803",e)}this.baseCollect({value:e,dimension:{cached:t,failed:n,file:r,protocol:o,res_type:i,res_path:a,sample_rate:this.sampling,is_official:1}})},t.prototype.destroy=function(){var e,t,n,r;this.isHit&&(e=window,t="error",n=this.onResError,r=!0,"attachEvent"in e?e.detachEvent("on"+t,n):e.removeEventListener(t,n,r),this.observer&&this.observer.disconnect())},t}(T),Le=function(e,t){return(Le=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},Ce=function(){return(Ce=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function Te(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([console,"radar:"],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}(e),!1))}catch(e){return}}function Ie(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}function Re(e){return{name:e,value:1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,delta:0,entries:[],id:Ge(),isFinal:!1}}function Ne(e,t){try{var n;if(PerformanceObserver.supportedEntryTypes.includes(e))return(n=new PerformanceObserver((function(e){return e.getEntries().map(t)}))).observe({type:e,buffered:!0}),n}catch(e){}}function Ue(e){Be=!e.persisted}function De(e,t,n,r){var o;return function(){n&&t.isFinal&&n.disconnect(),0<=t.value&&(r||t.isFinal||"hidden"===document.visibilityState)&&(t.delta=t.value-(o||0),(t.delta||t.isFinal||void 0===o)&&(e(t),o=t.value))}}function xe(){return void 0===je&&(je="hidden"===document.visibilityState?0:1/0,Ve((function(e){return e=e.timeStamp,je=e}),!0)),{get timeStamp(){return je}}}Se={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},Ie.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},Ie.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===Se.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return Te("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?(Te("115，".concat(this.key)),t):n},Ie.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},Ie.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return Ce(Ce({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,Ce(Ce({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):Te("110,".concat(this.key))},q=Ie,window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{};var je,Me,He,Ge=function(){return"".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)},Be=!1,We=!1,Ke=function(){addEventListener("pagehide",Ue),addEventListener("beforeunload",(function(){}))},Ve=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];We||(Ke(),We=!0),addEventListener("visibilitychange",(function(t){t=t.timeStamp,"hidden"===document.visibilityState&&e({timeStamp:t,isUnloading:Be})}),{capture:!0,once:t})},Fe=[{name:"fid",collectFn:function(e){function t(e){e.startTime<r.timeStamp&&(n.value=e.processingStart-e.startTime,n.entries.push(e),n.isFinal=!0,i())}var n=Re("FID"),r=xe(),o=Ne("first-input",t),i=De(e,n,o);o?Ve((function(){o.takeRecords().map(t),o.disconnect()}),!0):window.perfMetrics&&window.perfMetrics.onFirstInputDelay&&window.perfMetrics.onFirstInputDelay((function(e,t){t.timeStamp<r.timeStamp&&(n.value=e,n.isFinal=!0,n.entries=[{entryType:"first-input",name:t.type,target:t.target,cancelable:t.cancelable,startTime:t.timeStamp,processingStart:t.timeStamp+e}],i())}))}},{name:"lcp",collectFn:function(e){function t(e){var t=e.startTime;t<i.timeStamp?(o.value=t,o.entries.push(e)):o.isFinal=!0,n()}var n,r=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=Re("LCP"),i=xe(),a=Ne("largest-contentful-paint",t);a&&(n=De(e,o,a,r),r=function(){o.isFinal||(a.takeRecords().map(t),o.isFinal=!0,n())},(Me=Me||new Promise((function(e){return["scroll","keydown","pointerdown"].map((function(t){addEventListener(t,e,{once:!0,passive:!0,capture:!0})}))}))).then(r),Ve(r,!0))}},{name:"cls",collectFn:function(e){function t(e){e.hadRecentInput||(o.value+=e.value,o.entries.push(e),n())}var n,r=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=Re("CLS",0),i=Ne("layout-shift",t);i&&(n=De(e,o,i,r),Ve((function(e){e=e.isUnloading,i.takeRecords().map(t),e&&(o.isFinal=!0),n()})))}},{name:"fcp",collectFn:function(e){var t,n=Re("FCP"),r=xe(),o=Ne("paint",(function(e){"first-contentful-paint"===e.name&&e.startTime<r.timeStamp&&(n.value=e.startTime,n.isFinal=!0,n.entries.push(e),t())}));o&&(t=De(e,n,o))}}],Je=function(e){function t(t){var n=e.call(this,{core:t.core,sampling:t.sampling,version:"1.0.4-alpha.0",key:Se.EVENT,name:"RadarChromeMetricsCollect"})||this;return n.options={lcp:!1,cls:!1,fid:!1,fcp:!1},n.init=function(){var e=n.options;Fe.forEach((function(t){var r=t.name;e&&!e[r]||t.collectFn((function(e){var t,r,o=e.name;e=e.value,t=o.toLocaleLowerCase(),r=Number(e),window.__RADAR_PERFORMANCE_TIMING_[t]=r,n.baseCollect({dimension:{name:o.toLocaleLowerCase(),is_official:1},value:{duration:Number(e)}})}))}))},Object.assign(n.options,t.options),Fe.forEach((function(e){t="radar_"+e.name;var t=(t=new RegExp("(?:^|; ?)".concat(t,"=(1|0)(;|$)")).exec(document.cookie))&&t[1];t&&(n.options[e.name]="1"===t)})),n.init(),n}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return Le(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t}(q),ze=function(e,t){return(ze=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},qe=function(){return(qe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},Ye=function(){return(Ye=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function Qe(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function Xe(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function $e(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,Xe([console,"radar:"],Qe(e),!1))}catch(e){return}}function Ze(){var e=window.navigator.userAgent,t={};return e.split(" ").map((function(e){e=e.split("/"),t[e[0]]=e[1]})),t}He={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},tt.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},tt.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===He.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return $e("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?($e("115，".concat(this.key)),t):n},tt.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},tt.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return Ye(Ye({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,Ye(Ye({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):$e("110,".concat(this.key))},T=tt;var et=((q={}).RADAR_INIT="radar_init",q.H5_LOAD="h5_load",q.FMP="fmp",q.MAIN_API="main_api",q.H5_CUSTOM_METRIC="h5_custom_metric",window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{},function(e){function t(t){var n=e.call(this,{core:t.core,sampling:t.sampling,key:He.EVENT,version:"1.0.4-alpha.0",name:"RadarEventCollect"})||this;return n.action={start:function(e){n._checkActionDimension(e),n.event({name:e.name,extra_info:e.extra_info,event_type:"radar_action_start"})},end:function(e){n._checkActionDimension(e),n.event({name:e.name,extra_info:e.extra_info,result_type:e.result_type,event_type:"radar_action_end"})}},n}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return ze(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t.prototype.event=function(e,t){if(this.isHit){if(!e)throw new Error("radar error 402");if(void 0===e.name||"string"!=typeof e.name)throw new Error("radar error 402");if(127<e.name.length&&(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.error;n&&n.call.apply(n,Xe([console,"radar:"],Qe(e),!1))}catch(e){return}}("403"),e.name=e.name.slice(0,127)),e.is_official=1,t&&void 0!==t.duration&&("number"!=typeof t.duration||t.duration<0))throw new Error("radar error 404");if(t&&void 0!==t.event_count&&("number"!=typeof t.event_count||t.event_count<0))throw new Error("radar error 405，".concat(t.event_count));var n=Ze();void 0===e.yoda_version&&(e.yoda_version=n.Yoda||""),void 0===e.webview_type&&(e.webview_type=-1!==(r=window.navigator.userAgent).indexOf("Android")?"WebView":-1!==r.indexOf("iPhone")?(r=Ze()).Yoda&&"WK"===r.WebViewType?"YodaWKWebView":r.Yoda||"WKWebView"!==r.WebView?"UIWebView":"WKWebView":""),n={dimension:e,value:t},e&&e.extra_info&&"object"==typeof e.extra_info&&(n={dimension:qe(qe({},e),{extra_info:JSON.stringify(e.extra_info)}),value:t}),this.baseCollect(n)}else $e("401");var r},t.prototype.destroy=function(){this.event=function(){$e("407")},this.action={start:function(){$e("407")},end:function(){$e("407")}}},t.prototype._checkActionDimension=function(e){if(!e)throw Error("radar error 408");if(void 0!==e.extra_info&&!(e.extra_info instanceof Object))throw new Error("radar error 406")},t}(T));function tt(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}var nt,rt,ot=function(e,t){return(ot=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},it=function(){return(it=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},at=function(){return(at=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function st(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{var n=null===console||void 0===console?void 0:console.warn;n&&n.call.apply(n,function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([console,"radar:"],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}(e),!1))}catch(e){return}}function ct(e){if(void 0===(e=void 0===e?{}:e))throw new Error("radar error 100");if(!e.key)throw new Error("radar error 101");if(e.core&&("object"!=typeof e.core.sampleData||"function"!=typeof e.core.collect))throw new Error("radar error 121");this.baseOptions=e,this.key=e.key,this.version=e.version,this.name=e.name,this.isHit=!1,e.core&&this.updateCore(e.core)}function lt(e,t,n,r){"attachEvent"in e?e.attachEvent("on"+t,n):e.addEventListener(t,n,r)}function ut(e,t,n,r){"attachEvent"in e?e.detachEvent("on"+t,n):e.removeEventListener(t,n,r)}nt={LOAD:"load",RES:"resource",API:"api",ERROR:"error",SDK_ERROR:"sdk_error",CUSTOM:"custom",BATCH:"batch",EVENT:"event",BLOOD:"blood"},ct.prototype.updateCore=function(e){if("object"!=typeof e.sampleData||"function"!=typeof e.collect)throw new Error("radar error 121");this.core=e,this.sampling=this.getSample(this.baseOptions.sampling),this.sampling&&(this.isHit=this.samplingControl(this.sampling))},ct.prototype.getSample=function(e){var t=(null==(t=this.core)?void 0:t.sampleData.core)||1;if(this.key===nt.LOAD)return t;if("number"!=typeof(e=void 0===e?1:e)||e<0||1<e)return st("102，".concat(this.key,"，采用Core的采样率").concat(null==(n=this.core)?void 0:n.sampleData.core)),t;var n=null!=(n=this.core)&&n.isInYoda?void 0===(null==(n=this.core)?void 0:n.sampleData[this.key])?1:this.core.sampleData[this.key]:e;return t&&t<n?(st("115，".concat(this.key)),t):n},ct.prototype.samplingControl=function(e){var t;return!(null==(t=this.core)||!t.sampleData.isHit)&&null!==(t=e/this.core.sampleData.core)&&"number"==typeof t&&Math.random()<t},ct.prototype.baseCollect=function(e){var t;this.core?this.isHit&&(t=e.value||{},t=Object.keys(t).filter((function(e){return null!==t[e]&&void 0!==t[e]})).reduce((function(e,n){return at(at({},e),((e={})[n]=t[n],e))}),{}),this.core.collect(e.key||this.key,at(at({},e.dimension),{sample_rate:this.sampling,collect_version:this.version,collect_name:this.name}),t)):st("110,".concat(this.key))},q=ct,window.__RADAR_PERFORMANCE_TIMING_=window.__RADAR_PERFORMANCE_TIMING_||{},(T=rt=rt||{}).SCRIPT="script",T.RES="res",T.API="api",T.VIDEO="video";var dt=[];function pt(e,t,n){var r,o,i,a;return(a=t)&&(a instanceof Error||"object"==typeof a&&"string"==typeof a.message)?(o=t.name||"ErrorLikeObject",i=t.message||"",r=t.stack||void 0):(a=n,i=null==t&&"Script error."===(null==a?void 0:a.message)?(o="CrossOriginError","".concat(e," - message: ").concat((null==n?void 0:n.message)||"<UNKNOWN>")):null===t&&null!=n&&n.message&&"Uncaught null"!==n.message?(o="Error","".concat(e," - message: ").concat(n.message)):(o=null==t?"".concat(t):typeof t,n=function(e){var t;try{t=e.toString()}catch(e){}return"string"!=typeof t||""===t?t=null===e?"<NULL>":void 0===e?"<UNDEFINED>":""===t?"<EMPTY>":"<UNKNOWN>":2e3<t.length&&(t=t.substring(0,2e3)+"<<< ……"),t}(t),"".concat(e," - value: ").concat(n))),{error_type:rt.SCRIPT,error_cons_type:o,msg:i,stack:r}}function ft(e){var t=e.error_cons_type,n=e.file,r=e.error_type,o=e.msg,i=e.col,a=e.line,s=e.stack;return{key:"error",value:function(e,t){var n={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}(e,["error_cons_type","file","error_type","msg","col","line","stack"]),dimension:{error_cons_type:t,file:n,error_type:r,msg:o,col:i,line:a,stack:s}}}function ht(e){for(var t,n,r=0;r<dt.length;r++)if(n=e,(t=dt[r]).msg===n.msg&&t.stack===n.stack&&t.file===n.file&&t.line===n.line&&t.col===n.col)return r;return-1}var vt=function(e){function t(t){var n=e.call(this,{core:t.core,sampling:t.sampling,key:nt.ERROR,version:"1.0.4-alpha.0",name:"RadarErrorCollect"})||this;return n.onError=function(e){var t;!(t=e).message&&null==t.lineno||(e=it(it({},pt("Global Uncaught Exception",e.error,e)),{file:e.filename,line:e.lineno,col:e.colno}),n.isSameErrorAndReport(e))},n.onPromiseError=function(e){e=pt("Unhandled Promise Rejection",e.reason),n.isSameErrorAndReport(e)},n.core=t.core,n.init(),n}var n=t,r=e;if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=n}return ot(n,r),n.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o),t.prototype.init=function(){dt.splice(0),lt(window,"error",this.onError,!0),lt(window,"unhandledrejection",this.onPromiseError)},t.prototype.destroy=function(){ut(window,"error",this.onError,!0),ut(window,"unhandledrejection",this.onPromiseError),this.captureException=function(){st("600")}},t.prototype.isSameErrorAndReport=function(e){0<=ht(e)||(dt.push(e),setTimeout((function(){dt.splice(ht(e),1)}),2e3),this.collect(e))},t.prototype.collect=function(e){e=ft(e),this.baseCollect({dimension:e.dimension,value:e.value})},t.prototype.captureException=function(e){this.isSameErrorAndReport(pt("Captured Exception",e))},t}(q);function gt(e){var t=this;this.core=new _({weblogger:e.weblog,projectId:e.projectId,sampling:e.sampling||1,httpReportFirst:e.httpReportFirst,customDimensions:e.customDimensions}),this.navigationCollect=new M({core:this.core,mainApi:e.mainApi,mainJs:e.mainJs}),e.unuseAPIPlugin||(this.apiCollect=new de({core:this.core,sampling:e.apiSampling,APIHook:e.APIHook,customizeRadarStatus:e.customizeRadarStatus,ignoreList:e.ignoreList}),this.apiCollect.broadcastApi.on((function(e){return t.navigationCollect.addMainApiResult(e)}))),e.unuseResourcePlugin||(this.resourceCollect=new Ae({sampling:e.resourceSampling,core:this.core,ignoreList:e.ignoreList,resourceHook:e.resourceHook}),this.resourceCollect.broadcastJs.on((function(e){return t.navigationCollect.addMainJSResult(e)}))),this.chromeMetricsCollect=new Je({core:this.core,options:{lcp:e.lcp,cls:e.cls,fid:e.fid,fcp:e.fcp}}),this.eventCollect=new et({core:this.core}),this.action=this.eventCollect.action,e.unuseErrorPlugin||(this.errorCollect=new vt({core:this.core}))}return gt.prototype.event=function(e,t){this.eventCollect.event(e,t)},gt.prototype.customStage=function(e,t){this.navigationCollect.customStage(e,t)},gt.prototype.setDimensions=function(e){this.core.setDimensions(e)},gt.prototype.fmp=function(e){"number"==typeof e?this.navigationCollect.fmp(e):"object"==typeof e&&e.timestamp?this.navigationCollect.fmp(e.timestamp):this.navigationCollect.fmp()},gt.prototype.captureException=function(e){var t;this.errorCollect?null!=(t=this.errorCollect)&&t.captureException(e):console.warn("RadarErrorCollect 采集器未被初始化")},gt.prototype.destroy=function(){var e;null!=(e=this.navigationCollect)&&e.destroy(),null!=(e=this.apiCollect)&&e.destroy(),null!=(e=this.resourceCollect)&&e.destroy(),null!=(e=this.eventCollect)&&e.destroy(),null!=(e=this.errorCollect)&&e.destroy()},gt}()})),Y=J,Q=new(V(F((function(e,t){!function(e,t,n){function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=r(n),i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var s=function(){return(s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function c(e){for(var t={},n=0,r=e.slice(1).split("&");n<r.length;n++){var o=(i=r[n].split("="))[0],i=i[1];o in t||(t[o]=i)}return t}function l(e){return"undefined"!=typeof document&&document.cookie&&decodeURIComponent(null===document||void 0===document?void 0:document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[-.+*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null}var u,d=c(location.search),p={product_name:n=l("kpn")||d.kpn||"KWAI",package_name:n,platform:function(){if("undefined"==typeof window)return 0;var e=navigator.userAgent||"",t=/kwai|bulldog/i.test(e),n=/apple/i.test(e),r=/android/i.test(e),o=/ipad/i.test(e);return e=/iphone/i.test(e),t?r?6:n?7:0:o?4:r?8:e?9:10}(),version_name:l("appver")||"",network_type:l("net")||"unknow",user_id:l("ud")||l("userId")||"",h5_extra_attr:{host_product:n,country_name:{country:l("countryInfo")||"",bucket:l("bucket")||""},channel:d.source||""}},f={proto:"v3",largeFile:!0,autoFmp:!1,plugins:[],immediately:!0},h=0;function v(){this.weblog="undefined"!=typeof window&&window._GLOBAL_KS_WEBLOGGER_&&window._GLOBAL_KS_WEBLOGGER_.weblog}function g(e,t,n,r){return"attachEvent"in e?e.attachEvent("on"+t,n):e.addEventListener(t,n,r)}function m(e,t,n,r){return"attachEvent"in e?e.detachEvent("on"+t,n):e.removeEventListener(t,n,r)}"undefined"!=typeof window&&"undefined"!=typeof Event&&(u=new Event("autoFmp",{bubbles:!0,cancelable:!1})||null),v.prototype.apply=function(e){this.weblog=e};var y,_=(a(w,y=d=v),w.prototype.destroy=function(){m(window,"load",this.handleLoad)},w.key="largeFile",w);function w(e,t){var n=y.call(this)||this;return n.ignoreLogList=[],n.logIndex=0,n.logMaxNum=10,n.isLargeFile=function(e){return!!(("img"===(null==e?void 0:e.initiatorType)||"css"===(null==e?void 0:e.initiatorType))&&/\.(png|jpe?g|bmp|webp|svg)$/i.test(null==e?void 0:e.name)&&102400<=(null==e?void 0:e.encodedBodySize)&&(null==e?void 0:e.encodedBodySize)<=Number.MAX_SAFE_INTEGER)},n.sendLargeFileLog=function(){performance.getEntriesByType("resource").filter((function(e){return n.isLargeFile(e)})).forEach((function(e){var t,r;if(n.logIndex<n.logMaxNum&&!n.ignoreLogList.includes(e.name))try{n.logIndex+=1,n.ignoreLogList.push(e.name);var o={name:"large_file",category:"kwai-log",event_type:"count",src:e.name,message:e.encodedBodySize,duration:e.duration};null!==(r=null===(t=n.weblog)||void 0===t?void 0:t.plugins)&&void 0!==r&&r.radar&&(console.info("[@kibt/weblogger] LargerFile: ",o.src,o.message),n.weblog.plugins.radar.event(o,{duration:e.duration}))}catch(e){console.error(e)}}))},n.handleLoad=function(){n.sendLargeFileLog(),setTimeout((function(){n.sendLargeFileLog()}),3e3)},"undefined"==typeof window||"undefined"==typeof performance||"function"!=typeof(null===performance||void 0===performance?void 0:performance.getEntries)||(null!=t&&t.ignoreList&&(n.ignoreLogList=n.ignoreLogList.concat(null==t?void 0:t.ignoreList)),g(window,"load",n.handleLoad)),n}var b,E=(a(k,b=d),k.prototype.destroy=function(){m(window,"autoFmp",this.handleAutoFmp)},k.key="autoFmp",k);function k(){var e=b.call(this)||this;return e.handleAutoFmp=function(){var t=h;t&&0<t&&(performance.timing.autoFmp=t,console.info("[@kibt/weblogger] autoFmp: ",t),e.weblog.plugins.radar.event({name:"auto_fmp",category:"kwai-log",event_type:"duration"},{duration:t}))},"undefined"==typeof window||"undefined"==typeof performance||"function"!=typeof(null===performance||void 0===performance?void 0:performance.getEntries)||g(window,"autoFmp",e.handleAutoFmp),e}function S(e,t){var n,r;null!==(n=null==t?void 0:t.params)&&void 0!==n&&n.isBusinessCoreLog&&(r=null===(n=null===(r=null===window||void 0===window?void 0:window.__kibtlog__)||void 0===r?void 0:r.logger)||void 0===n?void 0:n.reportData,n=null===(n=null===window||void 0===window?void 0:window.__kibtlog__)||void 0===n?void 0:n.logger.businessCoreDimension,r&&n&&(r=t,"PV"===(e=e.toUpperCase())&&(r.action=r.page),t=r,window.__kibtlog__.logger.reportData(n({key:t.action,value:s(s({},t.params),{name:t.action,event_type:e})}))))}function O(e,t){var n=this;void 0===e&&(e={}),void 0===t&&(t={}),this.immediately=!0,this.sendRadarLog=function(e){var t,r,o=e.duration;try{null!==(r=null===(t=n.weblog)||void 0===t?void 0:t.plugins)&&void 0!==r&&r.radar&&n.weblog.plugins.radar.event(e,{duration:o})}catch(e){console.error(e)}},this.sendJsSuccessLog=function(){var e="undefined"!=typeof window?null===window||void 0===window?void 0:window.performance:null;if(!e)return{};n.sendRadarLog({name:"js_success",category:"kwai-log",event_type:"count",duration:Date.now()-e.timing.navigationStart})},"production"===e.env&&(e.env="oversea");var r,i,a=s(s({},f),e);t=s(s({},p),t),this.weblog=new O.Entries(a,t),this.immediately=!!a.immediately,e.radar&&(e.radar.APIHook=(r=e.radar.APIHook||void 0,i=e.autoFmp,function(e){var t,n=e.response,o=e.duration,a=n.finalUrl||n.url||"";!i||h||!a.includes(window.location.host)||Date.now()-o<=(o=performance.timing).domContentLoadedEventEnd+500&&(h=Date.now()-o.navigationStart,window.dispatchEvent(u));try{t=JSON.parse(n.data)}catch(e){t={}}return r&&"function"==typeof r&&(e=r(e))&&"response_code"in e&&"response_msg"in e&&"status"in e?e:{response_code:t.result||t.code,response_msg:t.error_msg||t.message||t.msg||"",status:n.status}})),e.radar&&(e=new o.default(s(s({},a.radar),{weblog:this.weblog})),this.weblog.plugins.radar=e,this.radar=e),"undefined"!=typeof window&&(a.autoFmp&&this.weblog.plug(E),a.largeFile&&this.weblog.plug(_,a.largeFile)),this.plugins=null===(a=this.weblog)||void 0===a?void 0:a.plugins,this.weblog.sendImmediately=this.sendImmediately.bind(this),this.weblog.collect=this.collect.bind(this)}O.prototype.collect=function(e,t){this.weblog.send(e,t,!1),S(e,t)},O.prototype.sendImmediately=function(e,t){this.weblog.send(e,t,!0),S(e,t)},O.prototype.sendLog=function(e,t){this.immediately?this.sendImmediately(e,t):this.collect(e,t)},O.prototype.sendPv=function(e){var t,n=c(location.search);"string"==typeof e?this.sendLog("PV",{type:"enter",page:e,params:s({},n)}):(t=e.params||{},n&&(t=s(s({},n),t)),this.sendLog("PV",s(s({type:"enter"},e),{params:t})))},O.prototype.sendShow=function(e){"string"==typeof e?this.sendLog("SHOW",{action:e}):this.sendLog("SHOW",e)},O.prototype.sendClick=function(e){"string"==typeof e?this.sendLog("CLICK",{action:e}):this.sendLog("CLICK",e)},O.prototype.sendCustom=function(e){this.sendLog("CUSTOM",e)},(d=O).Entries=t.Weblog,e.KwaiLog=d,e.default=d,Object.defineProperty(e,"__esModule",{value:!0})}(t,Y,q)}))))({env:"production",radar:{projectId:"a5e475d36r",sampling:1}});!function(e){e.CUSTOM="custom",e.PV="pv",e.SHOW="show",e.CLICK="click",e.API_ERROR="api_error"}(z||(z={}));var X,$=function(e){var n=t(t({},e),{event_type:void 0===e.event_type?z.CUSTOM:e.event_type,result_type:void 0===e.result_type?"":"".concat(e.result_type),category:"kwai-live-activity-log"});Q.sendRadarLog(n)};!function(e){e.PRO="pro",e.SNACK="snack",e.ME="me"}(X||(X={}));var Z,ee={"m.kwai.com":"m.kslawin.com","incentive.kwai.com":"incentive.kslawin.com","app.kwai.com":"app.kslawin.com","m-wallet.kwai.com":"m-wallet.kslawin.com","m-live.kwai.com":"m-live.kslawin.com","m-creative.kwai.com":"m-creative.kslawin.com","m-shop.kwai.com":"m-shop.kslawin.com","shop.kwai.com":"shop.kslawin.com","www.kwai.com":"www.kslawin.com","hotvideo.kwai.com":"hotvideo.kslawin.com","share.kwai.com":"share.kslawin.com"},te=function(e){if(!e)return e;if(!function(){var e=window.navigator.userAgent||"";return/ISDR\/1/.test(e)}())return e;try{var t=new URL(e);return t.host&&ee[t.host]?e.replace(t.host,ee[t.host]):e}catch(t){return e}};!function(e){e[e.GREATR=1]="GREATR",e[e.EQUAL=0]="EQUAL",e[e.LESS=-1]="LESS"}(Z||(Z={}));window.kwaiLoad=function(e){Q.sendPv({page:"LIVE_BET_GAME",type:"enter",params:{GAME_TYPE:e?"".concat(e):"0"}}),$({name:"LIVEGAME-LOAD",extra_info:JSON.stringify({url:window.location.href})}),console.log("-----kwaiLoad-----")},window.kwaiOnPay=function(){if($({name:"LIVEGAME-PAY",extra_info:JSON.stringify({url:window.location.href})}),console.log("-----kwaiOnPay-----"),"true"===function(){for(var e=(location.search||"").substr(1).replace(/\?author_id/,"&author_id"),t=Object.create(null),n=e.split("&").filter(Boolean),r=0;r<n.length;r++)t[n[r].split("=")[0]]=unescape(n[r].split("=")[1]);return t}().offmusic){var e="https://m-wallet.kwai.com/main/diamond?showFirstChannel=true&hyId=trinityWallet&webview=yoda&webviewType=halfWebview&source=LIVEGAME&entrySource=LIVEGAME";(function(e,t,r){var o;void 0===t&&(t={});var i=Object.entries(t).map((function(e){var t=n(e,2),r=t[0],o=t[1];return"".concat(r,"=").concat(o)})),a=i.join("&"),s=-1!==e.indexOf("?")?"&":"?";a&&(e+=s+a),e.startsWith("/")&&(e="".concat(window.location.origin).concat(e)),B?(r.url=e,null===(o=W.component)||void 0===o||o.loadNewUrlHalfPage(r)):window.location.href=e})(e=te(e),{},{launchType:1,corner:16,height:628,fullScreen:"0",hideToolbar:!0,enableProgress:!0})}else{var r="https://m-wallet.kwai.com/main/diamond?source=LIVEGAME&hyId=trinityWallet&entrySource=LIVEGAME";(function(e,r,o){var i;void 0===r&&(r={});var a=Object.entries(r).map((function(e){var t=n(e,2),r=t[0],o=t[1];return"".concat(r,"=").concat(o)})),s=a.join("&"),c=-1!==e.indexOf("?")?"&":"?";if(s&&(e+=c+s),e.startsWith("/")&&(e="".concat(window.location.origin).concat(e)),B){var l=t(t({},{titleColor:"#222222"}),o);null===(i=W.webview)||void 0===i||i.open({url:e,launchOptions:l})}else window.location.href=e})(r=te(r))}},window.kwaiLogin=function(){$({name:"LIVEGAME-LOGIN",extra_info:JSON.stringify({url:window.location.href})}),console.log("-----kwaiLogin-----"),W.component.login()},window.kwaiClosePage=function(){$({name:"LIVEGAME-CLOSEPAGE",extra_info:JSON.stringify({url:window.location.href})}),console.log("-----kwaiClosePage-----"),W.component.backOrCloseWeb({closeWeb:!0})},window.kwaiGiftPanel=function(e){console.log("-----kwaiGiftPanel-----"),$({name:"LIVEGAME-GIFTPANEL",extra_info:JSON.stringify({url:window.location.href,giftId:e})}),W.component.backOrCloseWeb({closeWeb:!0});var t=e?"kwaiLive://giftPanel?giftId=".concat(e):"kwaiLive://giftPanel?tabId=1";W.live.startLiveInnerRouter({url:t})},window.kwaiSlide=function(){console.log("-----kwaiSlide-----"),$({name:"LIVEGAME-SLIDE",extra_info:JSON.stringify({url:window.location.href})}),window.location.href="ikwai://live/slide"},window.kwaiBackCenter=function(){console.log("-----kwaiBackCenter-----"),Q.sendClick({action:"GAME_ENTRANCE"}),$({name:"LIVEGAME-BACKCENTER",extra_info:JSON.stringify({url:window.location.href})}),W.component.backOrCloseWeb({closeWeb:!0});var e=encodeURIComponent("https://m-live.kwai.com/live/exchange?hyId=live_exchange&isLiving=true&fullScreen=1&isHalf=1&redirectUrl=bundleId%3DLiveBGCenterPanel%26isLiving%3Dtrue%26isHalf%3D1%26fullScreen%3D1%26componentName%3DLiveBGCenterPanel%26height%3D624%26width%3D0%26bgColor%3Dtransparent%26themeStyle%3D1");W.live.startLiveInnerRouter({url:"kwaiLive://webview?height=0.6&hiddenTopBar=true&url=".concat(e)})};
