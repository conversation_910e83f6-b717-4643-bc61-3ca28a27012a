!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).lobahGameSDK=t()}(this,(function(){"use strict";function e(e){var t={};return new URL(e).searchParams.forEach((function(e,n){t[n]=e})),t}function t(e){var t=document.createElement("iframe");t.setAttribute("src","lobah://".concat(e)),t.style.height="0",t.style.width="0",t.style.position="absolute",t.style.zIndex="-1000",t.style.display="none",document.body.appendChild(t),setTimeout((function(){return document.body.removeChild(t)}),5e3)}return function(){function n(){this.messageListener=null,this.isConnected=!1;var t=e(window.location.href);window.top&&window.top.postMessage({key:"g2s_sdkReady",content:null},n.targetOrigin),n.targetOrigin=t.target||"*"}return n.getInstance=function(){return n.instance||(n.instance=new n),n.instance},n.prototype.emitMsg=function(e){return this.isConnected?new Promise((function(t,o){if(window.top)if("string"!=typeof e){window.top.postMessage(e,n.targetOrigin);var i=e.key;i?(n.eventCallbackMap.set(i,t),setTimeout((function(){n.eventCallbackMap.has(e.key)&&(n.eventCallbackMap.delete(e.key),o(new Error("Event ".concat(e.key," timed out"))))}),5e3)):o(new Error("Invalid key,please refer https://portal.lobah.net/#/docs/game-sdk"))}else o(new Error("Invalid event format,please refer https://portal.lobah.net/#/docs/game-sdk"));else o(new Error("No parent window found"))})):Promise.reject(new Error("Not connected to platform"))},n.prototype.onPlatformEvent=function(e){this.isConnected?console.warn("Already connected to platform"):(this.messageListener=function(t){var o=t.data;if(o&&Object.values(e).some((function(e){return e&&"function"==typeof e}))){var i=o.key;if(n.eventCallbackMap.has(i)){var s=n.eventCallbackMap.get(i);s&&(s(o.content),n.eventCallbackMap.delete(i))}e[i]&&e[i](o.content)}},window.addEventListener("message",this.messageListener),this.isConnected=!0)},n.prototype.disconnect=function(){this.isConnected?(this.messageListener&&(window.removeEventListener("message",this.messageListener),this.messageListener=null),n.eventCallbackMap.clear(),this.isConnected=!1,console.log("Disconnected from platform")):console.warn("Not connected to platform")},n.prototype.inviteFriend=function(){t("util/inviteFriendsAssist"),console.log("show Invite Friend")},n.prototype.openHalfProfile=function(e){!function(e){t("general/openHalfProfile?uid=".concat(e))}(e),console.log("show mini profile")},n.prototype.share=function(e,n){!function(e,n){t("general/share?shareType=room&shareTitle=".concat(e,"&shareLink=").concat(encodeURIComponent(n)))}(e,n)},n.prototype.getUrlParams=function(){return e(window.location.href)},n.targetOrigin="*",n.eventCallbackMap=new Map,n}().getInstance()}));