!function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=59)}({0:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){var t=window.location.href;e=e.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)").exec(t);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null}("WEBHOST");function o(){var e=void 0;if(window.KEWLWebZip&&window.KEWLWebZip.name){var t=window.KEWLWebZip.name||"UNKNOWN";e=new URL(n+"/app/"+t+"/dist/"+window.location.href.split("/dist/")[1])}else e=new URL(window.location.toString());return window.location2=e,document.location2=e,e}window.KEWLWebZip&&(window.KEWLWebZip.WEBHOST=n),o();var i=window.location.href;function s(e){setTimeout(function(){i!==window.location.href&&(i=window.location.href,o())})}!function(e){var t=e.pushState;e.pushState=function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return"function"==typeof e.onpushstate&&e.onpushstate({args:o}),t.apply(e,o)};var n=e.replaceState;e.replaceState=function(){for(var t=arguments.length,o=Array(t),i=0;i<t;i++)o[i]=arguments[i];return"function"==typeof e.onreplacestate&&e.onreplacestate({args:o}),n.apply(e,o)}}(window.history),window.onhashchange=s,window.onpopstate=s,window.history.onpushstate=s,window.history.onreplacestate=s,t.default=o},19:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=window,i=window.Base64,s=void 0,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";if(e.exports)try{s=n(20).Buffer}catch(e){}var a=function(e){for(var t={},n=0,o=e.length;n<o;n++)t[e.charAt(n)]=n;return t}(r),p=String.fromCharCode,c=function(e){if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?p(192|t>>>6)+p(128|63&t):p(224|t>>>12&15)+p(128|t>>>6&63)+p(128|63&t)}var n=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return p(240|n>>>18&7)+p(128|n>>>12&63)+p(128|n>>>6&63)+p(128|63&n)},u=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,f=function(e){return e.replace(u,c)},l=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[r.charAt(n>>>18),r.charAt(n>>>12&63),t>=2?"=":r.charAt(n>>>6&63),t>=1?"=":r.charAt(63&n)].join("")},h=window.btoa?function(e){return window.btoa(e)}:function(e){return e.replace(/[\s\S]{1,3}/g,l)},d=s?function(e){return(e.constructor===s.constructor?e:new s(e)).toString("base64")}:function(e){return h(f(e))},g=function(e,t){return t?d(String(e)).replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,""):d(String(e))},I=new RegExp(["[脌-脽][聙-驴]","[脿-茂][聙-驴]{2}","[冒-梅][聙-驴]{3}"].join("|"),"g"),y=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return p(55296+(t>>>10))+p(56320+(1023&t));case 3:return p((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return p((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},w=function(e){return e.replace(I,y)},A=function(e){var t=e.length,n=t%4,o=(t>0?a[e.charAt(0)]<<18:0)|(t>1?a[e.charAt(1)]<<12:0)|(t>2?a[e.charAt(2)]<<6:0)|(t>3?a[e.charAt(3)]:0),i=[p(o>>>16),p(o>>>8&255),p(255&o)];return i.length-=[0,0,2,1][n],i.join("")},v=o.atob?function(e){return o.atob(e)}:function(e){return e.replace(/[\s\S]{1,4}/g,A)},m=s?function(e){return(e.constructor===s.constructor?e:new s(e,"base64")).toString()}:function(e){return w(v(e))},O=function(e){return m(String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))},S={VERSION:"2.1.9",atob:v,btoa:h,fromBase64:O,toBase64:g,utob:f,encode:g,encodeURI:function(e){return g(e,!0)},btou:w,decode:O,noConflict:function(){var e=o.Base64;return o.Base64=i,e}};t.default=S},2:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getUserInfo=t.transformRequest=t.loadJs=t.getFullUrl=t.json2url=t.sysMap=t.WinMap=t.utility=void 0;var o,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s=n(0),r=((o=s)&&o.__esModule,n(3));navigator.platform;var a=navigator.userAgent,p=(window.location2.href,window.location2.search,{copy:function(e){if(!(e instanceof Object)||"function"==typeof e)return e;var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},versionCompare:function(e,t){if(null===e)return!1;var n=parseFloat(e),o=parseFloat(t),i=e.replace(n+".",""),s=t.replace(o+".","");return n>o||!(n<o)&&i>=s},createUUID:function(e){for(var t=[],n=void 0,o=0;o<36;o++)t[o]="0123456789abcdef".substr(Math.floor(16*Math.random()),1);return t[14]="4",t[19]="0123456789abcdef".substr(3&t[19]|8,1),e?n=t.join("").substr(0,32):(t[8]=t[13]=t[18]=t[23]="-",n=t.join("")),n},buildQuery:function(e){var t=[];if("object"===(void 0===e?"undefined":i(e)))for(var n in e)e.hasOwnProperty(n)&&t.push(n+"="+encodeURIComponent(null==e[n]?"":""+e[n]));return t.join("&")},create:function(e){var t=new Image(0,0);t.onload=function(){t=null},t.src=e,t.complete&&(t=null)},isAndroid:function(){return/Android|HTC/i.test(a)},isIos:function(){return!/Android|HTC/i.test(a)&&/iPod|iPhone/i.test(a)},isMobile:function(){var e=/Android|HTC/i.test(a),t=!e&&/iPod|iPhone/i.test(a);return e||t},getQueryString:function(e){var t=window.location2.href;e=e.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)").exec(t);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null},getCookie:function(e){var t=void 0,n=void 0,o=void 0,i=void 0;if(document.cookie)for(o=0,i=(n=document.cookie.split("; ")).length;o<i;o++)if(0===n[o].indexOf(e+"=")){t=decodeURIComponent(n[o].substr(e.length+1));break}return t},setCookie:function(e,t,n){var o=[],i=void 0,s=n||{};return null==t&&(t="",s.expires=-1),"number"==typeof s.expires?(i=new Date).setTime(i.getTime()+1e3*s.expires*60*60*24):s.expires instanceof Date&&(i=s.expires),o.push(e+"="+encodeURIComponent(t)),i&&o.push("expires="+i.toUTCString()),s.path&&o.push("path="+s.path),s.domain&&o.push("domain="+s.domain),s.secure&&o.push("secure"),document.cookie=o.join("; ")}}),c={"windows nt 5.0":1,"windows 2000":2,"windows nt 5.1":3,"windows xp":4,"windows nt 5.2":5,"windows 2003":6,"windows nt 6.0":7,"windows vista":8,"windows nt 6.1":9,"windows 7":10,"windows nt 6.2":11,"windows 8":12,"windows nt 6.3":13,"windows 8.1":14,"windows nt 6.4":23,"windows 10":24,"windows nt 10.0":25},u={win32:c,windows:c,android:15,ipad:16,iphone:17,macintosh:18,macIntel:19,mac:20,x11:21,linux:22},f=function(e){if(/^https|http|\/\//g.test(e)||r.isWebZip&&/^\.\/resource\//.test(e))return e;var t=window.location2.origin;return"/"===e.charAt(0)?""+t+e:t+"/"+e},l=function(e,t){var n=f(e);return new Promise(function(e,o){var i=document.createElement("script");i.readyState?i.onreadystatechange=function(){"loaded"!=i.readyState&&"complete"!=i.readyState||(i.onreadystatechange=null,e(n))}:(i.onload=function(){e(n)},i.onerror=function(e){o(e)}),i.src=n,t&&(i.async=1),(document.body||document.getElementsByTagName("head").item(0)).appendChild(i)})};window.KEWLUtils={getFullUrl:f,loadJs:l},t.utility=p,t.WinMap=c,t.sysMap=u,t.json2url=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=[];return Object.keys(e).forEach(function(n){t.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]))}),t.join("&")},t.getFullUrl=f,t.loadJs=l,t.transformRequest=function(e){return Object.keys(e).map(function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])}).join("&")},t.getUserInfo=function(){return new Promise(function(e,t){if(window.KEWLApp&&window.KEWLApp.isKEWLApp)window.KEWLApp.getUserInfo(function(n){var o="string"==typeof n&&"null"!==n&&""!==n?JSON.parse(n):n;o instanceof Object&&o.userId&&o.token&&o.deviceId?e(o):t()});else{var n=window.localStorage&&window.localStorage.getItem("userInfo")?JSON.parse(window.Base64.fromBase64(window.localStorage.getItem("userInfo"))):"";n instanceof Object&&n.user&&n.user.uid?e(n):t()}})}},20:function(e,t,n){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var o=n(22),i=n(23),s=n(24);function r(){return p.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(r()<t)throw new RangeError("Invalid typed array length");return p.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=p.prototype:(null===e&&(e=new p(t)),e.length=t),e}function p(e,t,n){if(!(p.TYPED_ARRAY_SUPPORT||this instanceof p))return new p(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return c(this,e,t,n)}function c(e,t,n,o){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,o){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(o||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===o?new Uint8Array(t):void 0===o?new Uint8Array(t,n):new Uint8Array(t,n,o);p.TYPED_ARRAY_SUPPORT?(e=t).__proto__=p.prototype:e=l(e,t);return e}(e,t,n,o):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!p.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var o=0|d(t,n),i=(e=a(e,o)).write(t,n);i!==o&&(e=e.slice(0,i));return e}(e,t,n):function(e,t){if(p.isBuffer(t)){var n=0|h(t.length);return 0===(e=a(e,n)).length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(o=t.length)!=o?a(e,0):l(e,t);if("Buffer"===t.type&&s(t.data))return l(e,t.data)}var o;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function f(e,t){if(u(t),e=a(e,t<0?0:0|h(t)),!p.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function l(e,t){var n=t.length<0?0:0|h(t.length);e=a(e,n);for(var o=0;o<n;o+=1)e[o]=255&t[o];return e}function h(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function d(e,t){if(p.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return x(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return D(e).length;default:if(o)return x(e).length;t=(""+t).toLowerCase(),o=!0}}function g(e,t,n){var o=e[t];e[t]=e[n],e[n]=o}function I(e,t,n,o,i){if(0===e.length)return-1;if("string"==typeof n?(o=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=p.from(t,o)),p.isBuffer(t))return 0===t.length?-1:y(e,t,n,o,i);if("number"==typeof t)return t&=255,p.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):y(e,[t],n,o,i);throw new TypeError("val must be string, number or Buffer")}function y(e,t,n,o,i){var s,r=1,a=e.length,p=t.length;if(void 0!==o&&("ucs2"===(o=String(o).toLowerCase())||"ucs-2"===o||"utf16le"===o||"utf-16le"===o)){if(e.length<2||t.length<2)return-1;r=2,a/=2,p/=2,n/=2}function c(e,t){return 1===r?e[t]:e.readUInt16BE(t*r)}if(i){var u=-1;for(s=n;s<a;s++)if(c(e,s)===c(t,-1===u?0:s-u)){if(-1===u&&(u=s),s-u+1===p)return u*r}else-1!==u&&(s-=s-u),u=-1}else for(n+p>a&&(n=a-p),s=n;s>=0;s--){for(var f=!0,l=0;l<p;l++)if(c(e,s+l)!==c(t,l)){f=!1;break}if(f)return s}return-1}function w(e,t,n,o){n=Number(n)||0;var i=e.length-n;o?(o=Number(o))>i&&(o=i):o=i;var s=t.length;if(s%2!=0)throw new TypeError("Invalid hex string");o>s/2&&(o=s/2);for(var r=0;r<o;++r){var a=parseInt(t.substr(2*r,2),16);if(isNaN(a))return r;e[n+r]=a}return r}function A(e,t,n,o){return F(x(t,e.length-n),e,n,o)}function v(e,t,n,o){return F(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,o)}function m(e,t,n,o){return v(e,t,n,o)}function O(e,t,n,o){return F(D(t),e,n,o)}function S(e,t,n,o){return F(function(e,t){for(var n,o,i,s=[],r=0;r<e.length&&!((t-=2)<0);++r)n=e.charCodeAt(r),o=n>>8,i=n%256,s.push(i),s.push(o);return s}(t,e.length-n),e,n,o)}function P(e,t,n){return 0===t&&n===e.length?o.fromByteArray(e):o.fromByteArray(e.slice(t,n))}function b(e,t,n){n=Math.min(e.length,n);for(var o=[],i=t;i<n;){var s,r,a,p,c=e[i],u=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=n)switch(f){case 1:c<128&&(u=c);break;case 2:128==(192&(s=e[i+1]))&&(p=(31&c)<<6|63&s)>127&&(u=p);break;case 3:s=e[i+1],r=e[i+2],128==(192&s)&&128==(192&r)&&(p=(15&c)<<12|(63&s)<<6|63&r)>2047&&(p<55296||p>57343)&&(u=p);break;case 4:s=e[i+1],r=e[i+2],a=e[i+3],128==(192&s)&&128==(192&r)&&128==(192&a)&&(p=(15&c)<<18|(63&s)<<12|(63&r)<<6|63&a)>65535&&p<1114112&&(u=p)}null===u?(u=65533,f=1):u>65535&&(u-=65536,o.push(u>>>10&1023|55296),u=56320|1023&u),o.push(u),i+=f}return function(e){var t=e.length;if(t<=E)return String.fromCharCode.apply(String,e);var n="",o=0;for(;o<t;)n+=String.fromCharCode.apply(String,e.slice(o,o+=E));return n}(o)}t.Buffer=p,t.SlowBuffer=function(e){+e!=e&&(e=0);return p.alloc(+e)},t.INSPECT_MAX_BYTES=50,p.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=r(),p.poolSize=8192,p._augment=function(e){return e.__proto__=p.prototype,e},p.from=function(e,t,n){return c(null,e,t,n)},p.TYPED_ARRAY_SUPPORT&&(p.prototype.__proto__=Uint8Array.prototype,p.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&p[Symbol.species]===p&&Object.defineProperty(p,Symbol.species,{value:null,configurable:!0})),p.alloc=function(e,t,n){return function(e,t,n,o){return u(t),t<=0?a(e,t):void 0!==n?"string"==typeof o?a(e,t).fill(n,o):a(e,t).fill(n):a(e,t)}(null,e,t,n)},p.allocUnsafe=function(e){return f(null,e)},p.allocUnsafeSlow=function(e){return f(null,e)},p.isBuffer=function(e){return!(null==e||!e._isBuffer)},p.compare=function(e,t){if(!p.isBuffer(e)||!p.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,o=t.length,i=0,s=Math.min(n,o);i<s;++i)if(e[i]!==t[i]){n=e[i],o=t[i];break}return n<o?-1:o<n?1:0},p.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},p.concat=function(e,t){if(!s(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return p.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var o=p.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var r=e[n];if(!p.isBuffer(r))throw new TypeError('"list" argument must be an Array of Buffers');r.copy(o,i),i+=r.length}return o},p.byteLength=d,p.prototype._isBuffer=!0,p.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},p.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},p.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},p.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?b(this,0,e):function(e,t,n){var o=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return M(this,t,n);case"utf8":case"utf-8":return b(this,t,n);case"ascii":return L(this,t,n);case"latin1":case"binary":return W(this,t,n);case"base64":return P(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,t,n);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}.apply(this,arguments)},p.prototype.equals=function(e){if(!p.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===p.compare(this,e)},p.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},p.prototype.compare=function(e,t,n,o,i){if(!p.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===o&&(o=0),void 0===i&&(i=this.length),t<0||n>e.length||o<0||i>this.length)throw new RangeError("out of range index");if(o>=i&&t>=n)return 0;if(o>=i)return-1;if(t>=n)return 1;if(this===e)return 0;for(var s=(i>>>=0)-(o>>>=0),r=(n>>>=0)-(t>>>=0),a=Math.min(s,r),c=this.slice(o,i),u=e.slice(t,n),f=0;f<a;++f)if(c[f]!==u[f]){s=c[f],r=u[f];break}return s<r?-1:r<s?1:0},p.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},p.prototype.indexOf=function(e,t,n){return I(this,e,t,n,!0)},p.prototype.lastIndexOf=function(e,t,n){return I(this,e,t,n,!1)},p.prototype.write=function(e,t,n,o){if(void 0===t)o="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)o=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===o&&(o="utf8")):(o=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");for(var s=!1;;)switch(o){case"hex":return w(this,e,t,n);case"utf8":case"utf-8":return A(this,e,t,n);case"ascii":return v(this,e,t,n);case"latin1":case"binary":return m(this,e,t,n);case"base64":return O(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,t,n);default:if(s)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),s=!0}},p.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var E=4096;function L(e,t,n){var o="";n=Math.min(e.length,n);for(var i=t;i<n;++i)o+=String.fromCharCode(127&e[i]);return o}function W(e,t,n){var o="";n=Math.min(e.length,n);for(var i=t;i<n;++i)o+=String.fromCharCode(e[i]);return o}function M(e,t,n){var o=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>o)&&(n=o);for(var i="",s=t;s<n;++s)i+=B(e[s]);return i}function N(e,t,n){for(var o=e.slice(t,n),i="",s=0;s<o.length;s+=2)i+=String.fromCharCode(o[s]+256*o[s+1]);return i}function K(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function k(e,t,n,o,i,s){if(!p.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<s)throw new RangeError('"value" argument is out of bounds');if(n+o>e.length)throw new RangeError("Index out of range")}function R(e,t,n,o){t<0&&(t=65535+t+1);for(var i=0,s=Math.min(e.length-n,2);i<s;++i)e[n+i]=(t&255<<8*(o?i:1-i))>>>8*(o?i:1-i)}function T(e,t,n,o){t<0&&(t=4294967295+t+1);for(var i=0,s=Math.min(e.length-n,4);i<s;++i)e[n+i]=t>>>8*(o?i:3-i)&255}function _(e,t,n,o,i,s){if(n+o>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function C(e,t,n,o,s){return s||_(e,0,n,4),i.write(e,t,n,o,23,4),n+4}function j(e,t,n,o,s){return s||_(e,0,n,8),i.write(e,t,n,o,52,8),n+8}p.prototype.slice=function(e,t){var n,o=this.length;if((e=~~e)<0?(e+=o)<0&&(e=0):e>o&&(e=o),(t=void 0===t?o:~~t)<0?(t+=o)<0&&(t=0):t>o&&(t=o),t<e&&(t=e),p.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=p.prototype;else{var i=t-e;n=new p(i,void 0);for(var s=0;s<i;++s)n[s]=this[s+e]}return n},p.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||K(e,t,this.length);for(var o=this[e],i=1,s=0;++s<t&&(i*=256);)o+=this[e+s]*i;return o},p.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||K(e,t,this.length);for(var o=this[e+--t],i=1;t>0&&(i*=256);)o+=this[e+--t]*i;return o},p.prototype.readUInt8=function(e,t){return t||K(e,1,this.length),this[e]},p.prototype.readUInt16LE=function(e,t){return t||K(e,2,this.length),this[e]|this[e+1]<<8},p.prototype.readUInt16BE=function(e,t){return t||K(e,2,this.length),this[e]<<8|this[e+1]},p.prototype.readUInt32LE=function(e,t){return t||K(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},p.prototype.readUInt32BE=function(e,t){return t||K(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},p.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||K(e,t,this.length);for(var o=this[e],i=1,s=0;++s<t&&(i*=256);)o+=this[e+s]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},p.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||K(e,t,this.length);for(var o=t,i=1,s=this[e+--o];o>0&&(i*=256);)s+=this[e+--o]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},p.prototype.readInt8=function(e,t){return t||K(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},p.prototype.readInt16LE=function(e,t){t||K(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},p.prototype.readInt16BE=function(e,t){t||K(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},p.prototype.readInt32LE=function(e,t){return t||K(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},p.prototype.readInt32BE=function(e,t){return t||K(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},p.prototype.readFloatLE=function(e,t){return t||K(e,4,this.length),i.read(this,e,!0,23,4)},p.prototype.readFloatBE=function(e,t){return t||K(e,4,this.length),i.read(this,e,!1,23,4)},p.prototype.readDoubleLE=function(e,t){return t||K(e,8,this.length),i.read(this,e,!0,52,8)},p.prototype.readDoubleBE=function(e,t){return t||K(e,8,this.length),i.read(this,e,!1,52,8)},p.prototype.writeUIntLE=function(e,t,n,o){(e=+e,t|=0,n|=0,o)||k(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,s=0;for(this[t]=255&e;++s<n&&(i*=256);)this[t+s]=e/i&255;return t+n},p.prototype.writeUIntBE=function(e,t,n,o){(e=+e,t|=0,n|=0,o)||k(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+n},p.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,1,255,0),p.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},p.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):R(this,e,t,!0),t+2},p.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):R(this,e,t,!1),t+2},p.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):T(this,e,t,!0),t+4},p.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):T(this,e,t,!1),t+4},p.prototype.writeIntLE=function(e,t,n,o){if(e=+e,t|=0,!o){var i=Math.pow(2,8*n-1);k(this,e,t,n,i-1,-i)}var s=0,r=1,a=0;for(this[t]=255&e;++s<n&&(r*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/r>>0)-a&255;return t+n},p.prototype.writeIntBE=function(e,t,n,o){if(e=+e,t|=0,!o){var i=Math.pow(2,8*n-1);k(this,e,t,n,i-1,-i)}var s=n-1,r=1,a=0;for(this[t+s]=255&e;--s>=0&&(r*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/r>>0)-a&255;return t+n},p.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,1,127,-128),p.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},p.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):R(this,e,t,!0),t+2},p.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):R(this,e,t,!1),t+2},p.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,**********,-2147483648),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):T(this,e,t,!0),t+4},p.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):T(this,e,t,!1),t+4},p.prototype.writeFloatLE=function(e,t,n){return C(this,e,t,!0,n)},p.prototype.writeFloatBE=function(e,t,n){return C(this,e,t,!1,n)},p.prototype.writeDoubleLE=function(e,t,n){return j(this,e,t,!0,n)},p.prototype.writeDoubleBE=function(e,t,n){return j(this,e,t,!1,n)},p.prototype.copy=function(e,t,n,o){if(n||(n=0),o||0===o||(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<n&&(o=n),o===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-n&&(o=e.length-t+n);var i,s=o-n;if(this===e&&n<t&&t<o)for(i=s-1;i>=0;--i)e[i+t]=this[i+n];else if(s<1e3||!p.TYPED_ARRAY_SUPPORT)for(i=0;i<s;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+s),t);return s},p.prototype.fill=function(e,t,n,o){if("string"==typeof e){if("string"==typeof t?(o=t,t=0,n=this.length):"string"==typeof n&&(o=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==o&&"string"!=typeof o)throw new TypeError("encoding must be a string");if("string"==typeof o&&!p.isEncoding(o))throw new TypeError("Unknown encoding: "+o)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var s;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(s=t;s<n;++s)this[s]=e;else{var r=p.isBuffer(e)?e:x(new p(e,o).toString()),a=r.length;for(s=0;s<n-t;++s)this[s+t]=r[s%a]}return this};var U=/[^+\/0-9A-Za-z-_]/g;function B(e){return e<16?"0"+e.toString(16):e.toString(16)}function x(e,t){var n;t=t||1/0;for(var o=e.length,i=null,s=[],r=0;r<o;++r){if((n=e.charCodeAt(r))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(r+1===o){(t-=3)>-1&&s.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&s.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;s.push(n)}else if(n<2048){if((t-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function D(e){return o.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(U,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function F(e,t,n,o){for(var i=0;i<o&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}}).call(this,n(21))},21:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},22:function(e,t,n){"use strict";t.byteLength=function(e){var t=c(e),n=t[0],o=t[1];return 3*(n+o)/4-o},t.toByteArray=function(e){for(var t,n=c(e),o=n[0],r=n[1],a=new s(function(e,t,n){return 3*(t+n)/4-n}(0,o,r)),p=0,u=r>0?o-4:o,f=0;f<u;f+=4)t=i[e.charCodeAt(f)]<<18|i[e.charCodeAt(f+1)]<<12|i[e.charCodeAt(f+2)]<<6|i[e.charCodeAt(f+3)],a[p++]=t>>16&255,a[p++]=t>>8&255,a[p++]=255&t;2===r&&(t=i[e.charCodeAt(f)]<<2|i[e.charCodeAt(f+1)]>>4,a[p++]=255&t);1===r&&(t=i[e.charCodeAt(f)]<<10|i[e.charCodeAt(f+1)]<<4|i[e.charCodeAt(f+2)]>>2,a[p++]=t>>8&255,a[p++]=255&t);return a},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,s=[],r=0,a=n-i;r<a;r+=16383)s.push(u(e,r,r+16383>a?a:r+16383));1===i?(t=e[n-1],s.push(o[t>>2]+o[t<<4&63]+"==")):2===i&&(t=(e[n-2]<<8)+e[n-1],s.push(o[t>>10]+o[t>>4&63]+o[t<<2&63]+"="));return s.join("")};for(var o=[],i=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,p=r.length;a<p;++a)o[a]=r[a],i[r.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function u(e,t,n){for(var i,s,r=[],a=t;a<n;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),r.push(o[(s=i)>>18&63]+o[s>>12&63]+o[s>>6&63]+o[63&s]);return r.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},23:function(e,t){t.read=function(e,t,n,o,i){var s,r,a=8*i-o-1,p=(1<<a)-1,c=p>>1,u=-7,f=n?i-1:0,l=n?-1:1,h=e[t+f];for(f+=l,s=h&(1<<-u)-1,h>>=-u,u+=a;u>0;s=256*s+e[t+f],f+=l,u-=8);for(r=s&(1<<-u)-1,s>>=-u,u+=o;u>0;r=256*r+e[t+f],f+=l,u-=8);if(0===s)s=1-c;else{if(s===p)return r?NaN:1/0*(h?-1:1);r+=Math.pow(2,o),s-=c}return(h?-1:1)*r*Math.pow(2,s-o)},t.write=function(e,t,n,o,i,s){var r,a,p,c=8*s-i-1,u=(1<<c)-1,f=u>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=o?0:s-1,d=o?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,r=u):(r=Math.floor(Math.log(t)/Math.LN2),t*(p=Math.pow(2,-r))<1&&(r--,p*=2),(t+=r+f>=1?l/p:l*Math.pow(2,1-f))*p>=2&&(r++,p/=2),r+f>=u?(a=0,r=u):r+f>=1?(a=(t*p-1)*Math.pow(2,i),r+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),r=0));i>=8;e[n+h]=255&a,h+=d,a/=256,i-=8);for(r=r<<i|a,c+=i;c>0;e[n+h]=255&r,h+=d,r/=256,c-=8);e[n+h-d]|=128*g}},24:function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},25:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.userInfo=null,this.fetchLock=!1,this.callbacks=[],this.instance=null}return o(e,[{key:"toSafe",value:function(e){return"string"==typeof e&&"null"!==e&&""!==e?JSON.parse(e):JSON.parse(JSON.stringify(e))}},{key:"getUserInfo",value:function(e){this.userInfo?e&&e(this.toSafe(this.userInfo)):(this.callbacks.push(e),this.fetchLock||(this.fetchLock=!0,this.getNativeUserInfo()))}},{key:"getNativeUserInfo",value:function(){var e=window.KEWLApp.UAInfo,t=e.IsKEWLApp,o=e.IsIOS,i=e.IsNewIosApp,s=t&&"object"===n(window.android)?window.android:null;if(t&&!o&&"object"===(void 0===s?"undefined":n(s))&&Object.prototype.hasOwnProperty.call(s,"getUserInfo"))this.updateUserInfo(s.getUserInfo(""));else if(t&&o){var r=window.KEWLApp.asyncCallbackName(this.updateUserInfo,this);i?window.KEWLApp.iosPostMessage({type:"getuserinfo",callback:r}):window.KEWLApp.tryOpen("getuserinfo?callback="+r)}}},{key:"updateUserInfo",value:function(e){this.userInfo=this.toSafe(e);for(var t=this.callbacks.shift();t;)t&&t(this.toSafe(e)),t=this.callbacks.shift()}}],[{key:"getInstance",value:function(){return this.instance||(this.instance=new e),this.instance}}]),e}();t.default=i},26:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(6),s=(o=i)&&o.__esModule?o:{default:o};t.default=function(){var e="liveme";switch(s.default.packageInfo){case"arab":e="ar";break;case"us":e="us";break;case"livemeet":e="livemeet";break;case"cheez":e="cheez";break;case"lenovo":e="lenovo";break;case"meet":e="meet";break;case"pro":e="lmpro";break;case"athena":e="athena";break;case"emolm":e="emolm";break;case"highlive":e="highlive";break;default:e="liveme"}return e}()},3:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isWebZip=void 0;var o,i=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],o=!0,i=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(n.push(r.value),!t||n.length!==t);o=!0);}catch(e){i=!0,s=e}finally{try{!o&&a.return&&a.return()}finally{if(i)throw s}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=n(0);(o=s)&&o.__esModule;var r=window.KEWLWebZip||{},a=t.isWebZip=["content:","file:"].includes(window.location.protocol);r.isWebZip=a,r.jump=function(e){var t=e.url,n=e.isReplace,o=t;window.KEWLWebZip.isWebZip&&(new RegExp("/app/"+window.KEWLWebZip.name).test(t)?(o=window.location.pathname.replace(/(\/dist\/.*)|\/[^/]+(?!.)/g,t.split("/app/"+window.KEWLWebZip.name)[1]),window.KEWLWebZip.WEBHOST&&-1==o.indexOf("WEBHOST")&&(o+=(o.indexOf("?")>-1?"&":"?")+"WEBHOST="+encodeURIComponent(window.KEWLWebZip.WEBHOST))):/^\/\//g.test(t)?o=""+window.location2.protocol+t:/^\//g.test(t)?o=""+window.location2.origin+t:/https:\/\/|http:\/\/|^\/\//g.test(t)&&(o=t)),n?window.location.replace(o):window.location.href=o};var p=void 0,c=function(){return new Promise(function(e){var t=(new Date).getTime();window.KEWLHttp&&window.KEWLHttp.get(window.location2.origin+"/app/webzip/zipVersion.json?t="+t).then(function(t){200==t.status&&t.data.zips&&(p=t.data.zips,e(p))})})},u=function(e,t,n){p?n(e,t,p[e]):c().then(function(){n(e,t,p[e])})};r.findSelfZip=function(){var e=void 0,t=void 0,n=void 0;if(window.KEWLWebZip.isWebZip){var o=window.KEWLWebZip;e=o.prex,t=o.name,n=o.version,e||(e="/app/"+t)}else{var s=/\/app\/(.*?)\//g.exec(window.location2.href);if(!(s&&Array.isArray(s)&&s.length>1))return;var r=i(s,2);e=r[0],t=r[1],n=0}u(t,n,function(n,o,i){!i&&window.KEWLWebZip.isWebZip?(window.KEWLApp.deleteWebZipRes(e),window.location.replace(window.location2.href)):i>o&&(window.KEWLApp.downloadWebZipRes(t,i),window.KEWLWebZip.isWebZip&&window.location.replace(window.location2.href))})},r.fetchZipVersion=c,r.checkZipVersion=u,window.KEWLWebZip=r,t.default=r},5:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.IsKEWLApp=t.IsNewIosApp=t.IsPC=t.IsIOS=t.IsIPhone=t.IsIPad=t.IsAndroid=t.matchUA=t.UA=void 0;var o,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s=n(6);var r=((o=s)&&o.__esModule?o:{default:o}).default.iosUAKeyword,a=window,p=t.UA=a.navigator.userAgent,c=new RegExp(r+"/(.+)","i"),u=(t.matchUA=p.match(c)||window.LMUserAgent&&window.LMUserAgent.match(c),t.IsAndroid=/Android|HTC/i.test(p)),f=t.IsIPad=!u&&(/iPad/i.test(p)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!window.MSStream,l=t.IsIPhone=!u&&/iPod|iPhone/i.test(p),h=t.IsIOS=f||l,d=(t.IsPC=!u&&!h&&-1==p.toLocaleLowerCase().indexOf("xiaomi"),t.IsNewIosApp="object"===i(a.webkit)&&!!a.webkit.messageHandlers&&!!a.webkit.messageHandlers.webViewApp&&!!a.webkit.messageHandlers.webViewApp.postMessage&&"function"==typeof a.webkit.messageHandlers.webViewApp.postMessage);t.IsKEWLApp=u&&"object"===i(a.android)&&Object.prototype.hasOwnProperty.call(a.android,"openBoZhuPage")||!!d||c.test(p)},59:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.KEWLApp=t.Base64=void 0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=(u(n(0)),u(n(19))),s=u(n(6)),r=n(2),a=n(5),p=u(n(25)),c=u(n(26));function u(e){return e&&e.__esModule?e:{default:e}}var f=window,l=f.document,h=a.IsKEWLApp&&"object"===o(f.android)?f.android:null,d=s.default.packageName,g=s.default.packageArea,I=s.default.packageInfo,y=d+"://";function w(e,t){var n=""+(d+(new Date).getTime())+Math.random().toString().substr(2);return f[n]=function(o){var i="";if("string"==typeof o)try{i=JSON.parse(o)}catch(e){i=o}else i=o;var s=void 0;e&&(s=t?e.call(t,i):e(i)),s||delete f[n]},n}function A(){if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getVerCode"))return h.getVerCode("");if(a.IsKEWLApp&&a.IsIOS&&a.matchUA&&a.matchUA.length>1){var e=a.matchUA[1].split("."),t=0;return t+=e[0]?1e7*parseInt(e[0]):0,t+=e[1]?1e5*parseInt(e[1]):0,t+=e[2]?1e4*parseInt(e[2]):0,t+=e[3]?parseInt(e[3]):0}return 0}function v(){if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getVerName"))return h.getVerName("");if(a.IsKEWLApp&&a.IsIOS&&a.matchUA&&a.matchUA.length>1){var e=a.matchUA[1].split(";");return e.length>1?e[0]:a.matchUA[1]}return""}window.onCloseH5Game||(window.onCloseH5Game=function(){return!1}),window.getAudioGameBeamList||(window.getAudioGameBeamList=function(){return!1}),window.endAudioGame||(window.endAudioGame=function(){return!1}),window.stopAudioMusic||(window.stopAudioMusic=function(){return!1}),window.startAudioMusic||(window.startAudioMusic=function(){return!1});var m={kewlversion:function(){return"0.1.1"},UAInfo:{UA:a.UA,IsAndroid:a.IsAndroid,IsIPad:a.IsIPad,IsIPhone:a.IsIPhone,IsIOS:a.IsIOS,IsNewIosApp:a.IsNewIosApp,IsKEWLApp:a.IsKEWLApp},PackageDetails:{packageName:d,packageArea:g,packageInfo:I,PREFIX:y},asyncCallbackName:w,tryOpen:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(a.IsIOS){if(!e&&""!==e)return;if(e=y+e,a.IsIOS)return void(f.location2.href=e);var n=l.createElement("iframe");n.style.cssText="width:1px;height:1px;position:fixed;top:0;left:0;",n.src=e,l.body.appendChild(n),setTimeout(function(){l.body.removeChild(n)},150)}this.tryIntentOpen(e,t)},tryIntentOpen:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=l.createElement("a"),o=f.location2.protocol+"//"+f.location2.host+"/download/middlepage.html?"+f.location2.search;t&&(o=t);var i="intent://"+e+"#Intent;scheme="+d+";package="+g+";S.browser_fallback_url="+o+";end";n.style.cssText="width:1px;height:1px;position:fixed;top:0;left:0;",n.href=i,n.id="appIntent",l.body.appendChild(n),document.getElementById("appIntent").click(),l.body.removeChild(n)},iosPostMessage:function(e){a.IsNewIosApp&&"object"===(void 0===e?"undefined":o(e))&&f.webkit.messageHandlers.webViewApp.postMessage(e)},openApp:function(){a.IsKEWLApp||this.tryOpen("")},openPersonalTriviaPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openPersonalTriviaPage(""):a.IsNewIosApp?this.iosPostMessage({type:"openPersonalTriviaPage"}):this.tryOpen("openPersonalTriviaPage")},openBindAccountPage:function(e){a.IsKEWLApp&&!a.IsIOS?h.openBindAccountPage(e):a.IsNewIosApp?this.iosPostMessage({type:"openBindAccountPage",category:e}):this.tryOpen("openBindAccountPage?category="+e)},openCreateFAMPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openCreateFAMPage():a.IsNewIosApp?this.iosPostMessage({type:"openCreateFAMPage"}):this.tryOpen("openCreateFAMPage")},openFAMPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openFAMPage():a.IsNewIosApp?this.iosPostMessage({type:"openFAMPage"}):this.tryOpen("openFAMPage")},openAnalysePage:function(){a.IsKEWLApp&&!a.IsIOS?h.openAnalysePage():a.IsNewIosApp?this.iosPostMessage({type:"openAnalysePage"}):this.tryOpen("openAnalysePage")},openApplyVAnchorPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openApplyVAnchorPage():a.IsNewIosApp?this.iosPostMessage({type:"openApplyVAnchorPage"}):this.tryOpen("openApplyVAnchorPage")},openApplySAnchorPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openApplySAnchorPage():a.IsNewIosApp?this.iosPostMessage({type:"openApplySAnchorPage"}):this.tryOpen("openApplySAnchorPage")},openqrcodepage:function(){a.IsKEWLApp&&!a.IsIOS?h.openqrcodepage("0"):a.IsNewIosApp?this.iosPostMessage({type:"openqrcodepage"}):this.tryOpen("openqrcodepage?source=0")},openShortVideoRecord:function(e){if(a.IsKEWLApp&&!a.IsIOS)h.openShortVideoRecord(e);else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openShortVideoRecord",tag:e})}catch(e){}else this.tryOpen("openShortVideoRecord?tag="+e)},openActivityCenter:function(){if(a.IsKEWLApp&&!a.IsIOS)h.openactivitycenter();else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openactivitycenter"})}catch(e){}else this.tryOpen("openactivitycenter")},openShortVideoPlayer:function(e){if(a.IsKEWLApp&&!a.IsIOS)h.openShortVideoPlayer(e);else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openShortVideoPlayer",id:""+e})}catch(e){}else this.tryOpen("openShortVideoPlayer?id="+e)},openGameVideoList:function(){if(a.IsKEWLApp&&!a.IsIOS)h.openGameVideoList();else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openGameVideoList"})}catch(e){}else this.tryOpen("openGameVideoList")},openMyFamList:function(e){if(e)if(a.IsKEWLApp&&!a.IsIOS)h.openmyfamlist(e);else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openmyfamlist",filtertype:""+e})}catch(e){}else this.tryOpen("openmyfamlist?filtertype="+e);else if(a.IsKEWLApp&&!a.IsIOS)h.openmyfamlist();else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openmyfamlist"})}catch(e){}else this.tryOpen("openmyfamlist")},openLivePage:function(e){if(e=e||"",a.IsKEWLApp&&!a.IsIOS)h.openLivePage(""+e);else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openlivepage",id:""+e})}catch(e){}else this.tryOpen("openlivepage?id="+e)},openReplayPage:function(e){if(e=e||"",a.IsKEWLApp&&!a.IsIOS)this.getVerCode()<30080500?this.openLivePage(e):h.openReplayPage(""+e);else if(a.IsNewIosApp)try{this.iosPostMessage({type:"openlivepage",id:""+e})}catch(e){}else this.tryOpen("openreplaypage?id="+e)},openBoZhuPage:function(e){e&&(a.IsKEWLApp&&!a.IsIOS?h.openBoZhuPage(""+e):a.IsNewIosApp?this.iosPostMessage({type:"openbozhupage",id:""+e}):this.tryOpen("openbozhupage?id="+e))},openHomePage:function(){a.IsKEWLApp&&!a.IsIOS?h.openHomePage(""):a.IsNewIosApp?this.iosPostMessage({type:"openhomepage"}):this.tryOpen("openhomepage")},openPersonalPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openPersonalPage():a.IsNewIosApp?this.iosPostMessage({type:"openpersonalpage"}):this.tryOpen("openpersonalpage")},openTaskListPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openTaskListPage():this.tryOpen("opentasklistpage")},openCashPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openCashPage(""):this.tryOpen("opencashpage")},openNft:function(e,t){a.IsKEWLApp&&!a.IsIOS?h.openNft(e,t):this.tryOpen("openNft?uid="+i.default.encode(e)+"&name="+i.default.encode(t))},openPrepareLivePage:function(){if(a.IsKEWLApp&&!a.IsIOS)if(0===arguments.length||1===arguments.length&&""===arguments[0])h.openPrepareLivePage("");else{var e=Array.prototype.slice.call(arguments);if(e.length<3)for(var t=0,n=3-e.length;t<n;t+=1)e.push("");else e.length=3;h.openPrepareLivePage.apply(h,e)()}else a.IsNewIosApp?this.iosPostMessage({type:"openpreparelivepage"}):this.tryOpen("openpreparelivepage")},openIntroduceBroadcasterPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openIntroduceBroadcasterPage(""):this.tryOpen("openintroducebroadcasterpage")},openwebview:function(e){!a.IsKEWLApp&&i.default&&this.tryOpen("openwebview?url="+i.default.encode(e))},androidTryOpenAppOrDownload:function(e){var t=this;setTimeout(function(){var n=(new Date).valueOf();e&&e.id?t.openLivePage(e.id):e.other&&t[e.other.fn]?t[e.other.fn].apply(t,e.other.param||[]):t.openApp(),n=(new Date).valueOf(),setTimeout(function(){(new Date).valueOf()-n<1550?e.fail&&e.fail():e.success&&e.success()},1500)},100)},openSearchTagPage:function(e){a.IsKEWLApp&&!a.IsIOS&&e&&h&&h.openSearchTagPage&&h.openSearchTagPage("",e)},closePage:function(){a.IsKEWLApp&&!a.IsIOS?h.closePage(""):a.IsNewIosApp?this.iosPostMessage({type:"close"}):a.IsKEWLApp&&a.IsIOS&&this.tryOpen("close")},getVerCode:function(){return A()},getVerName:function(){return v()},setTitle:function(e){e&&"string"==typeof e&&(i.default&&a.IsKEWLApp&&!a.IsIOS&&this.getVerCode()>=209e5?h.changeTitle(i.default.encode(e)):i.default&&a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=1031e5?this.tryOpen("changeTitle?title="+i.default.encode(e)):i.default&&a.IsNewIosApp?this.iosPostMessage({type:"changetitle",title:i.default.encode(e)}):document.title=e)},getAnchorInfo:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getAnchorInfo"))t="string"==typeof(t=h.getAnchorInfo())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getAnchorInfo",callback:n}):this.tryOpen("getAnchorInfo?callback="+n)}},getVideoInfo:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getVideoInfo"))t="string"==typeof(t=h.getVideoInfo())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getVideoInfo",callback:n}):this.tryOpen("getVideoInfo?callback="+n)}},getUserInfo:function(e){p.default.getInstance().getUserInfo(e)},copy:function(e){return!!e&&(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getUserInfo")?(h.copy(""+e),!0):!!a.IsNewIosApp&&(this.iosPostMessage({type:"copy",text:""+e}),!0))},hasShare:a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&(Object.prototype.hasOwnProperty.call(h,"openShareMenu")||Object.prototype.hasOwnProperty.call(h,"showShareMenu"))||a.IsKEWLApp&&a.IsIOS&&A()>=108e5,openShareMenu:function(e,t){var n={shareTypes:"0",url:window.location2.href,content:document.querySelector('meta[name="description"]').content,image:f.location2.protocol+"//"+f.location2.hostname+"/images/logo.jpg",subject:document.title,isShowShare:!0,private_chat_url:"",show_friend:0};"object"===(void 0===e?"undefined":o(e))&&(Object.keys(n).forEach(function(t){Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}),n.callback=w(t),this.hasShare&&!a.IsIOS?h.openShareMenu(JSON.stringify(n)):this.hasShare&&a.IsIOS&&(n.type="openShareMenu",this.iosPostMessage(n)))},getH5ActEntranceInfo:function(e){var t=w(e);if(a.IsKEWLApp&&!a.IsIOS){var n=h.getH5ActEntranceInfo();e&&e(n)}else a.IsNewIosApp?this.iosPostMessage({type:"getH5ActEntranceInfo",callback:t}):this.tryOpen("getH5ActEntranceInfo?callback="+t)},changeH5ActEntranceSize:function(e){a.IsKEWLApp&&!a.IsIOS?h.changeH5ActEntranceSize(e):a.IsNewIosApp?this.iosPostMessage({type:"changeH5ActEntranceSize",status:e}):this.tryOpen("changeH5ActEntranceSize?status="+e)},showShareMenu:function(e,t){var n={shareTypes:"0",url:window.location2.href,content:document.querySelector('meta[name="description"]').content,image:f.location2.protocol+"//"+f.location2.hostname+"/images/logo.jpg",subject:document.title,isShowShare:!0,private_chat_url:"",show_friend:0};"object"===(void 0===e?"undefined":o(e))&&(Object.keys(n).forEach(function(t){Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}),n.callback=w(t),this.hasShare&&!a.IsIOS?h.showShareMenu(JSON.stringify(n)):this.hasShare&&a.IsIOS&&(n.type="showShareMenu",this.iosPostMessage(n)))},openInBrowser:function(e){a.IsKEWLApp&&a.IsIOS&&A()>=2e7?this.iosPostMessage({type:"openurl",url:i.default.encode(""+e)}):a.IsKEWLApp&&h.openurl?h.openurl(i.default.encode(""+e)):window.location.href=""+e},openRechargePage:function(e){var t="";return t="object"===(void 0===e?"undefined":o(e))?JSON.stringify(e):e,a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openRechargePage")?(h.openRechargePage(t),!0):a.IsNewIosApp?(this.iosPostMessage({type:"openrechargepage",str:t}),!0):(this.tryOpen("openRechargePage?str="+t),!1)},openCatchDoll:function(){a.IsKEWLApp&&!a.IsIOS?h.openCatchDoll(""):a.IsNewIosApp?this.iosPostMessage({type:"openCatchDoll"}):this.tryOpen("openCatchDoll")},openCatchLive:function(e,t){a.IsKEWLApp&&!a.IsIOS?h.openCatchLive(e,t):a.IsNewIosApp?this.iosPostMessage({type:"openCatchLive",vid:""+e,videosource:""+t}):this.tryOpen("openCatchLive?vid="+e+"&videosource="+t)},openTopicList:function(){a.IsKEWLApp&&!a.IsIOS?h.openTopicList(""):a.IsNewIosApp?this.iosPostMessage({type:"openTopicList"}):this.tryOpen("openTopicList")},openTopicDetail:function(e,t){a.IsKEWLApp&&!a.IsIOS?h.openTopicDetail(e,t):a.IsNewIosApp?this.iosPostMessage({type:"openTopicDetail",vid:""+e,videosource:""+t}):this.tryOpen("openTopicDetail?topicId="+e+"&topicName="+t)},opendynamictopicdetail:function(e,t,n,o){a.IsKEWLApp&&!a.IsIOS?h.opendynamictopicdetail(e,t,n,o):a.IsNewIosApp?this.iosPostMessage({type:"opendynamictopicdetail",tag_id:e,feed_count:t,view_count:n,desc:o}):this.tryOpen("opendynamictopicdetail?tag_id="+e+"&feed_count="+t+"&view_count="+n+"&desc="+o)},openShortVideo:function(){a.IsKEWLApp&&!a.IsIOS?h.openShortVideo(""):a.IsNewIosApp?this.iosPostMessage({type:"openShortVideo"}):this.tryOpen("openShortVideo")},openPlayground:function(){a.IsKEWLApp&&!a.IsIOS?h.openPlayground(""):a.IsNewIosApp?this.iosPostMessage({type:"openPlayground"}):this.tryOpen("openPlayground")},getExtraString:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getExtraString"))t="string"==typeof(t=h.getExtraString())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=104e5){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getExtraString",callback:n}):this.tryOpen("getExtraString?callback="+n)}},performPhoneLogin:function(e,t,n){var o={code:e,phoneNum:t,password:n,type:"performphonelogin"};a.IsKEWLApp&&!a.IsIOS?h.performPhoneLogin(e,t,n):a.IsNewIosApp&&this.iosPostMessage(o)},openLetterPage:function(e){var t="{gid="+e+"}";return a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openLetterPage")?(h.openLetterPage(t),!0):a.IsNewIosApp?(this.iosPostMessage({type:"openLetterPage",gid:e}),!0):(this.tryOpen("openLetterPage"),!1)},robRedPacket:function(e){var t="{packetid="+e+"}";if(a.IsKEWLApp&&!a.IsIOS)h.robRedPacket(t);else{if(a.IsNewIosApp)return this.iosPostMessage({type:"robRedPacket",packetid:e}),!0;this.tryOpen("robRedPacket")}return!1},openFamilyManagerPage:function(e){return a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openFamilyManagerPage")?(h.openFamilyManagerPage(e.toString(),6),!0):a.IsNewIosApp?(this.iosPostMessage({type:"openfamilymanagerpage",gid:e,source:6}),!0):(this.tryOpen("openfamilymanagerpage"),!1)},getApiDomain:function(e,t){var n=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getApiDomain"))n="string"==typeof(n=h.getApiDomain(e))&&"null"!==n&&""!==n?JSON.parse(n):n,t&&t(n);else if(a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=104e5){var i=w(t);a.IsNewIosApp?this.iosPostMessage({type:"getApiDomain",domain:e,callback:i}):this.tryOpen("getApiDomain?domain="+e+"&callback="+i)}},getBindInfo:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getBindInfo"))t="string"==typeof(t=h.getBindInfo())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=104e5){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getBindInfo",callback:n}):this.tryOpen("getBindInfo?callback="+n)}},logoutApp:function(){a.IsKEWLApp&&!a.IsIOS?h.logoutApp():a.IsNewIosApp?this.iosPostMessage({type:"logoutApp"}):this.tryOpen("logoutApp")},openUrlInternal:function(e,t,n){var s=t||!0,r=i.default.encode(e);if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openUrlInternal"))return h.openUrlInternal(r,s),!0;if(a.IsNewIosApp){var p={type:"openUrlInternal",url:r,neadhead:s};return n&&(p.transition=n),this.iosPostMessage(p),!0}return!1},openUrlFullScreen:function(e,t){var n=!!t,s=i.default.encode(e);if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openUrlFullScreen"))return h.openUrlFullScreen(s,n),!0;if(a.IsNewIosApp){var r={type:"openUrlFullScreen",url:s,neadhead:n};return this.iosPostMessage(r),!0}return!1},openSendGifts:function(e,t,n){var o=e||"",i=t||"",s=n||"";this.versionCompare("4.3.30")?a.IsKEWLApp&&!a.IsIOS?h.openSendGifts(o,i,s):a.IsNewIosApp?this.iosPostMessage({type:"openSendGifts",tabid:""+o,activityid:""+i,source:""+s}):this.tryOpen("openSendGifts?tabid="+o+"&activityid="+i+"&source="+s):a.IsKEWLApp&&!a.IsIOS?h.openSendGifts(o,i):a.IsNewIosApp&&(this.iosPostMessage({type:"openSendGifts",tabid:""+o,activityid:""+i}),this.tryOpen("openSendGifts?tabid="+o+"&activityid="+i))},updateStar:function(e,t){var n=e||"",o=t||"";a.IsKEWLApp&&!a.IsIOS?h.updatestar(n,o):a.IsNewIosApp?this.iosPostMessage({type:"updatestar",star_num:""+n,star_sign:""+o}):this.tryOpen("updatestar?star_num="+n+"&star_sign="+o)},openFirstRechargeCommodity:function(e){a.IsKEWLApp&&!a.IsIOS?h.openFirstRechargeCommodity(e||""):a.IsNewIosApp?this.iosPostMessage({type:"openFirstRechargeCommodity",source:e||""}):this.tryOpen("openFirstRechargeCommodity?source="+e)},getFirstRechargeInfos:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS)t="string"==typeof(t=h.getFirstRechargeInfos())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else{var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getFirstRechargeInfos",callback:n}):this.tryOpen("getFirstRechargeInfos?callback="+n)}},openChargePanel:function(e){if(a.IsNewIosApp)this.iosPostMessage({type:"openchargepanel",srcName:e});else{var t="";t="string"==typeof e?JSON.stringify({srcName:e}):JSON.stringify(e),a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openChargePanel")?h.openChargePanel(t):a.IsIOS?this.tryOpen("openChargePanel?srcName="+e):this.tryOpen("openChargePanel?srcName="+t)}},getDiscountRechargeCurrency:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS)t="string"==typeof(t=h.getDiscountRechargeCurrency())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else{var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getDiscountRechargeCurrency",callback:n}):this.tryOpen("getDiscountRechargeCurrency?callback="+n)}},openDiscountRecharge:function(e){a.IsKEWLApp&&!a.IsIOS?h.openDiscountRecharge(e||""):a.IsNewIosApp?this.iosPostMessage({type:"openDiscountRecharge",srcName:e||""}):this.tryOpen("openDiscountRecharge?srcName="+e)},openDiscountRechargeKeyboard:function(e){a.IsKEWLApp&&!a.IsIOS?h.openDiscountRechargeKeyboard(e||""):a.IsNewIosApp?this.iosPostMessage({type:"openDiscountRechargeKeyboard",text:e||""}):this.tryOpen("openDiscountRechargeKeyboard?text="+e)},refreshGold:function(e){a.IsKEWLApp&&a.IsAndroid&&h.refreshGold&&h.refreshGold(e)},getThemeInfo:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getThemeInfo"))t="string"==typeof(t=h.getThemeInfo())&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getThemeInfo",callback:n}):this.tryOpen("getThemeInfo?callback="+n)}},getRequest:function(e,t){var n=void 0,i=w(t);a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getRequest")?(n="string"==typeof(n=h.getRequest(e,i))&&"null"!==n&&""!==n?JSON.parse(n):n,t&&t(n)):a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=104e5&&(a.IsNewIosApp?this.iosPostMessage({type:"getRequest",info:e,callback:i}):this.tryOpen("getRequest?info="+e+"&callback="+i))},pageFinish:function(e,t){var n=e||"",o=t||"";a.IsKEWLApp&&!a.IsIOS&&"function"==typeof h.pageFinish?h.pageFinish(n,o):a.IsNewIosApp?this.iosPostMessage({type:"pageFinish",url:n,performance:o}):this.tryOpen("pageFinish?url="+n+"&performance="+o)},pageShow:function(e){var t=e||"";a.IsKEWLApp&&!a.IsIOS?h.pageShow(t):a.IsNewIosApp?this.iosPostMessage({type:"pageShow",url:t}):this.tryOpen("pageShow?url="+t)},getPackgetInfo:function(){return I},getAlias:function(){return c.default},doTmxProfile:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"doTmxProfile"))t=h.doTmxProfile(),e&&e(t);else if(a.IsKEWLApp&&a.IsIOS){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"doTmxProfile",callback:n}):this.tryOpen("doTmxProfile?callback="+n)}},openVIPService:function(e,t,n,i){a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"openVIPService")?h.openVIPService(e,t,n,i):a.IsNewIosApp?this.iosPostMessage({type:"openVIPService",orderId:""+e,channel:""+t,errorCode:""+n,errorMsg:""+i}):this.tryOpen("openVIPService?tabid="+e+"&channel="+t+"&errorCode="+n+"&errorMsg="+i)},versionCompare:function(e){var t=v();if(null===t)return!1;var n=parseFloat(t),o=parseFloat(e),i=t.replace(n+".",""),s=e.replace(o+".","");return n>o||!(n<o)&&i>=s},onFamilyActStart:function(e){var t=e||null;a.IsKEWLApp&&!a.IsIOS?h.onFamilyActStart(t):a.IsNewIosApp?this.iosPostMessage({type:"onFamilyActStart",jsonString:t}):this.tryOpen("onFamilyActStart?jsonString="+t)},onFamilyActEnd:function(e){var t=e||null;a.IsKEWLApp&&!a.IsIOS?h.onFamilyActEnd(t):a.IsNewIosApp?this.iosPostMessage({type:"onFamilyActEnd",jsonString:t}):this.tryOpen("onFamilyActEnd?jsonString="+t)},openFansTagPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openFansTagPage(""):a.IsNewIosApp?this.iosPostMessage({type:"openFansTagPage"}):this.tryOpen("openFansTagPage")},hostMission:function(e,t){var n=e||null,o=void 0;if(a.IsKEWLApp&&!a.IsIOS)o=h.hostmission(n),t&&t(o);else if(a.IsNewIosApp){var i=w(t);this.iosPostMessage({type:"hostmission",jsonString:n,callback:i})}else this.tryOpen("hostmission?jsonString="+n)},onSettleAccount:function(e){var t=e||null;a.IsKEWLApp&&!a.IsIOS?h.onSettleAccount(t):a.IsNewIosApp?this.iosPostMessage({type:"onSettleAccount",balance:t}):this.tryOpen("onSettleAccount?balance="+t)},openPrivateChatroom:function(e,t){var n=e||null,o=void 0;if(a.IsKEWLApp&&!a.IsIOS)o=h.openprivatechatroom(n),t&&t(o);else if(a.IsNewIosApp){var i=w(t);this.iosPostMessage({type:"openprivatechatroom",chatinfo:n,callback:i})}else this.tryOpen("openprivatechatroom?jsonString="+n)},onApiAuthorization:function(){a.IsKEWLApp&&!a.IsIOS?h.onApiAuthorization():a.IsNewIosApp?this.iosPostMessage({type:"onApiAuthorization"}):this.tryOpen("onApiAuthorization")},autoZoomAndClipImage:function(e,t){var n=w(t);if(a.IsKEWLApp&&!a.IsIOS){e.callback=n;var o=h.autoZoomAndClipImage(JSON.stringify(e));o="string"==typeof o&&"null"!==o&&""!==o?JSON.parse(o):o,t&&t(o)}else a.IsKEWLApp&&a.IsIOS&&this.getVerCode()>=104e5&&(a.IsNewIosApp?(e.type="autozoomandclipimage",e.callback=n,this.iosPostMessage(e)):this.tryOpen("autoZoomAndClipImage?ver="+e.ver+"&images="+JSON.stringify(e.images)+"&callback="+n))},openBagPage:function(e){a.IsKEWLApp&&!a.IsIOS?h.openBagPage(e):a.IsNewIosApp?this.iosPostMessage({type:"openBagPage",effectType:""+e}):this.tryOpen("openBagPage?effectType="+e)},getAppInfo:function(e){var t=void 0,n=w(e);a.IsKEWLApp&&!a.IsIOS?(t=h.getappinfo(),e&&e(t)):a.IsNewIosApp?this.iosPostMessage({type:"getappinfo",callback:n}):this.tryOpen("getappinfo?callback="+n)},refreshNative:function(e,t){var n=t||"";a.IsKEWLApp&&!a.IsIOS?h.refreshNative(e,n):a.IsNewIosApp?this.iosPostMessage({type:"refreshNative",refreshType:e,jsonBean:n}):this.tryOpen("refreshNative?refreshType="+e+"&jsonBean="+n)},postDataToNative:function(e){var t=e||"";a.IsKEWLApp&&!a.IsIOS?h.postDataToNative(t):a.IsNewIosApp?this.iosPostMessage({type:"postDataToNative",jsonBean:t}):this.tryOpen("postDataToNative?jsonBean="+t)},purchaseGuard:function(){a.IsKEWLApp&&!a.IsIOS?h.purchaseGuard():a.IsNewIosApp?this.iosPostMessage({type:"purchaseGuard"}):this.tryOpen("purchaseGuard")},openFamilyList:function(e,t){var n=e||null,o=void 0;if(a.IsKEWLApp&&!a.IsIOS)o=h.openFamilyList(n),t&&t(o);else if(a.IsNewIosApp){var i=w(t);this.iosPostMessage({type:"openFamilyList",jsonString:n,callback:i})}else this.tryOpen("openFamilyList?jsonString="+n)},updateH5Page:function(e){a.IsKEWLApp&&!a.IsIOS?h.updateH5Page(e):a.IsNewIosApp?this.iosPostMessage({type:"updateH5Page",fun:e}):this.tryOpen("updateH5Page")},openCustomerService:function(e,t){var n=e||"",o=void 0;if(a.IsKEWLApp&&!a.IsIOS)o=h.openCustomerService(n),t&&t(o);else if(a.IsNewIosApp){w(t);this.iosPostMessage({type:"openCustomerService",params:n})}else this.tryOpen("openCustomerService?params="+n)},openEditPage:function(e,t){var n=e||"",o=void 0;if(a.IsKEWLApp&&!a.IsIOS)o=h.openEditPage(n),t&&t(o);else if(a.IsNewIosApp){var i=w(t);this.iosPostMessage({type:"openEditPage",jsonString:n,callback:i})}else this.tryOpen("openEditPage?jsonString="+n)},hideBackButton:function(e,t){var n=e||"";if(a.IsNewIosApp){var o=w(t);this.iosPostMessage({type:"hideBackButton",jsonString:n,callback:o})}},opendynamicdetail:function(e){a.IsKEWLApp&&!a.IsIOS?h.opendynamicdetail(""+e):a.IsNewIosApp?this.iosPostMessage({type:"opendynamicdetail",id:""+e}):this.tryOpen("opendynamicdetail?id="+e)},opendynamiclist:function(){a.IsKEWLApp&&!a.IsIOS?h.opendynamiclist():a.IsNewIosApp?this.iosPostMessage({type:"opendynamiclist"}):this.tryOpen("opendynamiclist")},opendynamicrelease:function(){a.IsKEWLApp&&!a.IsIOS?h.opendynamicrelease():a.IsNewIosApp?this.iosPostMessage({type:"opendynamicrelease"}):this.tryOpen("opendynamicrelease")},kewlBridge:function(e,t,n){var o=t||"",i=void 0;if(a.IsKEWLApp&&!a.IsIOS)i=h[e](o),n&&n(i);else if(a.IsNewIosApp){var s=w(n);this.iosPostMessage({type:e,jsonString:o,callback:s})}},kewlBridgeV2:function(e,t,n,o){var i=t||{},s=void 0,r=(n||[]).map(function(e){return void 0!==i[e]?i[e]:""});if(a.IsKEWLApp&&!a.IsIOS)s=h[e].apply(h,function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(r)),o&&o(s);else if(a.IsNewIosApp){var p=w(o);this.iosPostMessage(Object.assign({type:e,callback:p},i))}},audioGameBridge:function(e,t,n){var o=t||"",i=void 0;if(a.IsKEWLApp&&!a.IsIOS)i=h[e](o),n&&n(i);else if(a.IsNewIosApp){var s=w(n);this.iosPostMessage({type:e,jsonString:o,callback:s})}},spellGroupPay:function(e,t,n){a.IsKEWLApp&&!a.IsIOS?h.spellGroupPay(e,t,n):a.IsNewIosApp?this.iosPostMessage({type:"spellGroupPay",productId:e,groupId:t,gold:n}):this.tryOpen("spellGroupPay?productId="+e+"&groupId="+t+"&gold="+n)},showInviteUserPage:function(){a.IsKEWLApp&&!a.IsIOS?h.showInviteUserPage():a.IsNewIosApp?this.iosPostMessage({type:"showInviteUserPage"}):this.tryOpen("showInviteUserPage")},firstGiftGive:function(){a.IsKEWLApp&&!a.IsIOS?h.firstGiftGive():a.IsNewIosApp?this.iosPostMessage({type:"firstGiftGive"}):this.tryOpen("firstGiftGive")},continuousWatchLiveThirtySeconds:function(){a.IsKEWLApp&&!a.IsIOS?h.continuousWatchLiveThirtySeconds():a.IsNewIosApp?this.iosPostMessage({type:"continuousWatchLiveThirtySeconds"}):this.tryOpen("continuousWatchLiveThirtySeconds")},openFirstRechargeTaskPage:function(){a.IsKEWLApp&&!a.IsIOS?h.openFirstRechargeTaskPage():a.IsNewIosApp?this.iosPostMessage({type:"openFirstRechargeTaskPage"}):this.tryOpen("openFirstRechargeTaskPage")},newbieTasksPageGoBack:function(){a.IsKEWLApp&&!a.IsIOS?h.newbieTasksPageGoBack():a.IsNewIosApp?this.iosPostMessage({type:"newbieTasksPageGoBack"}):this.tryOpen("newbieTasksPageGoBack")},getNativeProductInfo:function(e){var t=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,"getNativeProductInfo"))t="string"==typeof(t=h.getNativeProductInfo(""))&&"null"!==t&&""!==t?JSON.parse(t):t,e&&e(t);else if(a.IsKEWLApp&&a.IsIOS){var n=w(e);a.IsNewIosApp?this.iosPostMessage({type:"getNativeProductInfo",callback:n}):this.tryOpen("getNativeProductInfo?callback="+n)}},openInputMethod:function(){a.IsKEWLApp&&!a.IsIOS?h.openInputMethod():a.IsNewIosApp?this.iosPostMessage({type:"openInputMethod"}):this.tryOpen("openInputMethod")},showGiftPanel:function(e,t,n){var o=e>=0?e:"",i=t||"",s=n||1;a.IsKEWLApp&&!a.IsIOS?(h.closePage(""),h.showGiftPanel(o,i,s)):a.IsNewIosApp?this.iosPostMessage({type:"showGiftPanel",tabType:""+o,activityid:""+i,isNeedOpen:""+s}):this.tryOpen("showGiftPanel?tabType="+o+"&activityid="+i,"&isNeedOpen="+s)},openLiveRoom:function(){a.IsKEWLApp&&!a.IsIOS?h.openLiveRoom():a.IsNewIosApp?this.iosPostMessage({type:"openLiveRoom"}):this.tryOpen("openLiveRoom")},openStickerMenu:function(){a.IsKEWLApp&&!a.IsIOS?h.openStickerMenu():a.IsNewIosApp?this.iosPostMessage({type:"openStickerMenu"}):this.tryOpen("openStickerMenu")},openBeamMenu:function(e,t){var n={role:e,vtype:t};a.IsKEWLApp&&!a.IsIOS?h.openBeamMenu(JSON.stringify(n)):a.IsNewIosApp?this.iosPostMessage({type:"openBeamMenu",role:e,vtype:t}):this.tryOpen("openBeamMenu?jsonString="+JSON.stringify(n))},taskfinish:function(e,t){a.IsKEWLApp&&!a.IsIOS?h.taskfinish(e,t):a.IsNewIosApp?this.iosPostMessage({type:"taskfinish",task:e||"",info:t||0}):this.tryOpen("taskfinish?task="+e+"&info="+t)},openSystemSetting:function(){a.IsKEWLApp&&!a.IsIOS?h.openSystemSetting():a.IsNewIosApp?this.iosPostMessage({type:"openSystemSetting"}):this.tryOpen("openSystemSetting")},openLiveH5Page:function(e){a.IsKEWLApp&&!a.IsIOS?h.openLiveH5Page(e):a.IsNewIosApp?this.iosPostMessage({type:"openLiveH5Page",url:e}):this.tryOpen("openLiveH5Page?url="+e)},openShareLivePanel:function(e,t,n,o){var i={tabIndex:e,vtype:t,content:n,context:o};if(a.IsKEWLApp&&!a.IsIOS)h.openShareLivePanel(JSON.stringify(i));else if(a.IsNewIosApp){var s={type:"openShareLivePanel",tabIndex:e,vtype:t};(n||o)&&(s.content=n,s.context=o),this.iosPostMessage(s)}else this.tryOpen("openShareLivePanel?jsonString="+JSON.stringify(i))},startRechargeActivity:function(e,t){var n="string"==typeof e?e:JSON.stringify(e),o=t&&!window.isNaN(t)?t-0:"";if(a.IsKEWLApp&&!a.IsIOS)""===o?h.startRechargeActivity(n):h.startRechargeActivityV2(n,o);else if(a.IsNewIosApp){var i={type:"startRechargeActivity",product_id:n};o&&(i.source=o),this.iosPostMessage(i)}else this.tryOpen("startRechargeActivity?product_id="+n)},openLegionTask:function(e){a.IsKEWLApp&&!a.IsIOS?h.openLegionTask(e):a.IsNewIosApp?this.iosPostMessage({type:"openLegionTask",legionID:e}):this.tryOpen("openLegionTask?legionID="+e)},openLegionDetail:function(e){a.IsKEWLApp&&!a.IsIOS?h.openLegionDetail(e):a.IsNewIosApp?this.iosPostMessage({type:"openLegionDetail",legionId:e}):this.tryOpen("openLegionDetail?legionId="+e)},openLegionRoom:function(){a.IsKEWLApp&&!a.IsIOS?h.openLegionRoom():a.IsNewIosApp?this.iosPostMessage({type:"openLegionRoom"}):this.tryOpen("openLegionRoom")},openLegionSharePanel:function(){a.IsKEWLApp&&!a.IsIOS?h.openLegionSharePanel():a.IsNewIosApp?this.iosPostMessage({type:"openLegionSharePanel"}):this.tryOpen("openLegionSharePanel")},openLegionDynamic:function(){a.IsKEWLApp&&!a.IsIOS?h.openLegionDynamic():a.IsNewIosApp?this.iosPostMessage({type:"openLegionDynamic"}):this.tryOpen("openLegionDynamic")},getNativeData:function(e,t){if(e){var n=e||"",i=void 0;if(a.IsKEWLApp&&!a.IsIOS&&"object"===(void 0===h?"undefined":o(h))&&Object.prototype.hasOwnProperty.call(h,n))i="string"==typeof(i=h[n]())&&"null"!==i&&""!==i?JSON.parse(i):i,t&&t(i);else if(a.IsKEWLApp&&a.IsIOS){var s=w(t);a.IsNewIosApp?this.iosPostMessage({type:n,callback:s}):this.tryOpen(n+"?callback="+s)}}},sendGift:function(e,t){var n=w(t);a.IsKEWLApp&&!a.IsIOS?(e.fnName=n,h.sendGift(JSON.stringify(e))):a.IsNewIosApp?this.iosPostMessage({type:"sendGift",obj:e,callback:n}):this.tryOpen("sendGift?obj="+e+"&callback="+n)},sendSayHiMsg:function(e){a.IsKEWLApp&&!a.IsIOS?h.sendSayHiMsg(JSON.stringify(e)):a.IsNewIosApp?this.iosPostMessage({type:"sendSayHiMsg",params:e}):this.tryOpen("sendSayHiMsg?params="+e)},tryOpenAppByPath:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=JSON.parse(JSON.stringify(e));delete n.other,delete n.path;var o=this;setTimeout(function(){if(o._setTimeEvent(function(t){e.success&&e.success(t)},function(t){e.fail&&e.fail(t)},function(t){e.unknown&&e.unknown(t)},2e3),e.other&&o[e.other.fn])o[e.other.fn].apply(o,e.other.param||[]);else if(e.path){var i=(0,r.json2url)(n)||"",s=e.path+"?"+i;o.tryOpen(s,t)}else o.openHomePage()},100)},fetchAreaGiftList:function(e){var t=w(e);a.IsKEWLApp&&!a.IsIOS?h.fetchAreaGiftList(t):a.IsNewIosApp?this.iosPostMessage({type:"fetchAreaGiftList",fnName:t}):this.tryOpen("fetchAreaGiftList?fnName="+t)},updateWishList:function(e){var t=JSON.stringify(e);a.IsKEWLApp&&!a.IsIOS?h.updateWishList(t):a.IsNewIosApp?this.iosPostMessage({type:"updateWishList",json:t}):this.tryOpen("updateWishList?json="+t)},getHost:function(e){var t=w(e);a.IsKEWLApp&&!a.IsIOS?h.getHost(t):a.IsNewIosApp&&this.iosPostMessage({type:"getHost",fnName:t})},downloadWebZipRes:function(e,t){var n="/app/"+e,o=f.location2.origin+"/app/"+e+"/dist/"+e+"_"+t+".zip";a.IsKEWLApp&&!a.IsIOS?h.downloadWebZipRes&&h.downloadWebZipRes(n,o):a.IsNewIosApp&&this.iosPostMessage({type:"downloadWebZipRes",prex:n,zipPath:o})},deleteWebZipRes:function(e){a.IsKEWLApp&&!a.IsIOS?h.deleteWebZipRes&&h.deleteWebZipRes(e):a.IsNewIosApp&&this.iosPostMessage({type:"deleteWebZipRes",prex:e})},startNativePaymentAction:function(e,t){window.rechargeCallback=function(e){t&&t(e,"startNativePaymentAction")},a.IsKEWLApp&&!a.IsIOS?h.startNativePaymentAction(JSON.stringify({product_id:e})):a.IsNewIosApp?this.iosPostMessage({type:"startNativePaymentAction",product_id:e}):this.tryOpen("startNativePaymentAction?product_id="+e)},showGiftPanelSelectGift:function(e,t){var n=JSON.stringify({tabId:e,giftId:t});a.IsKEWLApp&&!a.IsIOS?h.showGiftPanelSelectGift&&h.showGiftPanelSelectGift(n):a.IsNewIosApp?this.iosPostMessage({type:"showGiftPanelSelectGift",jsonString:n}):this.tryOpen("showGiftPanelSelectGift?jsonString="+n)},registerSendDataToWeb:function(e,t){var n={callback_source:e,callback:w(function(e){return t&&t(e),!0})};a.IsKEWLApp&&!a.IsIOS?h.registerSendDataToWeb&&h.registerSendDataToWeb(JSON.stringify(n)):a.IsNewIosApp&&(n.type="registerSendDataToWeb",this.iosPostMessage(n))},showMobileAds:function(e){var t=JSON.stringify(e);a.IsKEWLApp&&!a.IsIOS?h.showMobileAds(t):a.IsNewIosApp?this.iosPostMessage({type:"showMobileAds",json:t}):this.tryOpen("showMobileAds?json="+t)},deviceShock:function(){a.IsKEWLApp&&!a.IsIOS?h.deviceShock():a.IsNewIosApp?this.iosPostMessage({type:"deviceShock"}):this.tryOpen("deviceShock")},gameChannelUpdate:function(e){var t=JSON.stringify(e);a.IsKEWLApp&&!a.IsIOS?h.gameChannelUpdate(t):a.IsNewIosApp?this.iosPostMessage({type:"gameChannelUpdate",json:t}):this.tryOpen("gameChannelUpdate?json="+t)},webviewController:function(e){var t=JSON.stringify(e);a.IsKEWLApp&&!a.IsIOS?h.webviewController(t):a.IsNewIosApp?this.iosPostMessage({type:"webviewController",json:t}):this.tryOpen("webviewController?json="+t)},openFeedback:function(){a.IsKEWLApp&&!a.IsIOS?h.openFeedback&&h.openFeedback(""):a.IsNewIosApp?this.iosPostMessage({type:"openFeedback"}):this.tryOpen("openFeedback")},openAvatarPage:function(){this.versionCompare("4.5.75")&&(a.IsKEWLApp&&!a.IsIOS?h.openAvatarPage():a.IsNewIosApp?this.iosPostMessage({type:"openAvatarPage"}):this.tryOpen("openAvatarPage"))},_setTimeEvent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1500,i=!1,s="hidden",r="visibilitychange";void 0!==document.hidden?(s="hidden",r="visibilitychange"):void 0!==document.msHidden?(s="msHidden",r="msvisibilitychange"):void 0!==document.webkitHidden&&(s="webkitHidden",r="webkitvisibilitychange");var a=function t(o){i=!0,document[s]||o.hidden||"hidden"==document.visibilityState?e&&e():n&&n(),document.removeEventListener(r,t)};document.addEventListener(r,a,!1),setTimeout(function(){i||(document.removeEventListener(r,a),document.hidden||i?n&&n():t&&t(),i=!0)},o)}};Object.defineProperties(m,{isKEWLApp:{get:function(){return a.IsKEWLApp}}}),a.IsKEWLApp&&document.addEventListener("DOMContentLoaded",function(){m.setTitle(document.title)},!1),window.Base64=i.default;try{window.self==window.top?window.KEWLApp=m:window.KEWLApp=window.top.KEWLApp?window.top.KEWLApp:m}catch(e){window.KEWLApp=m}t.Base64=i.default,t.KEWLApp=m},6:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0);(o=i)&&o.__esModule;var s=window,r=s.navigator.userAgent,a=s.location2.hostname,p=r.indexOf("app/arab"),c=r.indexOf("app/us"),u=r.indexOf("app/cheez"),f=r.indexOf("app/lenovo"),l=r.indexOf("app/meet"),h=r.indexOf("app/pro"),d=r.indexOf("app/athena"),g=r.indexOf("app/livemeet"),I=r.indexOf("app/plusme"),y=r.indexOf("app/highlive"),w=atob("Y21saXZl"),A=atob("Y29tLmNtY20ubGl2ZQ=="),v=atob("bGl2ZW1l"),m=atob("bGl2ZS5tZS1pT1M=");(p>-1||"qa.royallive.com,www.royallive.com,qa.lmlite.net,www.lmlite.net,www.poplive.live,pop.lekerberos.com,royal.lekerberos.com".indexOf(a)>-1)&&(w="abslive",A="com.alive.android",v="arab"),(c>-1||"qa.aaalive.com,www.aaalive.com,www.frill.live,www.frilllive.com".indexOf(a)>-1)&&(w="alive",A="com.aaalive.live",v="us"),(g>-1||"qa.livemeet.tv,www.livemeet.tv".indexOf(a)>-1)&&(w="livemeet",A="com.livemeet.tv",v="livemeet",m="live.meet-iOS"),(I>-1||"qa.emolm.com,www.emolm.com,www.lmpluss.com".indexOf(a)>-1)&&(w="plusme",A="com.plusme.live",v="emolm",m="live.plusme-iOS"),(y>-1||"qa.highlives.net,www.highlives.net,www.hotslives.com".indexOf(a)>-1)&&(w="highlive",A="com.highlive.live",v="highlive",m="live.highlive-iOS"),(u>-1||"qa.cheez.com,www.cheez.com".indexOf(a)>-1)&&(w="cheezlive",A="com.joyinme.cheezlive",v="cheez"),f>-1&&(v="lenovo"),(l>-1||"qa-meet.linkv.sg,meet.linkv.sg".indexOf(a)>-1)&&(v="meet"),(h>-1||"qapro.liveme.com,pro.liveme.com,pro.lekerberos.com,pro.oedipus123.com,pro.muses123.com".indexOf(a)>-1)&&(w="cmlivepro",A="com.europe.live",v="pro"),d>-1&&(w="lmathena",A="com.athena.live",v="athena"),t.default={packageName:w,packageArea:A,packageInfo:v,iosUAKeyword:m}}});
//# sourceMappingURL=kewl.js.map