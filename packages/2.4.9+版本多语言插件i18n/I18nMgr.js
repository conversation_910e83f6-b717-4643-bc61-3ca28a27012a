module.exports = {
    // 刷新本地化组件
    'refresh-component': function (event, languageName) {
        // 把设置的语言名称更新到场景的全局变量中
        if (!cc.Jou_i18n) cc.Jou_i18n = {};
        cc.Jou_i18n.defaultLanguageName = languageName;

        // 刷新LocalizedLabel和LocalizedSprite
        let rootNode = cc.director.getScene();
        for (let i = 0, t = rootNode.childrenCount; i < t; i++) {
            let curNode = rootNode.children[i];
            let labelArray = curNode.getComponentsInChildren("LocalizedLabel");
            for (let j = 0, tt = labelArray.length; j < tt; j++) labelArray[j].refresh();
            let spriteArray = curNode.getComponentsInChildren("LocalizedSprite");
            for (let j = 0, tt = spriteArray.length; j < tt; j++) spriteArray[j].refresh();
        }
    }
}