import { LocalizedSprite } from "./LocalizedSprite";

const { ccclass, property, executeInEditMode, menu } = cc._decorator;

@ccclass
@executeInEditMode
@menu("i18n/LocalizedLabel")
export class LocalizedLabel extends cc.Component {

    @property({ serializable: true })
    private _key: string = "";
    @property(cc.String)
    public get key(): string { return this._key; }
    public set key(v: string) {
        this._key = v;
        this.refresh();
    }

    private curLabel: cc.Label = null;
    private paramStrArray: Array<string> = new Array<string>();

    onLoad() {
        this.curLabel = this.getComponent(cc.Label);
        if (this.curLabel == null) {
            cc.error("There is no cc.Label on LocalizedLabel, nodeName: " + this.node.name);
            this.enabled = false;
        }
    }

    onEnable() {
        this.refresh();
    }

    // 刷新cc.Label
    public refresh(): void {
        if (this.curLabel == null) return;

        let str = window.getLocalizedStr(this._key);

        // 填充参数
        for (let i = 0, t = this.paramStrArray.length; i < t; i++) str = str.replace("{" + i + "}", this.paramStrArray[i]);

        this.curLabel.string = str;
    }

    // 绑定参数
    public bindParam(...paramStrArray: Array<string>): void {
        this.bindParamStrArray(paramStrArray);
    }

    // 绑定参数StrArray
    public bindParamStrArray(paramStrArray: Array<string>): void {
        if (paramStrArray == null) return;
        this.paramStrArray = paramStrArray;
        this.refresh();
    }
}

// 全局声明和定义
declare global {
    interface Window {
        languageName: string;
        getLocalizedStr: (str: string) => string;
        refreshAllLocalizedComp: () => void;
    }
}

window.getLocalizedStr = (key: string) => {
    const cocos = cc as any;

    if (CC_EDITOR) {
        let defaultName = cocos.Jou_i18n.defaultLanguageName;
        let language = cocos.Jou_i18n[defaultName];
        if (language != null && language[key] != null) return language[key];
    }
    else {
        if (window.languageName != null && window.languageName !== "") {
            let language = cocos.Jou_i18n[window.languageName];
            if (language[key] != null) return language[key];
        }
        else console.error("LanguageName is null, please set it first.");
    }

    return key;
};

window.refreshAllLocalizedComp = () => {
    let rootNode = cc.director.getScene();
    for (let i = 0, t = rootNode.childrenCount; i < t; i++) {
        let curNode = rootNode.children[i];
        let labelArray = curNode.getComponentsInChildren("LocalizedLabel") as LocalizedLabel[];
        for (let j = 0, tt = labelArray.length; j < tt; j++) labelArray[j].refresh();
        let spriteArray = curNode.getComponentsInChildren("LocalizedSprite") as LocalizedSprite[];
        for (let j = 0, tt = spriteArray.length; j < tt; j++) spriteArray[j].refresh();
    }
}