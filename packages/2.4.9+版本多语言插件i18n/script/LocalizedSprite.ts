const { ccclass, property, executeInEditMode, menu } = cc._decorator;

@ccclass("LocalizedSpriteData")
export class LocalizedSpriteData {
    @property(cc.String)
    public language: string = "";
    @property(cc.SpriteFrame)
    public spriteFrame: cc.SpriteFrame = null;
}

@ccclass
@executeInEditMode
@menu("i18n/LocalizedSprite")
export class LocalizedSprite extends cc.Component {

    @property([LocalizedSpriteData])
    private spriteDataArray: Array<LocalizedSpriteData> = new Array<LocalizedSpriteData>();

    private curSprite: cc.Sprite = null;

    onLoad() {
        this.curSprite = this.getComponent(cc.Sprite);
        if (this.curSprite == null) {
            cc.error("There is no cc.Sprite on LocalizedSprite, nodeName: " + this.node.name);
            this.enabled = false;
        }

        // 根据当前已有的语言，自动填充LocalizedSpriteDataArray
        if (CC_EDITOR && this.spriteDataArray.length === 0) {
            const cocos = cc as any;
            if (cocos.Jou_i18n !== null) {
                let nameArray = Object.keys(cocos.Jou_i18n);
                for (let i = 0, t = nameArray.length; i < t; i++) {
                    let name = nameArray[i];
                    if (name === "defaultLanguageName") continue;
                    let data = new LocalizedSpriteData();
                    data.language = nameArray[i];
                    this.spriteDataArray.push(data);
                }
            }
        }
    }

    onEnable() {
        this.refresh();
    }

    // 刷新cc.Sprite
    public refresh(): void {
        if (this.curSprite == null) return;

        const cocos = cc as any;
        let targetName: string;
        if (CC_EDITOR) targetName = cocos.Jou_i18n.defaultLanguageName;
        else targetName = window.languageName;

        for (let i = 0, t = this.spriteDataArray.length; i < t; i++) {
            let spriteData = this.spriteDataArray[i];
            if (spriteData.language === targetName) {
                this.curSprite.spriteFrame = spriteData.spriteFrame;
                return;
            }
        }

        this.curSprite.spriteFrame = null;
    }
}