Editor.Panel.extend({
  style: `
    :host { margin: 5px; }
  `,

  template: `
    <div class="layout horizontal">
      <div>
        <ui-button class="transparent" style="width:25px;" id="addBtn">
          <i class="icon-plus"></i>
        </ui-button>
      </div>
      <div class="flex-1">
        <ui-input id="nameInput" style="left:5px;" class="Big" placeholder="Name of new language..."></ui-input>
      </div>
      <div>
        <ui-button class="transparent" style="width:25px;" id="refreshBtn">
          <i class="icon-arrows-cw"></i>
        </ui-button>
      </div>
    </div>
    <hr></hr>
    <ui-box-container class="shadow" id="container">

    </ui-box-container>
  `,

  $: {
    addBtn: '#addBtn',
    nameInput: '#nameInput',
    refreshBtn: '#refreshBtn',
    container: '#container',
  },

  ready() {
    // 新建语言按钮事件
    this.$addBtn.addEventListener('confirm', () => {

      let languageName = this.$nameInput.value;

      // 语言名称命名规范检查
      if (!/[a-zA-Z]/.test(languageName)) {
        Editor.warn("语言名称只允许使用 a-z A-Z,\"" + languageName + "\"不合法!");
        return;
      }

      Editor.Ipc.sendToMain('joui18n:add-new-language', languageName);
    });

    // 刷新按钮事件
    this.$refreshBtn.addEventListener('confirm', () => {
      this.clearAllCheckBox();
      Editor.Ipc.sendToMain('joui18n:get-cur-language');
    });


    // 获取本地已经有的语言
    Editor.Ipc.sendToMain('joui18n:get-cur-language');
  },

  // 清空所有item
  clearAllCheckBox() {
    if (this.checkBoxMap == null) return;

    // 清除所有
    this.checkBoxMap.forEach((checkBox, key) => {
      let parent = checkBox.parentNode;
      let grandParent = parent.parentNode;
      parent.removeChild(checkBox);
      grandParent.removeChild(parent);
    });
  },

  // 添加新item
  addNewLanguage(name) {
    if (this.checkBoxMap == null) this.checkBoxMap = new Map();

    let newProp = document.createElement("ui-prop");
    newProp.setAttribute("name", name);
    let newCheckBox = document.createElement("ui-checkbox");
    newCheckBox.addEventListener('change', () => {
      // 如果该checkBox已经被选中了，就点了之后啥反应没有
      if (!newCheckBox.value) {
        newCheckBox.value = true;
        return;
      }

      // 如果没被选中，就取消选中其他所有的，然后选中当前的
      let self = this;
      this.checkBoxMap.forEach(function (checkBox, key) {
        if (checkBox === newCheckBox) {
          self.defaultLanguageName = key;
          checkBox.value = true;
        }
        else checkBox.value = false;
      });

      // 发消息给场景脚本，让它刷新场景内的本地化组件
      Editor.Scene.callSceneScript("joui18n", "refresh-component", this.defaultLanguageName);

      // 保存默认语言名称到setting.json
      Editor.Ipc.sendToMain("joui18n:set-defualt-language-name", this.defaultLanguageName);
    });
    newProp.appendChild(newCheckBox);
    this.$container.appendChild(newProp);
    this.checkBoxMap.set(name, newCheckBox);
  },

  messages: {
    'add-new-language-success'(event, name) {
      this.addNewLanguage(name);
      // 如果新添加的语言是唯一的一个语言，就把它设为默认语言
      if (this.checkBoxMap.size === 1) {
        this.checkBoxMap.forEach(function (checkBox, key) { checkBox.value = true; })

        this.defaultLanguageName = name;
        // 保存默认语言名称到setting.json
        Editor.Ipc.sendToMain("joui18n:set-defualt-language-name", name);

        // 刷新一下场景内的本地化组件
        Editor.Scene.callSceneScript("joui18n", "refresh-component", name);
      }
    },

    'refresh-language'(event, nameArray) {
      for (let i = 0, t = nameArray.length; i < t; i++) this.addNewLanguage(nameArray[i]);

      // 获取默认语言名称
      this.defaultLanguageName = "";
      Editor.Ipc.sendToMain('joui18n:get-default-language-name');
    },

    'after-get-default-language-name'(event, name) {
      if (name != null && name !== "") {
        this.defaultLanguageName = name;

        if (this.checkBoxMap) {
          let self = this;
          this.checkBoxMap.forEach(function (checkBox, key) {
            if (key === self.defaultLanguageName) checkBox.value = true;
          });
        }
        // 刷新一下场景内的本地化组件
        Editor.Scene.callSceneScript("joui18n", "refresh-component", this.defaultLanguageName);
      }
    }
  }
});