'use strict';
const resourcesUrl = "db://assets/resources";
const i18nUrl = resourcesUrl + "/i18n";

const defaultScript = "export const language = {\n\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.{name} = language;";

module.exports = {
  defaultLanguageName: "",

  load() {
    // 检查一下setting.json
    let fs = require("fs");
    let self = this;
    fs.readFile(Editor.url("packages://joui18n/setting.json"), "utf8", function (err, dataStr) {
      if (err) {
        Editor.error("Read setting.json failed, err: " + err);
        return;
      }

      let jsonObject = JSON.parse(dataStr);

      // 看看有没有默认语言
      if (jsonObject.defaultLanguageName) self.defaultLanguageName = jsonObject.defaultLanguageName;
    })
  },

  unload() { },

  checkFilePath() {
    // 先看有没有resources文件夹
    if (!Editor.assetdb.exists(resourcesUrl)) Editor.assetdb.create(resourcesUrl);

    // 再看看有没有i18n文件夹
    if (!Editor.assetdb.exists(i18nUrl)) Editor.assetdb.create(i18nUrl);
  },

  messages: {
    'open-setting-panel'() {
      Editor.Panel.open('joui18n');
    },

    'scene:ready'() {
      if (this.defaultLanguageName !== "") Editor.Scene.callSceneScript("joui18n", "refresh-component", this.defaultLanguageName);
    },

    // 添加新语言
    'add-new-language'(event, languageName) {
      // 先检查文件路径是否存在
      this.checkFilePath();

      // 检查有没有重名的文件
      let fileName = languageName;
      let index = 0;
      while (Editor.assetdb.exists(i18nUrl + "/" + fileName + ".ts")) {
        index++;
        fileName = languageName + index;
      }

      // 填充语言
      let data = defaultScript.replace("{name}", fileName);

      // 创建语言文件
      Editor.assetdb.create(i18nUrl + "/" + fileName + ".ts", data, function (err, results) {
        if (err) {
          Editor.error("Create new language file failed, err: " + err);
          return;
        }
        Editor.Ipc.sendToPanel("joui18n", "add-new-language-success", fileName);
      });
    },

    // 获取当前已经存在的语言
    'get-cur-language'() {
      Editor.assetdb.queryAssets(i18nUrl + "/*", 'typescript', function (err, results) {
        if (err) {
          Editor.error("Load language file failed, err: " + err);
          return;
        }
        let nameArray = [];
        for (let i = 0, t = results.length; i < t; i++) {
          let result = results[i];
          let url = result.url;
          let tempName = "";
          for (let i = url.length - 1; i >= 0; i--) {
            let char = url[i];
            if (char === "/") break;
            tempName += char;
          }
          let realName = "";
          for (let i = tempName.length - 1; i > 2; i--) realName += tempName[i];
          nameArray.push(realName);
        }

        // 发送给面板已存在的语言名称
        Editor.Ipc.sendToPanel("joui18n", "refresh-language", nameArray);
      })
    },

    // 获取默认语言
    'get-default-language-name'() {
      Editor.Ipc.sendToPanel("joui18n", "after-get-default-language-name", this.defaultLanguageName);
    },

    // 设置默认语言
    'set-defualt-language-name'(event, name) {
      this.defaultLanguageName = name;
      // 先读取setting.json，然后写入defaultLanguageName，然后覆盖文件
      let fs = require("fs");
      fs.readFile(Editor.url("packages://joui18n/setting.json"), "utf8", function (err, dataStr) {
        if (err) {
          Editor.err("Read setting.json failed, err: " + err);
          return;
        }

        let jsonObject = JSON.parse(dataStr);
        jsonObject.defaultLanguageName = name;

        fs.writeFile(Editor.url("packages://joui18n/setting.json"), JSON.stringify(jsonObject), "utf8", function (err) {
          if (err) {
            Editor.err("Write setting.json failed, err: " + err);
            return;
          }
        });
      })
    }
  },
};