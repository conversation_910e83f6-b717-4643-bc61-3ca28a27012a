* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0 12px;
  background-color: #454545;
  color: #bdbdbd;
  user-select: none;
}

#app {
  width: 100%;
  height: 100%;
}

/* 标题 */
.title {
  font-size: 20px;
  font-weight: 800;
  padding: 10px 0;
}

/* 属性容器 */
.properties {
  overflow: visible;
}

/* 应用按钮 */
.apply-btn {
  min-width: 20px;
  height: 33px;
  background-image: linear-gradient(#4281b6, #4281b6);
  border: 1px solid #171717;
  border-radius: 3px;
  color: #fff;
  font-size: 16px;
  font-weight: 800;
  text-align: center;
  outline: none;
  overflow: hidden;
  cursor: pointer;
}

.apply-btn:hover {
  background-image: none;
  background-color: #4c87b6;
}

.apply-btn:active {
  background-image: none;
  background-color: #2e6da2;
  border-color: #fd942b;
  color: #cdcdcd;
  box-shadow: 1px 1px 10px #262626 inset;
}

[v-cloak] {
  display: none;
}
