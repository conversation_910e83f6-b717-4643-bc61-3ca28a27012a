#app {
  width: 100%;
  height: 100%;
  background-color: #454545;
  box-sizing: border-box;
  padding: 10px;
  color: #bdbdbd;
  animation: fade-in 0.3s;
  animation-fill-mode: forwards;
}

/* 渐显 */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 主布局 */
.layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 预览区 */
.view {
  width: 100%;
  min-height: 0;
  background-color: #4c4c4c;
  position: relative;
  flex: 1;
}

/* 画布 */
.canvas {
  width: 100%;
  height: 100%;
  background-color: #4c4c4c;
}

/* 版本号 */
.version {
  height: 16px;
  opacity: 0.3;
  position: absolute;
  top: 4px;
  right: 6px;
  line-height: 16px;
  font-size: 11px;
  color: #bdbdbd;
}

/* 按钮容器 */
.buttons {
  height: 16px;
  opacity: 0.5;
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  flex-direction: row;
}

/* 按钮容器 > 子元素 */
.buttons > * {
  margin-right: 5px;
}

/* 信息,按钮 */
.info,
.button {
  width: 16px;
  height: 16px;
  color: #bdbdbd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 信息,按钮:虚指 */
.info:hover,
.button:hover {
  color: #09f;
}

/* 按钮:激活 */
.button:active {
  color: #fd942b;
}

/* 信息,按钮 图标 */
.info .icon,
.button .icon {
  width: 13px;
  height: 13px;
}

/* 位置 */
.position {
  height: 13px;
  opacity: 0.5;
  position: absolute;
  bottom: 4px;
  right: 4px;
  display: flex;
  flex-direction: row;
}

/* 位置 文本 */
.position .label {
  margin-right: 2px;
  line-height: 12px;
  font-size: 11px;
}

/* 位置 按钮 */
.position .button {
  width: 13px;
  height: 13px;
}

/* 位置 按钮 图标 */
.position .button .icon {
  width: 12px;
  height: 12px;
}

/* 颜色 */
.color {
  height: 14px;
  opacity: 1;
  position: absolute;
  bottom: 4px;
  left: 4px;
  display: flex;
  flex-direction: row;
}

/* 颜色 子元素 */
.color * {
  width: 14px !important;
  height: 14px !important;
}

/* CSS @media 在 Shadow DOM 中无法得到期望结果 */

/* .layout {
  flex-direction: column;
}

.properties {
  width: 100%;
  margin-top: 5px;
  margin-left: 0;
  display: flex;
} */

/* @media screen and (min-width: 800px) {
  @media screen and (min-width: 350px) {
    .layout {
      flex-direction: row;
    }
    .properties {
      width: 265px;
      margin-top: 0;
      margin-left: 5px;
      display: flex;
    }
  }
  @media screen and (max-width: 349px) {
    .properties {
      display: none;
    }
  }
}

@media screen and (max-height: 360px) {
  @media screen and (min-width: 350px) {
    .layout {
      flex-direction: row;
    }
    .properties {
      width: 265px;
      margin-top: 0;
      margin-left: 5px;
      display: flex;
    }
  }
  @media screen and (max-width: 349px) {
    .properties {
      display: none;
    }
  }
} */
