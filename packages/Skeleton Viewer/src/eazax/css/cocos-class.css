/* 
  Cocos Creator 风格样式
  版本: 20210911
  作者: 陈皮皮 (ifaswind)
  主页: https://gitee.com/ifaswind
  公众号: 菜鸟小栈
*/

/* 属性容器 */
.properties {
  width: 100%;
  border: 1px solid #666;
  border-radius: 3px;
  padding: 5px;
  box-sizing: border-box;
  outline: 0;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.properties > * {
  margin: 2px 0;
}

.properties:first-child {
  margin-top: 0;
}

.properties:last-child {
  margin-bottom: 0;
}

/* 属性 */
.property {
  width: 100%;
  min-height: 23px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
}

/* 属性标签 */
.property > .label {
  width: 38%;
  min-width: 70px;
  position: relative;
  margin-left: 5px;
  line-height: 23px;
  font-size: 12px;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex-shrink: 0;
  align-items: baseline;
  justify-content: flex-start;
}

/* 属性标签文本:虚指 */
.property:hover > .label > .text {
  color: #09f;
}

/* 属性标签文本:聚焦内部 */
.property:focus-within > .label > .text {
  color: #fd942b;
}

/* 属性标签内容:聚焦内部 */
.property:focus-within > .content > * {
  border-color: #fd942b;
}

/* tooltip */
.tooltip {
  background-color: #333333;
  padding: 5px 8px;
  border: 1px solid #646464;
  border-radius: 4px;
  position: absolute;
  top: -38px;
  left: -5px;
  visibility: hidden;
  text-align: center;
  z-index: 2;
}

/* tooltip 三角形 */
.tooltip::before,
.tooltip::after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  position: absolute;
  left: 10px;
  transform: rotate(-90deg);
}

/* tooltip 三角形 */
.tooltip::before {
  border-right: 6px solid #333333;
  top: 100%;
}

/* tooltip 三角形边框 */
.tooltip::after {
  border-right: 6px solid #646464;
  top: calc(100% + 1px);
  z-index: -1;
}

/* 前一个元素虚指时的 tooltip */
*:hover + .tooltip {
  visibility: visible;
}

/* 属性内容 */
.property > .content {
  display: flex;
  flex: 1;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
}

.property > .content > * {
  width: auto;
  min-width: 20px;
  height: 21px;
  flex: 1;
}

.property > .content > *:focus {
  border-color: #fd942b;
}

/* 提示 */
.tip {
  width: 100%;
  min-height: 45px;
  background: #333;
  border: 1px solid #666;
  border-radius: 3px;
  padding: 12px 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: left;
  color: #bdbdbd;
  line-height: 17px;
  font-size: 13px;
  white-space: pre-line;
}

.tip > * {
  display: inline-block;
}

/* *::滚动条 */
*::-webkit-scrollbar {
  width: 11px;
}

/* *::滚动条-按钮 */
*::-webkit-scrollbar-button {
  display: none;
}

/* *::滚动条-横竖交汇处 */
*::-webkit-scrollbar-corner {
  display: none;
}

/* *::滚动条-轨道 */
*::-webkit-scrollbar-track {
  /* background: rgba(0, 0, 0, 0.5); */
  background: none !important;
  background-clip: content-box;
  border: 5px solid transparent;
}

/* *::滚动条-滑块 */
*::-webkit-scrollbar-thumb {
  background: #7d7d7d;
  background-clip: content-box;
  border: 4px solid transparent;
  border-radius: 6px;
}

/* *::滚动条-滑块:虚指 */
*::-webkit-scrollbar-thumb:hover {
  background-color: #fd942b;
  border: 3px solid transparent;
}
