# Skeleton Viewer

## Introduction

[Cocos Creator Editor Extension]

**Used to preview skeleton animation. Provide a stand-alone panel and can also be attached to the editor window.**

*Currently, only versions 3.5~4.0 of Spine resources are supported. DragonBone resources would be supported in the future.*



## Open Source

This extension is an open source project, here is the git repository: [https://gitee.com/ifaswind/ccc-skeleton-viewer](https://gitee.com/ifaswind/ccc-skeleton-viewer)

If you like this project, don't forget to star [![star](https://gitee.com/ifaswind/ccc-skeleton-viewer/badge/star.svg?theme=dark)](https://gitee.com/ifaswind/ccc-skeleton-viewer/stargazers)!

*If you have any usage problems, just create an issue on Gitee or add my WeChat `im_chenpipi` and leave a message.*



## Screenshots

![screenshot-1](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-1.png)

![screenshot-2](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-2.png)

![screenshot-3](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-3.png)

![screenshot-4](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-4.png)

![screenshot-5](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-5.png)

![screenshot-6](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-6.png)

![screenshot-7](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-7.png)

![screenshot-8](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/screenshot-8.png)

![setting-panel](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/setting-panel.png)



## Environment

Platform: Windows、macOS

Engine: Cocos Creator 2.x



## Download & Installation

### Install from Cocos Store

You can find this extension in Cocos Store now, click on *Extension -> Cocos Store* option to open the Cocos Store.

Enter "**Skeleton Viewer**" in the search bar, find it and then install it.

![cocos-store](https://gitee.com/ifaswind/image-storage/raw/master/repositories/ccc-skeleton-viewer/cocos-store.png)

*Skeleton Viewer：[http://store.cocos.com/app/detail/3008](http://store.cocos.com/app/detail/3008)*



### Download from git repository

Click [here](https://gitee.com/ifaswind/ccc-skeleton-viewer/releases) or go to the release panel, download the latest version package of this extension.

And then unzip the package:

- Windows: Unzip to `C:\Users\<USER>\.CocosCreator\packages\`
- macOS: Unzip to `~/.CocosCreator/packages/`

For example, on my computer, the full path of `package.json` file should be like:

- Windows: `C:\Users\<USER>\.CocosCreator\packages\ccc-skeleton-viewer\package.json`
- macOS: `/Users/<USER>/.CocosCreator/packages/ccc-skeleton-viewer/package.json`



## Usage

## View

Click on *Extension -> Skeleton Viewer -> View* option to open the view panel.

*To be added...*



### Settings

Click on *Extension -> Skeleton Viewer -> Settings* option to open the setting panel.

In the setting panel, you can choose a hotkey(shortcut, for opening the view panel quickly) in preset list, or customize one by yourself.

One thing you should know, not every keys/keys-combinations can be used, because some keys/keys-combinations have been used by the system or Cocos Creator.

*Accelerator reference: [https://www.electronjs.org/docs/api/accelerator](https://www.electronjs.org/docs/api/accelerator)*

🥳 Enjoy!



## Change log

[Releases](https://gitee.com/ifaswind/ccc-skeleton-viewer/releases)



## Dependencies

- [cocos-creator](https://github.com/cocos-creator)
- [electron](https://github.com/electron/electron)
- [vue](https://github.com/vuejs/vue)
- [node-fetch](https://github.com/node-fetch/node-fetch)
- [spine-runtimes](https://github.com/EsotericSoftware/spine-runtimes)



## License

This project is licensed under the [MIT license](https://opensource.org/licenses/MIT).



---



# 公众号

## 菜鸟小栈

😺 我是陈皮皮，一个还在不断学习的游戏开发者，一个热爱分享的 Cocos Star Writer。

🎨 这是我的个人公众号，专注但不仅限于游戏开发和前端技术分享。

💖 每一篇原创都非常用心，你的关注就是我原创的动力！

> Input and output.

![](https://gitee.com/ifaswind/image-storage/raw/master/weixin/official-account.png)