module.exports = {
    'name': 'Skeleton Viewer',
    'view': 'View',
    'settings': 'Settings',
    'checkUpdate': 'Check Update',
    // update
    'currentLatest': 'Currently the latest version!',
    'hasNewVersion': 'New version found!',
    'localVersion': 'Local version: ',
    'latestVersion': 'Latest version: ',
    'releases': 'Releases: https://gitee.com/ifaswind/ccc-skeleton-viewer/releases',
    'cocosStore': 'Cocos Store: http://store.cocos.com/app/detail/3008',
    // main
    'skeletonAssets': 'Spine Assets',
    'selectAssets': 'Select skeleton assets',
    'noAssets': 'No assets selected',
    'noSkeleton': 'No skeleton asset selected!',
    'noTexture': 'No texture assets found!',
    'noAtlas': 'No atlas assets found!',
    // view
    'skin': 'Skin',
    'animation': 'Animation',
    'viewScale': 'View Scale',
    'loop': 'Loop',
    'premultipliedAlpha': 'Premultiplied Alpha',
    'timeScale': 'Time Scale',
    'drawBones': 'Draw Bones',
    'drawBoundingBoxes': 'Draw Bounding Boxes',
    'drawMeshTriangles': 'Draw Mesh Triangles',
    'drawPaths': 'Draw Paths',
    'spineRuntime': 'Spine Runtime',
    'version': 'Version',
    'noVersion': 'Cannot identify the version of the asset!',
    'noSpineRuntime': 'Target version of spine runtime is missing!',
    'noWebGL': 'WebGL is unavailable!',
    'noSkeletonData': 'Lack of skeleton data!',
    'dataMismatch': 'Data mismatch! Please check the selection!',
    'reset': 'Reset',
    'reposition': 'Reposition',
    'canvasColor': 'Canvas Color',
    // settings
    'none': 'None',
    'selectKey': 'Hotkey',
    'selectKeyTooltip': 'Choose a hotkey',
    'customKey': 'Custom',
    'customKeyPlaceholder': 'Choose a hotkey above or customize one by yourself',
    'customKeyTooltip': 'You can also customize your own hotkey',
    'autoCheck': 'Auto Check Update',
    'autoCheckTooltip': 'Check if there is a new version when the extension is loaded',
    'reference': '· Hotkey customization reference: ',
    'accelerator': 'Keyboard Shortcuts',
    'repository': '· Git repository of this extension: ',
    'apply': 'Apply',
    'quoteError': 'Do not use double quotes!',
    'customKeyError': 'Please specify a hotkey!',
};
