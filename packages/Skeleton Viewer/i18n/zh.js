module.exports = {
    'name': '骨骼查看器',
    'view': '预览',
    'settings': '设置',
    'checkUpdate': '检查更新',
    // update
    'currentLatest': '当前已是最新版本！',
    'hasNewVersion': '发现新版本！',
    'localVersion': '本地版本：',
    'latestVersion': '最新版本：',
    'releases': '发行版：https://gitee.com/ifaswind/ccc-skeleton-viewer/releases',
    'cocosStore': 'Cocos 商店：http://store.cocos.com/app/detail/3008',
    // main
    'skeletonAssets': 'Spine 资源',
    'selectAssets': '选择骨骼动画资源',
    'noAssets': '未选择资源',
    'noSkeleton': '未选择任何骨骼资源！',
    'noTexture': '未找到纹理资源！',
    'noAtlas': '未找到图集资源！',
    // view
    'skin': '皮肤',
    'animation': '动画',
    'viewScale': '预览缩放',
    'loop': '循环',
    'premultipliedAlpha': '预乘',
    'timeScale': '时间缩放',
    'drawBones': '绘制骨骼',
    'drawBoundingBoxes': '绘制包围盒',
    'drawMeshTriangles': '绘制网格三角形',
    'drawPaths': '绘制路径',
    'spineRuntime': 'Spine Runtime',
    'version': '版本',
    'noVersion': '无法识别资源版本！',
    'noSpineRuntime': '缺少对应版本的 Spine 运行时！',
    'noWebGL': 'WebGL 不可用！',
    'noSkeletonData': '缺少骨骼数据！',
    'dataMismatch': '资源数据不匹配！请检查当前选中的资源！',
    'reset': '重置',
    'reposition': '复位',
    'canvasColor': '画布颜色',
    // settings
    'none': '无',
    'selectKey': '快捷键',
    'selectKeyTooltip': '选择一个快捷键',
    'customKey': '自定义',
    'customKeyPlaceholder': '在上方选择一个快捷键或自定义一个快捷键',
    'customKeyTooltip': '自定义快捷键',
    'autoCheck': '自动检查更新',
    'autoCheckTooltip': '扩展启动时自动检查是否有新版本',
    'reference': '· 快捷键自定义请参考：',
    'accelerator': '键盘快捷键',
    'repository': '· 本扩展的 Git 仓库：',
    'apply': '应用',
    'quoteError': '请勿使用双引号！',
    'customKeyError': '请指定一个快捷键！',
};
