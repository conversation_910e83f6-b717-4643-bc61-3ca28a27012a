8. 断线重连
WebSocket消息类型："EnterRoom"
玩家断线重连，获取当前游戏状态。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
lastRoomID	-	是	Number	上一次房间ID（从登录响应中获取）
响应参数
基础房间信息
参数名	参数值	是否必填	参数类型	描述说明
roomId	-	是	Number	房间ID
roomType	1/2	是	Number	房间类型。1（匹配场）、2（房间场）
fee	0/500/1000	是	Number	房间费用
users	-	是	Array	房间内所有玩家，参考SeabattleRoomUser结构
gameStatus	1/2/3/4	是	Number	房间游戏状态。1（等待中）、2（布置战舰阶段）、3（战斗阶段）、4（游戏结束）
countDown	-	是	Number	房间游戏状态倒计时（秒）
游戏状态信息（根据gameStatus提供）
参数名	参数值	是否必填	参数类型	描述说明
deploymentStatus	-	否	Object	玩家部署状态（部署阶段和战斗阶段提供），key为userId，value为boolean
playerShips	-	否	Array	当前玩家的战舰部署（部署阶段和战斗阶段提供，只发送给玩家自己），参考ShipWithAnchor结构
attackHistory	-	否	Array	攻击历史记录（战斗阶段提供），参考AttackHistoryItem结构
currentAttacker	-	否	String	当前攻击者ID（战斗阶段提供）
remainingAttacks	1-3	否	Number	剩余攻击次数（战斗阶段提供）
playerStats	-	否	Object	玩家统计数据（战斗阶段提供，包含托管状态），key为userId，value为PlayerStats结构


攻击历史记录 (AttackHistoryItem)
参数名	参数值	是否必填	参数类型	描述说明
attackerId	-	是	String	攻击者ID
x	0-9	是	Number	攻击坐标X
y	0-9	是	Number	攻击坐标Y
hit	true/false	是	Boolean	是否命中
sunk	true/false	是	Boolean	是否击沉（仅在hit为true时有意义）
shipId	1月6日	否	Number	击中的战舰ID（仅当hit为true时存在）
anchorX	0-9	否	Number	被击沉战舰的锚点X坐标（仅击沉时有效）
anchorY	0-9	否	Number	被击沉战舰的锚点Y坐标（仅击沉时有效）
direction	0-3	否	Number	被击沉战舰的方向（仅击沉时有效）
  




30.玩家金币变化通知
WebSocket消息类型："NoticeUserCoin"
连接类型：服务器主动推送
当玩家金币发生变化时（如购买商品后），服务器会主动推送最新的金币数量。
请求参数
无（服务器主动推送）
响应参数
参数名	参数值	是否必填	参数类型	描述说明
coin	-	是	Number	玩家当前金币数量

NoticeAttack
连接类型：服务器主动推送
当房间内任何玩家发起攻击时，服务器会向房间内所有玩家广播攻击结果。攻击者和被攻击者都会收到相同的攻击通知。
请求参数
无（服务器主动推送）
响应参数
参数名	参数值	是否必填	参数类型	描述说明
userId	-	是	String	攻击者ID
x	0-9	是	Number	攻击坐标X
y	0-9	是	Number	攻击坐标Y
hit	true/false	是	Boolean	是否命中敌方战舰
sunk	true/false	是	Boolean	是否击沉敌方战舰（仅在hit为true时有效）
shipId	1月6日	否	Number	击中的战舰ID（仅当hit为true时存在）
anchorX	0-9	否	Number	被击沉战舰的锚点X坐标（仅击沉时有效）
anchorY	0-9	否	Number	被击沉战舰的锚点Y坐标（仅击沉时有效）
direction	0-3	否	Number	被击沉战舰的方向（仅击沉时有效）
nextTurn	-	是	String	下一回合玩家ID

11. 布置战舰
WebSocket消息类型："DeployShips"
类型：DeployShips
玩家布置战舰。
请求参数：
Body 请求参数
参数名	参数值	是否必填	参数类型	描述说明
shipPositions	-	是	Array	战舰位置列表，固定包含6艘战舰
shipPositions 数组元素结构
参数名	参数值	是否必填	参数类型	描述说明
id	1-6	是	Number	战舰ID。1（航母2×5）、2（战列舰1×5）、3（重巡1×4）、4（轻巡1×3）、5（驱逐舰1×2）、6（驱逐舰1×2）
gridX	0-9	是	Number	坐标X轴（锚点坐标，范围0-9）
gridY	0-9	是	Number	坐标Y轴（锚点坐标，范围0-9）
direction	0-3	是	Number	方向。0（向上）、1（向右）、2（向下）、3（向左）

响应：
个人响应（只发给部署玩家）
参数名	参数值	是否必填	参数类型	描述说明
success	true/false	是	Boolean	是否成功
shipPositions	-	是	Array	当前玩家的所有战舰数据（包含锚点），固定6艘
shipPositions 数组元素结构
参数名	参数值	是否必填	参数类型	描述说明
id	1月6日	是	Number	战舰ID
gridX	0-9	是	Number	起始X坐标（逻辑坐标）
gridY	0-9	是	Number	起始Y坐标（逻辑坐标）
direction	0-3	是	Number	方向（0-向上，1-向右，2-向下，3-向左）
anchorX	0-9	是	Number	锚点X坐标（渲染坐标）
anchorY	0-9	是	Number	锚点Y坐标（渲染坐标）

13. 准备就绪
13.1 部署完成通知
WebSocket消息类型："NoticePlayerReady"
玩家确认战舰部署完成并准备开始游戏。此接口与布置战舰接口分离，玩家需要在部署完战舰后主动调用此接口表示准备。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
ready	TRUE	是	Boolean	准备状态（固定为true）
响应参数
个人响应（只发给请求玩家）
无特定响应数据，成功后会广播准备通知。
广播通知（发给房间内所有玩家）
WebSocket消息类型："NoticePlayerReady"（服务器主动推送）
参数名	参数值	是否必填	参数类型	描述说明
userId	-	是	String	准备完成的用户ID
ready	TRUE	是	Boolean	是否准备完成（固定为true）
准备条件
1. 必须已部署战舰：玩家必须先调用 DeployShips 接口部署战舰
2. 必须在部署阶段：只能在游戏状态为部署阶段时调用
3. 必须在房间中：玩家必须已加入游戏房间并坐下

皮肤变更广播
当玩家成功设置皮肤后，服务器会自动向房间内所有其他玩家广播皮肤变更通知。
广播消息类型："NoticeSkinChange"
广播参数：
参数名	参数值	是否必填	参数类型	描述说明
userId	-	是	String	设置皮肤的玩家ID
skinId	2100-2102	是	Number	新设置的皮肤ID



托管状态管理接口
36.取消托管
WebSocket消息类型："CancelAutoManaged"
玩家主动取消当前的托管状态。只有处于托管状态的玩家才能调用此接口。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
-	-	-	-	无需参数
响应参数
参数名	参数值	是否必填	参数类型	描述说明
success	true/false	是	Boolean	是否成功取消托管
message	-	是	String	响应消息
37.托管状态变更广播
WebSocket消息类型："NoticeAutoManagedChange"
当房间内任何玩家的托管状态发生变更时，服务器会向房间内所有玩家广播此消息。
广播参数
参数名	参数值	是否必填	参数类型	描述说明
userId	-	是	String	状态变更的玩家ID
nickName	-	是	String	玩家昵称
isAutoManaged	true/false	是	Boolean	新的托管状态
reason	-	是	String	状态变更原因
状态变更原因说明
原因	描述
超时进入托管	玩家攻击超时1次后自动进入托管
主动攻击取消托管	玩家主动攻击时自动取消托管
玩家主动取消托管	玩家通过取消托管接口主动取消
38.托管状态查询
托管状态信息已集成到 "EnterRoom" 接口中，无需单独查询。
EnterRoom响应中的托管状态信息
在 "EnterRoom" 接口的响应中，每个用户对象都包含托管状态信息：
托管机制说明
进入托管的条件
- 超时托管：玩家攻击超时1次后自动进入托管状态
- 不区分离线/在线：无论玩家是否离线，只要超时就会进入托管
取消托管的方式
1. 主动攻击：玩家进行攻击操作时自动取消托管状态【后期可删】--还是留着吧
2. 手动取消：通过 "CancelAutoManaged" 接口主动取消
托管状态下的行为
- 系统会在3-5秒延迟后自动执行随机攻击
- 玩家仍可以主动操作，操作后自动取消托管状态【后期可删】
- 托管状态会实时广播给房间内所有玩家